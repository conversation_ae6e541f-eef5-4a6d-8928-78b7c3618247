# pipeline 分支


用来存放pipeline定义


## 解决项目引用，vscode飘黄线问题

安装uv, https://docs.astral.sh/uv/getting-started/installation/

```
uv venv --python 3.12.9
source .venv/bin/activate
uv pip install -r requirements.txt  # 报utf-8编码错误，copy内容并用vim重新创建一个requirements.txt
echo 'PYTHONPATH="${workspaceFolder}/plugins:${env:PYTHONPATH}"' > .env
```
另外.env和.vscode/setting.json两个文件是帮助在本地开发时vscode寻找plugins下面的包依赖的

由于docker compose启动airflow时也需要.env加载环境变量，和本地开发环境有冲突，目前把.env文件加入.gitignore了

airflow会自动寻找plugins下面的依赖，vscode不会。


requirements.txt除了airflow本身，还安装了一些提供商插件，
具体可以查看页面https://airflow.apache.org/docs/。

例如airflow.providers.postgres这个模块airflow本身是不包含的，需要运行下面的命令安装

```
pip install apache-airflow-providers-postgres
```
安装完成之后，vscode中会提示airflow.providers.postgres这个模块

另外书写相关插件时可以先查看文档看是否有提供商的官方插件
