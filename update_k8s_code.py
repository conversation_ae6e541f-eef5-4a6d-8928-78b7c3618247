import os
import hashlib
import shutil

def calculate_md5(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def sync_folders(src_folder, dst_folder):
    """同步两个文件夹，使目标文件夹与源文件夹保持一致"""
    # 首先处理源文件夹中的所有文件和子文件夹
    for root, _, files in os.walk(src_folder):
        if "__pycache__" in root:
            continue
        # 计算相对于源文件夹的相对路径
        relative_path = os.path.relpath(root, src_folder)
        dst_root = os.path.join(dst_folder, relative_path)

        # 确保目标文件夹中的对应目录存在
        if not os.path.exists(dst_root):
            os.makedirs(dst_root)

        # 处理文件
        for file in files:
            src_file = os.path.join(root, file)
            dst_file = os.path.join(dst_root, file)

            # 如果目标文件不存在，直接复制
            if not os.path.exists(dst_file):
                shutil.copy2(src_file, dst_file)
                print(f"Copied new file: {src_file} -> {dst_file}")
                continue

            # 如果目标文件存在，比较MD5
            src_md5 = calculate_md5(src_file)
            dst_md5 = calculate_md5(dst_file)

            if src_md5 != dst_md5:
                shutil.copy2(src_file, dst_file)
                print(f"Updated changed file: {src_file} -> {dst_file}")

    # 然后检查目标文件夹中是否有源文件夹不存在的文件或目录
    for root, _, files in os.walk(dst_folder, topdown=False):
        # 计算相对于目标文件夹的相对路径
        if "__pycache__" in root:
            continue
        relative_path = os.path.relpath(root, dst_folder)
        src_root = os.path.join(src_folder, relative_path)

        # 处理文件
        for file in files:
            dst_file = os.path.join(root, file)
            src_file = os.path.join(src_root, file)

            # 如果源文件不存在，删除目标文件
            if not os.path.exists(src_file):
                os.remove(dst_file)
                print(f"Removed file: {dst_file}")

        # 处理目录（只有在目录为空时才删除）
        if not os.path.exists(src_root):
            try:
                os.rmdir(root)
                print(f"Removed empty directory: {root}")
            except OSError:
                # 目录不为空，跳过
                pass

def main():
    srcs = [
        "/root/dags",
        "/root/plugins"
    ]
    dests = [
        "/opt/airflow/dags",
        "/opt/airflow/plugins"
    ]
    for src, dest in zip(srcs, dests):
        sync_folders(src, dest)
    shutil.copy2("/root/config/airflow_local_settings.py", "/opt/airflow/config/airflow_local_settings.py")
    shutil.copy2("/root/airflow.cfg", "/opt/airflow/airflow.cfg")
    if os.path.exists("/root/docker-compose.yaml"):
        shutil.copy2("/root/docker-compose.yaml", "/opt/airflow/docker-compose.yaml")


if __name__ == "__main__":
    main()
    # 设置源文件夹和目标文件夹
    # folder_a = "/tmp/dags"
    # folder_b = "/tmp/dags_new"

    # # 验证文件夹存在
    # if not os.path.isdir(folder_a):
    #     print(f"Error: Folder A '{folder_a}' does not exist!")
    #     exit(1)

    # if not os.path.isdir(folder_b):
    #     print(f"Error: Folder B '{folder_b}' does not exist!")
    #     exit(1)

    # print(f"\nSyncing {folder_b} to match {folder_a}...\n")
    # sync_folders(folder_a, folder_b)
    # print("\nSync completed!")
