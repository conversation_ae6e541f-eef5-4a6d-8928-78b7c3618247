aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.15.2
annotated-types==0.7.0
anyio==4.9.0
apache-airflow==2.10.0
apache-airflow-providers-cncf-kubernetes==10.4.0
apache-airflow-providers-common-compat==1.5.1
apache-airflow-providers-common-io==1.5.1
apache-airflow-providers-common-sql==1.24.0
apache-airflow-providers-fab==1.5.3
apache-airflow-providers-ftp==3.12.3
apache-airflow-providers-http==5.2.1
apache-airflow-providers-imap==3.8.3
apache-airflow-providers-postgres==6.1.1
apache-airflow-providers-redis==4.0.2
apache-airflow-providers-smtp==2.0.1
apache-airflow-providers-sqlite==4.0.1
apache-airflow-providers-ssh==4.0.1
apispec==6.8.1
argcomplete==3.6.1
asgiref==3.8.1
asyncpg==0.30.0
attrs==25.3.0
babel==2.17.0
bcrypt==4.3.0
blinker==1.9.0
cachelib==0.13.0
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
clickclick==20.10.2
colorama==0.4.6
colorlog==6.9.0
ConfigUpdater==3.2
connexion==2.14.2
cron-descriptor==1.4.5
croniter==6.0.0
cryptography==44.0.2
Deprecated==1.2.18
dill==0.3.9
dnspython==2.7.0
durationpy==0.9
email_validator==2.2.0
fastapi==0.115.12
Flask==2.2.5
Flask-AppBuilder==4.5.3
Flask-Babel==2.0.0
Flask-Caching==2.3.1
Flask-JWT-Extended==4.7.1
Flask-Limiter==3.12
Flask-Login==0.6.3
Flask-Session==0.5.0
Flask-SQLAlchemy==2.5.1
Flask-WTF==1.2.2
frozenlist==1.5.0
fsspec==2025.3.1
google-auth==2.38.0
google-re2==1.1.20240702
googleapis-common-protos==1.69.2
greenlet==3.1.1
grpcio==1.71.0
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
idna==3.10
importlib_metadata==8.6.1
inflection==0.5.1
itsdangerous==2.2.0
Jinja2==3.1.6
jmespath==1.0.1
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kubernetes==31.0.0
kubernetes_asyncio==30.3.1
lazy-object-proxy==1.10.0
limits==4.4.1
linkify-it-py==2.0.3
lockfile==0.12.2
Mako==1.3.9
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
marshmallow-oneofschema==3.1.1
marshmallow-sqlalchemy==0.28.2
mdit-py-plugins==0.4.2
mdurl==0.1.2
methodtools==0.4.7
more-itertools==10.6.0
multidict==6.2.0
oauthlib==3.2.2
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-grpc==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
ordered-set==4.1.0
packaging==24.2
paramiko==3.5.1
pathspec==0.12.1
pendulum==3.0.0
pluggy==1.5.0
prison==0.2.1
prometheus_client==0.21.1
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pycryptodome==3.22.0
pydantic==2.11.3
pydantic_core==2.33.1
Pygments==2.19.1
PyJWT==2.10.1
PyNaCl==1.5.0
python-daemon==3.1.2
python-dateutil==2.9.0.post0
python-nvd3==0.16.0
python-slugify==8.0.4
pytz==2025.2
PyYAML==6.0.2
redis==5.2.1
referencing==0.36.2
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rich==13.9.4
rich-argparse==1.7.0
rpds-py==0.24.0
rsa==4.9
setproctitle==1.3.5
setuptools==75.8.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==1.4.54
SQLAlchemy-JSONField==1.0.2
SQLAlchemy-Utils==0.41.2
sqlparse==0.5.3
sshtunnel==0.4.0
starlette==0.46.1
tabulate==0.9.0
tenacity==9.0.0
termcolor==2.5.0
text-unidecode==1.3
time-machine==2.16.0
typing-inspection==0.4.0
typing_extensions==4.13.0
tzdata==2025.2
uc-micro-py==1.0.3
unicodecsv==0.14.1
universal_pathlib==0.2.6
urllib3==2.3.0
websocket-client==1.8.0
Werkzeug==2.2.3
wheel==0.45.1
wirerope==1.0.0
wrapt==1.17.2
WTForms==3.2.1
yarl==1.18.3
zipp==3.21.0
