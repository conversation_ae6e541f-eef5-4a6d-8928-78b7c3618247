apiVersion: apps/v1
kind: Deployment
metadata:
  name: sil-track
  namespace: airflow
  labels:
    app: airflow
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sil-track
  template:
    metadata:
      name: sil-track
      labels:
        app: sil-track
    spec:
      containers:
        - name: sil-track
          image: registry.eqfleetcmder.com/eq-sim/sil-tools:0.0.4.2
          imagePullPolicy: Always
          ports:
            - containerPort: 8050
          command: ["python", "app.py"]
          resources:
            limits:
              cpu: 2
              memory: "2Gi"
            requests:
              cpu: "300m"
              memory: "100Mi"
      restartPolicy: Always
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret

---

apiVersion: v1
kind: Service
metadata:
  name: sil-track
  namespace: airflow
spec:
  selector:
    app: sil-track
  ports:
    - protocol: TCP
      port: 8050
      targetPort: 8050
      nodePort: 31150  # 指定 NodePort，范围通常是 30000-32767
  type: NodePort

