from airflow.hooks.base import BaseHook
import requests, json, logging
from utils.retry_wrapper import retry_decorator

class AirflowHook(BaseHook):
    def __init__(self, conn_id="airflow_self"):
        self.conn_id = conn_id
        self.conn = self.get_connection(self.conn_id)

    @retry_decorator(max_retries=5, delay=5)
    def post_request(self, url, data):
        url = f"{self.conn.host}{url}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {self.conn.password}"
        }
        res = requests.post(url, data=json.dumps(data), headers=headers)
        logging.info(res.text)
        if res.status_code == 200:
            return res.json()
        else:
            raise Exception(res.text)
