#!/usr/bin/env python3
"""
LanceDB Hook for Airflow
提供访问LanceDB向量数据库的常用方法
"""

import logging
from typing import Dict, List, Optional, Any, Union, Tuple
import pandas as pd
import numpy as np
from pathlib import Path

from airflow.hooks.base import BaseHook
from airflow.models import Connection
from airflow.exceptions import AirflowException
import lancedb
import pyarrow as pa
LANCEDB_AVAILABLE = True
# try:
#     import lancedb
#     import pyarrow as pa
#     LANCEDB_AVAILABLE = True
# except ImportError:
#     LANCEDB_AVAILABLE = False
#     lancedb = None
#     pa = None


class LanceDBHook(BaseHook):
    """
    LanceDB Hook for Airflow

    用于连接和操作LanceDB向量数据库的Hook类
    支持本地和远程LanceDB实例
    """

    conn_name_attr = "lancedb_conn_id"
    default_conn_name = "lancedb_default"
    conn_type = "lancedb"
    hook_name = "LanceDB"

    def __init__(self, lancedb_conn_id: str = default_conn_name):
        super().__init__()
        self.lancedb_conn_id = lancedb_conn_id
        self._connection = None
        self._db = None
        self.logger = logging.getLogger(__name__)

        if not LANCEDB_AVAILABLE:
            raise AirflowException(
                "LanceDB is not available. Please install it with: pip install lancedb"
            )

    def get_connection(self) -> Connection:
        """获取Airflow连接配置"""
        if self._connection is None:
            self._connection = self.get_conn()
        return self._connection

    def get_conn(self) -> Connection:
        """获取连接对象"""
        return BaseHook.get_connection(self.lancedb_conn_id)

    def get_db(self):
        """获取LanceDB数据库连接"""
        if self._db is None:
            conn = self.get_connection()

            # 解析连接参数
            if conn.host:
                # 远程连接
                uri = f"{conn.schema or 'http'}://{conn.host}"
                if conn.port:
                    uri += f":{conn.port}"
                if conn.extra_dejson.get('path'):
                    uri += conn.extra_dejson['path']
            else:
                # 本地连接
                uri = conn.extra_dejson.get('db_path', './lancedb')

            # 连接参数
            connect_kwargs = {}
            if conn.extra_dejson.get('api_key'):
                connect_kwargs['api_key'] = conn.extra_dejson['api_key']
            if conn.extra_dejson.get('region'):
                connect_kwargs['region'] = conn.extra_dejson['region']

            try:
                self._db = lancedb.connect(uri, **connect_kwargs)
                self.logger.info(f"成功连接到LanceDB: {uri}")
            except Exception as e:
                raise AirflowException(f"连接LanceDB失败: {e}")

        return self._db

    def close(self):
        """关闭连接"""
        if self._db:
            # LanceDB通常不需要显式关闭
            self._db = None
            self.logger.info("LanceDB连接已关闭")

    def list_tables(self) -> List[str]:
        """列出所有表"""
        try:
            db = self.get_db()
            tables = db.table_names()
            self.logger.info(f"找到 {len(tables)} 个表: {tables}")
            return tables
        except Exception as e:
            raise AirflowException(f"列出表失败: {e}")

    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            return table_name in self.list_tables()
        except Exception as e:
            self.logger.error(f"检查表存在性失败: {e}")
            return False

    def create_table(
        self,
        table_name: str,
        data: Union[pd.DataFrame, pa.Table, List[Dict]],
        mode: str = "create",
        embedding_column: Optional[str] = None
    ) -> Any:
        """
        创建表

        Args:
            table_name: 表名
            data: 数据 (DataFrame, PyArrow Table, 或字典列表)
            mode: 创建模式 ("create", "overwrite")
            embedding_column: 向量列名

        Returns:
            LanceDB Table对象
        """
        try:
            db = self.get_db()

            # 转换数据格式
            if isinstance(data, list):
                data = pd.DataFrame(data)
            elif isinstance(data, pa.Table):
                pass  # PyArrow Table可以直接使用
            elif isinstance(data, pd.DataFrame):
                pass  # DataFrame可以直接使用
            else:
                raise ValueError(f"不支持的数据类型: {type(data)}")

            # 检查表是否存在
            if self.table_exists(table_name):
                if mode == "overwrite":
                    self.drop_table(table_name)
                    self.logger.info(f"已删除现有表: {table_name}")
                elif mode == "create":
                    raise AirflowException(f"表 {table_name} 已存在")

            # 创建表
            table = db.create_table(table_name, data)
            self.logger.info(f"成功创建表: {table_name}, 行数: {len(data)}")

            return table

        except Exception as e:
            raise AirflowException(f"创建表失败: {e}")

    def get_table(self, table_name: str) -> Any:
        """获取表对象"""
        try:
            db = self.get_db()
            if not self.table_exists(table_name):
                raise AirflowException(f"表 {table_name} 不存在")

            table = db.open_table(table_name)
            self.logger.info(f"成功打开表: {table_name}")
            return table

        except Exception as e:
            raise AirflowException(f"获取表失败: {e}")

    def drop_table(self, table_name: str) -> bool:
        """删除表"""
        try:
            db = self.get_db()
            if not self.table_exists(table_name):
                self.logger.warning(f"表 {table_name} 不存在，跳过删除")
                return False

            db.drop_table(table_name)
            self.logger.info(f"成功删除表: {table_name}")
            return True

        except Exception as e:
            raise AirflowException(f"删除表失败: {e}")

    def insert_data(
        self,
        table_name: str,
        data: Union[pd.DataFrame, pa.Table, List[Dict]]
    ) -> int:
        """
        插入数据

        Args:
            table_name: 表名
            data: 要插入的数据

        Returns:
            插入的行数
        """
        try:
            table = self.get_table(table_name)

            # 转换数据格式
            if isinstance(data, list):
                data = pd.DataFrame(data)

            # 插入数据
            table.add(data)

            row_count = len(data)
            self.logger.info(f"成功插入 {row_count} 行数据到表 {table_name}")
            return row_count

        except Exception as e:
            raise AirflowException(f"插入数据失败: {e}")

    def vector_search(
        self,
        table_name: str,
        query_vector: Union[List[float], np.ndarray],
        limit: int = 10,
        columns: Optional[List[str]] = None,
        where: Optional[str] = None
    ) -> pd.DataFrame:
        """
        向量相似性搜索

        Args:
            table_name: 表名
            query_vector: 查询向量
            limit: 返回结果数量限制
            columns: 要返回的列
            where: 过滤条件

        Returns:
            搜索结果DataFrame
        """
        try:
            table = self.get_table(table_name)

            # 构建查询
            query = table.search(query_vector).limit(limit)

            # 添加列选择
            if columns:
                query = query.select(columns)

            # 添加过滤条件
            if where:
                query = query.where(where)

            # 执行查询
            results = query.to_pandas()

            self.logger.info(f"向量搜索完成，返回 {len(results)} 条结果")
            return results

        except Exception as e:
            raise AirflowException(f"向量搜索失败: {e}")

    def query_data(
        self,
        table_name: str,
        columns: Optional[List[str]] = None,
        where: Optional[str] = None,
        limit: Optional[int] = None
    ) -> pd.DataFrame:
        """
        查询数据

        Args:
            table_name: 表名
            columns: 要查询的列
            where: 过滤条件
            limit: 结果数量限制

        Returns:
            查询结果DataFrame
        """
        try:
            table = self.get_table(table_name)

            # 构建查询
            query = table.search()

            # 添加列选择
            if columns:
                query = query.select(columns)

            # 添加过滤条件
            if where:
                query = query.where(where)

            # 添加限制
            if limit:
                query = query.limit(limit)

            # 执行查询
            results = query.to_pandas()

            self.logger.info(f"数据查询完成，返回 {len(results)} 条结果")
            return results

        except Exception as e:
            raise AirflowException(f"查询数据失败: {e}")

    def update_data(
        self,
        table_name: str,
        where: str,
        values: Dict[str, Any]
    ) -> int:
        """
        更新数据

        Args:
            table_name: 表名
            where: 更新条件
            values: 更新值

        Returns:
            更新的行数
        """
        try:
            table = self.get_table(table_name)

            # 执行更新
            result = table.update(where=where, values=values)

            self.logger.info(f"数据更新完成，更新了 {result} 行")
            return result

        except Exception as e:
            raise AirflowException(f"更新数据失败: {e}")

    def delete_data(self, table_name: str, where: str) -> int:
        """
        删除数据

        Args:
            table_name: 表名
            where: 删除条件

        Returns:
            删除的行数
        """
        try:
            table = self.get_table(table_name)

            # 执行删除
            result = table.delete(where)

            self.logger.info(f"数据删除完成，删除了 {result} 行")
            return result

        except Exception as e:
            raise AirflowException(f"删除数据失败: {e}")

    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息

        Args:
            table_name: 表名

        Returns:
            表信息字典
        """
        try:
            table = self.get_table(table_name)

            # 获取表统计信息
            info = {
                'name': table_name,
                'schema': table.schema,
                'count_rows': table.count_rows(),
                'version': table.version,
            }

            self.logger.info(f"获取表信息: {table_name}")
            return info

        except Exception as e:
            raise AirflowException(f"获取表信息失败: {e}")

    def create_index(
        self,
        table_name: str,
        column: str,
        index_type: str = "IVF_PQ",
        **kwargs
    ) -> bool:
        """
        创建向量索引

        Args:
            table_name: 表名
            column: 向量列名
            index_type: 索引类型
            **kwargs: 其他索引参数

        Returns:
            是否成功创建索引
        """
        try:
            table = self.get_table(table_name)

            # 创建索引
            table.create_index(column, index_type=index_type, **kwargs)

            self.logger.info(f"成功为表 {table_name} 的列 {column} 创建 {index_type} 索引")
            return True

        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            return False
