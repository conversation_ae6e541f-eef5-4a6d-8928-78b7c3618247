from airflow.hooks.base import BaseHook
from airflow.providers.redis.hooks.redis import RedisHook
import requests, json, logging


class <PERSON>ishuHook(BaseHook):
    def __init__(self, feishu_conn_id: str = "feishu_msg_bot") -> None:
        self.feishu_conn_id = feishu_conn_id
        self.conn = self.get_connection(self.feishu_conn_id)
        self.redis_cli = RedisHook(
            redis_conn_id=self.conn.extra_dejson.get("redis_conn_id")
            ).get_conn()
        self.group_msg_uri = self.conn.extra_dejson.get("group_msg_uri")

    def refresh_token(self):
        token_redis_key = self.conn.extra_dejson.get("token_redis_key")
        self.access_token = self.redis_cli.get(token_redis_key)
        if self.access_token:
            self.access_token = self.access_token.decode('utf-8')
            return
        self.token_uri = self.conn.extra_dejson.get("token_uri")
        body = {
            "app_id": self.conn.login,
            "app_secret": self.conn.password,
        }
        resp = requests.post(f"{self.conn.host}{self.token_uri}", json=body)
        self.access_token = resp.json().get("tenant_access_token")
        self.redis_cli.set(token_redis_key, self.access_token, ex=7150) # 提前50s过期

    def get_feishu_id(self, user_email: str="", user_mobile: str=""):
        self.refresh_token()
        user_id = None
        # open_id = None
        self.uid_uri = self.conn.extra_dejson.get("uid_uri")
        params = {}
        if user_mobile:
            params["mobiles"] = user_mobile
        if user_email:
            params["emails"] = user_email
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        resp = requests.get(
            f"{self.conn.host}{self.uid_uri}",
            headers=headers,
            params=params)
        logging.info(resp.text)
        resd = resp.json().get("data", {})
        if user_mobile and resd.get("mobile_users"):
            # open_id = resd.get("mobile_users").get(user_mobile)[0].get("open_id")
            user_id = resd.get("mobile_users").get(user_mobile)[0].get("user_id")
        if not user_id and user_email and resd.get("email_users"):
            # open_id = resd.get("email_users").get(user_email)[0].get("open_id")
            user_id = resd.get("email_users").get(user_email)[0].get("user_id")
        return user_id

    def send_driect_msg(self, user_email: str="", user_mobile: str="", msg: str=""):
        user_id = self.get_feishu_id(user_email=user_email, user_mobile=user_mobile)
        if not user_id:
            logging.info("user_id not found")
            return False, "user_id not found"
        self.dm_uri = self.conn.extra_dejson.get("dm_uri")
        params = {"receive_id_type": "user_id"}
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        body = {
            "msg_type": "text",
            "receive_id": user_id,
            "content": json.dumps({"text": msg})
        }
        resp = requests.post(
            f"{self.conn.host}{self.dm_uri}",
            headers=headers,
            params=params,
            json=body)
        if resp.status_code == 200:
            logging.info(resp.text)
            return True, resp.text
        else:
            logging.info(resp.text)
            return False, resp.text

    def send_group_msg(self, group_uri: str="", msg: str=""):
        if not group_uri:
            group_uri = self.group_msg_uri
        body = {
            "msg_type": "text",
            "content": {"text": msg}
        }
        resp = requests.post(
            f"{self.conn.host}{group_uri}",
            json=body)
        if resp.status_code == 200:
            logging.info(resp.text)
            return True, resp.text
        else:
            logging.info(resp.text)
            return False, resp.text

    def send_msg(self, user_email: str="", user_mobile: str="", group_uri: str="", msg: str=""):
        success, resp_text = self.send_driect_msg(user_email=user_email, user_mobile=user_mobile, msg=msg)
        if success:
            return True, resp_text
        else:
            return self.send_group_msg(group_uri=group_uri, msg=msg)

    def build_mst_card(self, notify_list: list[dict], title: str="任务通知", group: bool=False) -> dict:
        message_body = {"msg_type": "interactive"}
        card = {
            "type": "template",
            "data": {
                "template_id": "AAqR5lGguZ8Vn",
                "template_version_name": "1.0.4",
                "template_variable": {
                    "message_title": title,
                    "notify_list": notify_list
                }
            }
        }
        if group:
            message_body["card"] = card
        else:
            message_body["content"] = json.dumps(card)
        return message_body

    def send_msg_card(
            self,
            user_email: str="",
            user_mobile: str="",
            group_uri: str="",
            notify_list: list[dict]=[],
            title: str="任务通知"
        ):
        user_id = self.get_feishu_id(user_email=user_email, user_mobile=user_mobile)
        if user_id:
            logging.info("user_id not found")
            message_body = self.build_mst_card(notify_list=notify_list, title=title)
            message_body["receive_id"] = user_id
            self.dm_uri = self.conn.extra_dejson.get("dm_uri")
            params = {"receive_id_type": "user_id"}
            headers = {
                "Authorization": f"Bearer {self.access_token}"
            }
            resp = requests.post(
                f"{self.conn.host}{self.dm_uri}",
                headers=headers,
                params=params,
                json=message_body)
            if resp.status_code == 200:
                logging.info(resp.text)
                return True, resp.text
            else:
                logging.info(resp.text)
                return False, resp.text
        else:
            message_body = self.build_mst_card(notify_list=notify_list, title=title, group=True)
            if not group_uri:
                group_uri = self.group_msg_uri
            resp = requests.post(
                f"{self.conn.host}{group_uri}",
                json=message_body)
            if resp.status_code == 200:
                logging.info(resp.text)
                return True, resp.text
            else:
                logging.info(resp.text)
                return False, resp.text
