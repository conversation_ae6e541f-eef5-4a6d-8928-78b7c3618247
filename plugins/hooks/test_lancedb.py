#!/usr/bin/env python3
"""
LanceDB Hook测试和使用示例
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_lancedb_hook():
    """测试LanceDB Hook基本功能"""
    print("🚀 LanceDB Hook功能测试")
    print("=" * 60)
    
    try:
        from hooks.lancedb import LanceDBHook
        
        # 创建Hook实例
        hook = LanceDBHook(lancedb_conn_id="lancedb_default")
        
        # 测试连接
        print("1. 测试数据库连接...")
        try:
            db = hook.get_db()
            print("✅ 数据库连接成功")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return
        
        # 测试列出表
        print("\n2. 列出现有表...")
        tables = hook.list_tables()
        print(f"现有表: {tables}")
        
        # 创建测试数据
        print("\n3. 创建测试数据...")
        test_data = create_test_data()
        print(f"创建了 {len(test_data)} 条测试记录")
        
        # 创建表
        table_name = "test_vectors"
        print(f"\n4. 创建表: {table_name}")
        
        try:
            table = hook.create_table(table_name, test_data, mode="overwrite")
            print("✅ 表创建成功")
        except Exception as e:
            print(f"❌ 表创建失败: {e}")
            return
        
        # 获取表信息
        print(f"\n5. 获取表信息...")
        info = hook.get_table_info(table_name)
        print(f"表信息: 行数={info['count_rows']}, 版本={info['version']}")
        
        # 插入更多数据
        print(f"\n6. 插入更多数据...")
        additional_data = create_additional_data()
        rows_inserted = hook.insert_data(table_name, additional_data)
        print(f"插入了 {rows_inserted} 行数据")
        
        # 向量搜索
        print(f"\n7. 执行向量搜索...")
        query_vector = np.random.rand(128).tolist()
        search_results = hook.vector_search(
            table_name, 
            query_vector, 
            limit=5,
            columns=["id", "text", "_distance"]
        )
        print(f"搜索结果:")
        print(search_results[['id', 'text', '_distance']].head())
        
        # 数据查询
        print(f"\n8. 执行数据查询...")
        query_results = hook.query_data(
            table_name,
            columns=["id", "text", "category"],
            where="category = 'tech'",
            limit=3
        )
        print(f"查询结果:")
        print(query_results)
        
        # 更新数据
        print(f"\n9. 更新数据...")
        updated_rows = hook.update_data(
            table_name,
            where="id = 1",
            values={"text": "Updated text content"}
        )
        print(f"更新了 {updated_rows} 行数据")
        
        # 删除数据
        print(f"\n10. 删除数据...")
        deleted_rows = hook.delete_data(table_name, "id > 8")
        print(f"删除了 {deleted_rows} 行数据")
        
        # 创建索引
        print(f"\n11. 创建向量索引...")
        index_created = hook.create_index(table_name, "vector", "IVF_PQ")
        if index_created:
            print("✅ 索引创建成功")
        else:
            print("⚠️ 索引创建失败或已存在")
        
        # 清理
        print(f"\n12. 清理测试数据...")
        hook.drop_table(table_name)
        print("✅ 测试表已删除")
        
        hook.close()
        print("✅ 连接已关闭")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请安装LanceDB: pip install lancedb")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    data = []
    categories = ['tech', 'science', 'art', 'sports']
    
    for i in range(10):
        data.append({
            'id': i,
            'text': f'This is test document {i}',
            'category': categories[i % len(categories)],
            'vector': np.random.rand(128).tolist(),
            'timestamp': datetime.now().isoformat(),
            'score': np.random.rand()
        })
    
    return data

def create_additional_data():
    """创建额外的测试数据"""
    np.random.seed(123)
    
    data = []
    for i in range(10, 15):
        data.append({
            'id': i,
            'text': f'Additional test document {i}',
            'category': 'news',
            'vector': np.random.rand(128).tolist(),
            'timestamp': datetime.now().isoformat(),
            'score': np.random.rand()
        })
    
    return data

def demo_airflow_usage():
    """演示在Airflow中的使用方法"""
    print(f"\n🔧 Airflow使用示例")
    print("=" * 60)
    
    airflow_code = '''
# 在Airflow DAG中使用LanceDB Hook

from airflow.decorators import dag, task
from datetime import datetime, timedelta
from plugins.hooks.lancedb import LanceDBHook
import pandas as pd
import numpy as np

@dag(
    dag_id='lancedb_example',
    start_date=datetime(2025, 1, 1),
    schedule_interval=None,
    catchup=False,
    tags=['lancedb', 'vector-db']
)
def lancedb_example_dag():
    
    @task
    def create_embeddings():
        """创建文档嵌入向量"""
        # 模拟文档数据
        documents = [
            {"id": 1, "text": "Machine learning is amazing", "category": "tech"},
            {"id": 2, "text": "Deep learning transforms AI", "category": "tech"},
            {"id": 3, "text": "Natural language processing", "category": "tech"},
        ]
        
        # 模拟生成嵌入向量（实际中可能使用OpenAI、HuggingFace等）
        for doc in documents:
            doc['vector'] = np.random.rand(768).tolist()  # 768维向量
        
        return documents
    
    @task
    def store_vectors(documents):
        """存储向量到LanceDB"""
        hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
        
        # 创建或更新表
        table_name = "document_embeddings"
        hook.create_table(table_name, documents, mode="overwrite")
        
        # 创建向量索引以提高搜索性能
        hook.create_index(table_name, "vector", "IVF_PQ")
        
        return f"存储了 {len(documents)} 个文档向量"
    
    @task
    def semantic_search(query_text: str = "artificial intelligence"):
        """语义搜索"""
        hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
        
        # 生成查询向量（实际中需要使用相同的嵌入模型）
        query_vector = np.random.rand(768).tolist()
        
        # 执行向量搜索
        results = hook.vector_search(
            table_name="document_embeddings",
            query_vector=query_vector,
            limit=5,
            columns=["id", "text", "category", "_distance"]
        )
        
        print(f"搜索查询: {query_text}")
        print("搜索结果:")
        print(results)
        
        return results.to_dict('records')
    
    @task
    def update_document_metadata():
        """更新文档元数据"""
        hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
        
        # 更新特定文档的元数据
        updated_rows = hook.update_data(
            table_name="document_embeddings",
            where="category = 'tech'",
            values={"last_updated": datetime.now().isoformat()}
        )
        
        return f"更新了 {updated_rows} 个文档的元数据"
    
    @task
    def cleanup_old_documents():
        """清理旧文档"""
        hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
        
        # 删除旧文档（示例：删除特定条件的文档）
        deleted_rows = hook.delete_data(
            table_name="document_embeddings",
            where="id < 0"  # 示例条件
        )
        
        return f"清理了 {deleted_rows} 个旧文档"
    
    # 定义任务依赖
    docs = create_embeddings()
    store_result = store_vectors(docs)
    search_result = semantic_search()
    update_result = update_document_metadata()
    cleanup_result = cleanup_old_documents()
    
    # 设置依赖关系
    store_result >> [search_result, update_result, cleanup_result]

# 实例化DAG
dag_instance = lancedb_example_dag()

# 自定义Operator示例
from airflow.models import BaseOperator
from airflow.utils.context import Context

class LanceDBVectorSearchOperator(BaseOperator):
    """自定义LanceDB向量搜索Operator"""
    
    def __init__(
        self,
        lancedb_conn_id: str,
        table_name: str,
        query_vector: list,
        limit: int = 10,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.lancedb_conn_id = lancedb_conn_id
        self.table_name = table_name
        self.query_vector = query_vector
        self.limit = limit
    
    def execute(self, context: Context):
        hook = LanceDBHook(lancedb_conn_id=self.lancedb_conn_id)
        
        results = hook.vector_search(
            table_name=self.table_name,
            query_vector=self.query_vector,
            limit=self.limit
        )
        
        self.log.info(f"向量搜索完成，返回 {len(results)} 条结果")
        return results.to_dict('records')
    '''
    
    print(airflow_code)

def show_connection_config():
    """显示连接配置示例"""
    print(f"\n⚙️ Airflow连接配置")
    print("=" * 60)
    
    config_examples = '''
# Airflow连接配置示例

## 1. 本地LanceDB连接
Connection ID: lancedb_default
Connection Type: lancedb
Host: (留空)
Schema: (留空)
Login: (留空)
Password: (留空)
Extra: {"db_path": "/path/to/lancedb"}

## 2. LanceDB Cloud连接
Connection ID: lancedb_cloud
Connection Type: lancedb
Host: your-instance.lancedb.com
Schema: https
Port: 443
Login: (留空)
Password: (留空)
Extra: {
    "api_key": "your-api-key",
    "region": "us-east-1",
    "path": "/your-database"
}

## 3. 使用环境变量
Connection ID: lancedb_prod
Connection Type: lancedb
Host: ${LANCEDB_HOST}
Extra: {
    "api_key": "${LANCEDB_API_KEY}",
    "db_path": "${LANCEDB_PATH}"
}
    '''
    
    print(config_examples)

def main():
    """主函数"""
    print("🧪 LanceDB Hook测试和演示")
    
    # 运行测试
    test_lancedb_hook()
    
    # 显示使用示例
    demo_airflow_usage()
    
    # 显示配置示例
    show_connection_config()
    
    print(f"\n" + "=" * 60)
    print("✅ 演示完成")
    print("=" * 60)
    
    print("\n📝 使用说明:")
    print("1. 安装依赖: pip install lancedb pyarrow")
    print("2. 在Airflow中配置LanceDB连接")
    print("3. 在DAG中导入并使用LanceDBHook")
    print("4. 根据需要创建自定义Operator")

if __name__ == "__main__":
    main()
