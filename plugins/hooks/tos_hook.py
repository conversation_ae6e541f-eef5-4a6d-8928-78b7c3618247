from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from boto3.s3.transfer import S3<PERSON><PERSON>s<PERSON>, TransferConfig
from botocore.exceptions import ClientError
import boto3
import botocore
import botocore.session
from botocore.config import Config
from botocore.waiter import Waiter, WaiterModel
from functools import cached_property, wraps
from airflow.exceptions import AirflowException, AirflowNotFoundException
import re
from io import BytesIO
from copy import deepcopy
import gzip as gz
import uuid
import os


class TosHook(S3Hook):
    """
    A hook for Tos
    """

    def __init__(
        self,
        conn_id: str | None
    ) -> None:
        conn_obj = self.get_connection(conn_id)
        self.ak = conn_obj.login
        self.sk = conn_obj.password
        self.endpoint = conn_obj.host
        self._extra_args = {}
        self.transfer_config = TransferConfig()

    @property
    def extra_args(self):
        """Return hook's extra arguments (immutable)."""
        return deepcopy(self._extra_args)

    @cached_property
    def conn(self) -> boto3.client:
        return boto3.client(
            's3',
            aws_access_key_id=self.ak,
            aws_secret_access_key=self.sk,
            use_ssl=True,
            endpoint_url=self.endpoint,
            config=Config(s3={"addressing_style": "virtual", "signature_version": 's3v4'})
        )

    def get_conn(self) -> boto3.client:
        """
        Get the underlying boto3 client/resource (cached).

        Implemented so that caching works as intended. It exists for compatibility
        with subclasses that rely on a super().get_conn() method.

        :return: boto3.client or boto3.resource
        """
        # Compat shim
        return self.conn

    def get_session(self) -> boto3.Session:
        return boto3.Session(
            aws_access_key_id=self.ak,
            aws_secret_access_key=self.sk
        )

    @cached_property
    def resource(self):
        return self.get_session().resource(
            "s3",
            endpoint_url=self.endpoint,
            config=Config(s3={"addressing_style": "virtual", "signature_version": 's3v4'}),
            verify=False,
        )

    def check_for_bucket(self, bucket_name: str | None = None) -> bool:
        """
        Check if bucket_name exists.

        .. seealso::
            - :external+boto3:py:meth:`S3.Client.head_bucket`

        :param bucket_name: the name of the bucket
        :return: True if it exists and False if not.
        """
        try:
            self.get_conn().head_bucket(Bucket=bucket_name)
            return True
        except ClientError as e:
            # The head_bucket api is odd in that it cannot return proper
            # exception objects, so error codes must be used. Only 200, 404 and 403
            # are ever returned. See the following links for more details:
            # https://github.com/boto/boto3/issues/2499
            # https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html#S3.Client.head_bucket
            return_code = int(e.response["Error"]["Code"])
            if return_code == 404:
                self.log.info('Bucket "%s" does not exist', bucket_name)
            elif return_code == 403:
                self.log.error(
                    'Access to bucket "%s" is forbidden or there was an error with the request',
                    bucket_name,
                )
                self.log.error(e)
            return False

    def get_bucket(self, bucket_name: str | None = None) -> object:
        """
        Return a :py:class:`S3.Bucket` object.

        .. seealso::
            - :external+boto3:py:meth:`S3.ServiceResource.Bucket`

        :param bucket_name: the name of the bucket
        :return: the bucket object to the bucket name.
        """
        return self.resource.Bucket(bucket_name)

    def create_bucket(self, bucket_name: str | None = None, region_name: str | None = None) -> None:
        """
        Create an Amazon S3 bucket.

        .. seealso::
            - :external+boto3:py:meth:`S3.Client.create_bucket`

        :param bucket_name: The name of the bucket
        :param region_name: The name of the aws region in which to create the bucket.
        """
        if not region_name:
            self.get_conn().create_bucket(Bucket=bucket_name)
        else:
            self.get_conn().create_bucket(
                Bucket=bucket_name,
                CreateBucketConfiguration={"LocationConstraint": region_name},
            )

    def check_for_prefix(self, prefix: str, delimiter: str, bucket_name: str | None = None) -> bool:
        """
        Check that a prefix exists in a bucket.

        :param bucket_name: the name of the bucket
        :param prefix: a key prefix
        :param delimiter: the delimiter marks key hierarchy.
        :return: False if the prefix does not exist in the bucket and True if it does.
        """
        if not prefix.endswith(delimiter):
            prefix += delimiter
        prefix_split = re.split(rf"(\w+[{delimiter}])$", prefix, 1)
        previous_level = prefix_split[0]
        plist = self.list_prefixes(bucket_name, previous_level, delimiter)
        return prefix in plist

    def list_prefixes(
        self,
        bucket_name: str | None = None,
        prefix: str | None = None,
        delimiter: str | None = None,
        page_size: int | None = None,
        max_items: int | None = None,
    ) -> list:
        """
        List prefixes in a bucket under prefix.

        .. seealso::
            - :external+boto3:py:class:`S3.Paginator.ListObjectsV2`

        :param bucket_name: the name of the bucket
        :param prefix: a key prefix
        :param delimiter: the delimiter marks key hierarchy.
        :param page_size: pagination size
        :param max_items: maximum items to return
        :return: a list of matched prefixes
        """
        prefix = prefix or ""
        delimiter = delimiter or ""
        config = {
            "PageSize": page_size,
            "MaxItems": max_items,
        }

        paginator = self.get_conn().get_paginator("list_objects_v2")
        response = paginator.paginate(
            Bucket=bucket_name,
            Prefix=prefix,
            Delimiter=delimiter,
            PaginationConfig=config,
        )

        prefixes: list[str] = []
        for page in response:
            if "CommonPrefixes" in page:
                prefixes.extend(common_prefix["Prefix"] for common_prefix in page["CommonPrefixes"])

        return prefixes

    def load_string(
        self,
        string_data: str,
        key: str,
        bucket_name: str | None = None,
        replace: bool = False,
        encrypt: bool = False,
        encoding: str | None = None,
        acl_policy: str | None = None,
        compression: str | None = None,
    ) -> None:
        """
        Load a string to S3.

        This is provided as a convenience to drop a string in S3. It uses the
        boto infrastructure to ship a file to s3.

        .. seealso::
            - :external+boto3:py:meth:`S3.Client.upload_fileobj`

        :param string_data: str to set as content for the key.
        :param key: S3 key that will point to the file
        :param bucket_name: Name of the bucket in which to store the file
        :param replace: A flag to decide whether or not to overwrite the key
            if it already exists
        :param encrypt: If True, the file will be encrypted on the server-side
            by S3 and will be stored in an encrypted form while at rest in S3.
        :param encoding: The string to byte encoding
        :param acl_policy: The string to specify the canned ACL policy for the
            object to be uploaded
        :param compression: Type of compression to use, currently only gzip is supported.
        """
        encoding = encoding or "utf-8"

        bytes_data = string_data.encode(encoding)

        # Compress string
        available_compressions = ["gzip"]
        if compression is not None and compression not in available_compressions:
            raise NotImplementedError(
                f"Received {compression} compression type. "
                f"String can currently be compressed in {available_compressions} only."
            )
        if compression == "gzip":
            bytes_data = gz.compress(bytes_data)

        tmp_file_path = f"/tmp/{uuid.uuid4()}"
        with open(tmp_file_path, "wb") as f:
            f.write(bytes_data)
        self._upload_file_obj(tmp_file_path, key, bucket_name, replace, encrypt, acl_policy)
        os.remove(tmp_file_path)

    def load_bytes(
        self,
        bytes_data: bytes,
        key: str,
        bucket_name: str | None = None,
        replace: bool = False,
        encrypt: bool = False,
        acl_policy: str | None = None,
    ) -> None:
        """
        Load bytes to S3.

        This is provided as a convenience to drop bytes data into S3. It uses the
        boto infrastructure to ship a file to s3.

        .. seealso::
            - :external+boto3:py:meth:`S3.Client.upload_fileobj`

        :param bytes_data: bytes to set as content for the key.
        :param key: S3 key that will point to the file
        :param bucket_name: Name of the bucket in which to store the file
        :param replace: A flag to decide whether or not to overwrite the key
            if it already exists
        :param encrypt: If True, the file will be encrypted on the server-side
            by S3 and will be stored in an encrypted form while at rest in S3.
        :param acl_policy: The string to specify the canned ACL policy for the
            object to be uploaded
        """
        tmp_file_path = f"/tmp/{uuid.uuid4()}"
        with open(tmp_file_path, "wb") as f:
            f.write(bytes_data)
        self._upload_file_obj(tmp_file_path, key, bucket_name, replace, encrypt, acl_policy)
        os.remove(tmp_file_path)

    def load_file_obj(
        self,
        file_obj: BytesIO,
        key: str,
        bucket_name: str | None = None,
        replace: bool = False,
        encrypt: bool = False,
        acl_policy: str | None = None,
    ) -> None:
        """
        Load a file object to S3.

        .. seealso::
            - :external+boto3:py:meth:`S3.Client.upload_fileobj`

        :param file_obj: The file-like object to set as the content for the S3 key.
        :param key: S3 key that will point to the file
        :param bucket_name: Name of the bucket in which to store the file
        :param replace: A flag that indicates whether to overwrite the key
            if it already exists.
        :param encrypt: If True, S3 encrypts the file on the server,
            and the file is stored in encrypted form at rest in S3.
        :param acl_policy: The string to specify the canned ACL policy for the
            object to be uploaded
        """
        self._upload_file_obj(file_obj, key, bucket_name, replace, encrypt, acl_policy)

    def _upload_file_obj(
        self,
        file_obj: str,
        key: str,
        bucket_name: str | None = None,
        replace: bool = False,
        encrypt: bool = False,
        acl_policy: str | None = None,
    ) -> None:
        if not replace and self.check_for_key(key, bucket_name):
            raise ValueError(f"The key {key} already exists.")

        extra_args = self.extra_args
        if encrypt:
            extra_args["ServerSideEncryption"] = "AES256"
        if acl_policy:
            extra_args["ACL"] = acl_policy

        client = self.get_conn()
        client.upload_file(
            file_obj,
            bucket_name,
            key
        )
