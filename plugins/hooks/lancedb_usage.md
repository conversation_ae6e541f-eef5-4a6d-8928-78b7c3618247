# LanceDB Hook使用说明

## 概述

`LanceDBHook` 是为Airflow设计的LanceDB向量数据库连接器，提供了完整的向量数据库操作功能，包括表管理、数据插入、向量搜索、索引创建等。

## 🚀 主要功能

- **连接管理**: 支持本地和云端LanceDB实例
- **表操作**: 创建、删除、查询表信息
- **数据操作**: 插入、更新、删除数据
- **向量搜索**: 高效的相似性搜索
- **索引管理**: 创建和管理向量索引
- **类型支持**: 支持pandas DataFrame、PyArrow Table、字典列表

## 📦 安装依赖

```bash
pip install lancedb pyarrow pandas numpy
```

## ⚙️ 连接配置

### 本地LanceDB连接

```python
# Airflow连接配置
Connection ID: lancedb_default
Connection Type: lancedb
Host: (留空)
Extra: {"db_path": "/path/to/lancedb"}
```

### LanceDB Cloud连接

```python
Connection ID: lancedb_cloud
Connection Type: lancedb
Host: your-instance.lancedb.com
Schema: https
Port: 443
Extra: {
    "api_key": "your-api-key",
    "region": "us-east-1",
    "path": "/your-database"
}
```

## 🔧 基本使用

### 创建Hook实例

```python
from plugins.hooks.lancedb import LanceDBHook

# 使用默认连接
hook = LanceDBHook()

# 使用指定连接
hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
```

### 表管理

```python
# 列出所有表
tables = hook.list_tables()

# 检查表是否存在
exists = hook.table_exists("my_table")

# 创建表
import pandas as pd
data = pd.DataFrame({
    'id': [1, 2, 3],
    'text': ['doc1', 'doc2', 'doc3'],
    'vector': [[0.1, 0.2], [0.3, 0.4], [0.5, 0.6]]
})
table = hook.create_table("my_table", data)

# 获取表信息
info = hook.get_table_info("my_table")
print(f"行数: {info['count_rows']}")

# 删除表
hook.drop_table("my_table")
```

### 数据操作

```python
# 插入数据
new_data = [
    {'id': 4, 'text': 'doc4', 'vector': [0.7, 0.8]},
    {'id': 5, 'text': 'doc5', 'vector': [0.9, 1.0]}
]
rows_inserted = hook.insert_data("my_table", new_data)

# 查询数据
results = hook.query_data(
    table_name="my_table",
    columns=["id", "text"],
    where="id > 2",
    limit=10
)

# 更新数据
updated_rows = hook.update_data(
    table_name="my_table",
    where="id = 1",
    values={"text": "updated doc1"}
)

# 删除数据
deleted_rows = hook.delete_data("my_table", "id > 10")
```

### 向量搜索

```python
import numpy as np

# 准备查询向量
query_vector = np.random.rand(128).tolist()

# 执行向量搜索
search_results = hook.vector_search(
    table_name="embeddings",
    query_vector=query_vector,
    limit=5,
    columns=["id", "text", "_distance"],
    where="category = 'tech'"
)

print("搜索结果:")
print(search_results)
```

### 索引管理

```python
# 创建向量索引
success = hook.create_index(
    table_name="embeddings",
    column="vector",
    index_type="IVF_PQ",
    num_partitions=256,
    num_sub_vectors=16
)
```

## 🚁 在Airflow DAG中使用

### 基本DAG示例

```python
from airflow.decorators import dag, task
from datetime import datetime
from plugins.hooks.lancedb import LanceDBHook
import numpy as np

@dag(
    dag_id='vector_processing',
    start_date=datetime(2025, 1, 1),
    schedule_interval=None,
    catchup=False
)
def vector_processing_dag():
    
    @task
    def create_embeddings():
        """生成文档嵌入"""
        documents = [
            {"id": 1, "text": "AI and machine learning", "category": "tech"},
            {"id": 2, "text": "Data science applications", "category": "tech"},
            {"id": 3, "text": "Natural language processing", "category": "nlp"}
        ]
        
        # 生成嵌入向量（实际使用中可能调用OpenAI API等）
        for doc in documents:
            doc['vector'] = np.random.rand(768).tolist()
        
        return documents
    
    @task
    def store_vectors(documents):
        """存储向量到LanceDB"""
        hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
        
        # 创建表
        hook.create_table("documents", documents, mode="overwrite")
        
        # 创建索引
        hook.create_index("documents", "vector", "IVF_PQ")
        
        return len(documents)
    
    @task
    def semantic_search():
        """执行语义搜索"""
        hook = LanceDBHook(lancedb_conn_id="lancedb_prod")
        
        # 查询向量
        query_vector = np.random.rand(768).tolist()
        
        # 搜索相似文档
        results = hook.vector_search(
            table_name="documents",
            query_vector=query_vector,
            limit=3
        )
        
        return results.to_dict('records')
    
    # 任务依赖
    docs = create_embeddings()
    count = store_vectors(docs)
    results = semantic_search()
    
    count >> results

dag_instance = vector_processing_dag()
```

### 自定义Operator

```python
from airflow.models import BaseOperator
from airflow.utils.context import Context
from plugins.hooks.lancedb import LanceDBHook

class LanceDBVectorSearchOperator(BaseOperator):
    """LanceDB向量搜索Operator"""
    
    def __init__(
        self,
        lancedb_conn_id: str,
        table_name: str,
        query_vector: list,
        limit: int = 10,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.lancedb_conn_id = lancedb_conn_id
        self.table_name = table_name
        self.query_vector = query_vector
        self.limit = limit
    
    def execute(self, context: Context):
        hook = LanceDBHook(lancedb_conn_id=self.lancedb_conn_id)
        
        results = hook.vector_search(
            table_name=self.table_name,
            query_vector=self.query_vector,
            limit=self.limit
        )
        
        return results.to_dict('records')

# 在DAG中使用
search_task = LanceDBVectorSearchOperator(
    task_id="vector_search",
    lancedb_conn_id="lancedb_prod",
    table_name="embeddings",
    query_vector=[0.1, 0.2, 0.3],  # 实际查询向量
    limit=5
)
```

## 📊 实际应用场景

### 1. 文档相似性搜索

```python
@task
def document_similarity_search():
    hook = LanceDBHook()
    
    # 存储文档嵌入
    documents = load_documents_with_embeddings()
    hook.create_table("doc_embeddings", documents, mode="overwrite")
    
    # 创建索引提高搜索性能
    hook.create_index("doc_embeddings", "embedding", "IVF_PQ")
    
    # 搜索相似文档
    query_embedding = get_query_embedding("machine learning")
    similar_docs = hook.vector_search(
        "doc_embeddings", 
        query_embedding, 
        limit=10
    )
    
    return similar_docs
```

### 2. 推荐系统

```python
@task
def product_recommendations(user_id: int):
    hook = LanceDBHook()
    
    # 获取用户偏好向量
    user_vector = get_user_preference_vector(user_id)
    
    # 搜索相似产品
    recommendations = hook.vector_search(
        table_name="product_embeddings",
        query_vector=user_vector,
        limit=20,
        where=f"user_id != {user_id}"  # 排除用户已有产品
    )
    
    return recommendations
```

### 3. 异常检测

```python
@task
def anomaly_detection():
    hook = LanceDBHook()
    
    # 获取正常行为模式的嵌入
    normal_patterns = get_normal_behavior_embeddings()
    hook.create_table("normal_patterns", normal_patterns)
    
    # 检测异常
    current_behavior = get_current_behavior_embedding()
    
    # 搜索最相似的正常模式
    similar_patterns = hook.vector_search(
        "normal_patterns",
        current_behavior,
        limit=1
    )
    
    # 如果距离太大，则认为是异常
    if similar_patterns.iloc[0]['_distance'] > threshold:
        return "异常检测到"
    else:
        return "正常行为"
```

## 🔍 高级功能

### 批量操作

```python
@task
def batch_vector_processing():
    hook = LanceDBHook()
    
    # 批量插入大量数据
    batch_size = 1000
    for i in range(0, len(large_dataset), batch_size):
        batch = large_dataset[i:i+batch_size]
        hook.insert_data("large_table", batch)
    
    # 批量更新
    hook.update_data(
        "large_table",
        where="category = 'old'",
        values={"category": "updated"}
    )
```

### 数据版本管理

```python
@task
def version_management():
    hook = LanceDBHook()
    
    # 获取表版本信息
    info = hook.get_table_info("versioned_table")
    current_version = info['version']
    
    # 创建新版本的数据
    new_data = prepare_updated_data()
    
    # 插入新数据（LanceDB自动管理版本）
    hook.insert_data("versioned_table", new_data)
    
    return f"从版本 {current_version} 更新完成"
```

## 🛠️ 故障排查

### 常见问题

1. **连接失败**
   ```
   Error: 连接LanceDB失败
   ```
   - 检查连接配置
   - 验证数据库路径或URL
   - 确认API密钥有效

2. **依赖缺失**
   ```
   Error: LanceDB is not available
   ```
   - 安装LanceDB: `pip install lancedb`
   - 安装PyArrow: `pip install pyarrow`

3. **向量维度不匹配**
   ```
   Error: Vector dimension mismatch
   ```
   - 确保查询向量与表中向量维度一致
   - 检查嵌入模型输出维度

### 调试方法

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)

# 测试连接
hook = LanceDBHook()
try:
    db = hook.get_db()
    print("✅ 连接成功")
except Exception as e:
    print(f"❌ 连接失败: {e}")

# 检查表状态
tables = hook.list_tables()
print(f"可用表: {tables}")
```

## 🚀 最佳实践

1. **索引优化**: 为大表创建适当的向量索引
2. **批量操作**: 使用批量插入提高性能
3. **连接管理**: 及时关闭连接释放资源
4. **错误处理**: 添加适当的异常处理
5. **监控**: 监控查询性能和存储使用情况

## 📈 性能优化

```python
# 1. 使用批量操作
def bulk_insert_optimized(hook, table_name, data, batch_size=1000):
    for i in range(0, len(data), batch_size):
        batch = data[i:i+batch_size]
        hook.insert_data(table_name, batch)

# 2. 创建合适的索引
hook.create_index(
    table_name="large_vectors",
    column="embedding",
    index_type="IVF_PQ",
    num_partitions=256,  # 根据数据量调整
    num_sub_vectors=16   # 根据向量维度调整
)

# 3. 使用列选择减少数据传输
results = hook.vector_search(
    table_name="embeddings",
    query_vector=query_vector,
    columns=["id", "title", "_distance"],  # 只选择需要的列
    limit=10
)
```
