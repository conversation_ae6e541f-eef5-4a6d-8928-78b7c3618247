from airflow.providers.redis.hooks.redis import RedisHook
from airflow.models.variable import Variable
import uuid
import logging
K8S_RESOURCE_LOCKER_NAME = Variable.get("k8s_resource_locker_name")

class EQRedisHook(RedisHook):
    """
    Interact with <PERSON><PERSON> using the RedisHook.
    """

    def __init__(self, redis_conn_id: str="dev-redis", *args, **kwargs):
        super().__init__(redis_conn_id=redis_conn_id, *args, **kwargs)


class EQRedisLockHook(EQRedisHook):
    def acquire_lock(self, unique_id: str="", lock_key: str=K8S_RESOURCE_LOCKER_NAME, lock_expire_time: int=10):
        """尝试获取锁"""
        cli = self.get_conn()
        unique_id = unique_id if unique_id else str(uuid.uuid4())  # 生成唯一的标识符
        result = cli.set(lock_key, unique_id, nx=True, ex=lock_expire_time)
        logging.info(f"尝试获取锁, 结果为{result}")
        if result:
            logging.info(f"获取锁成功，锁的key为：{lock_key}，锁的value为：{unique_id}")
        else:
            logging.info(f"获取锁失败，锁的key为：{lock_key}，锁的value为：{unique_id}")
        return result, unique_id

    def release_lock(self, unique_id: str, lock_key: str=K8S_RESOURCE_LOCKER_NAME):
        """释放锁"""
        # 使用 Lua 脚本确保原子性
        script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        cli = self.get_conn()
        result = cli.eval(script, 1, lock_key, unique_id)
        logging.info(f"尝试释放锁, 结果为{result}")
        if result == 1:
            logging.info(f"释放锁成功，锁的key为：{lock_key}，锁的value为：{unique_id}")
        else:
            logging.info(f"释放锁失败，锁的key为：{lock_key}，锁的value为：{unique_id}")
        return result == 1
