from airflow.hooks.base import BaseHook
import requests, json, logging
from utils.retry_wrapper import retry_decorator
from utils.login_method import Login
from hooks.redis_hook import EQRedisHook

class StatusHook(BaseHook):

    def __init__(self, status_hook_conn_id="dms_status_hook_test"):
        self.status_hook_conn_id = status_hook_conn_id
        self.conn = self.get_connection(self.status_hook_conn_id)
        self.status_uri = self.conn.extra_dejson.get("status_uri")
        self.task_status_uri = self.conn.extra_dejson.get("task_status_uri", "/task/callback")
        self.interface_token_uri = self.conn.extra_dejson.get("interface_token_uri", "/data/clientLogin")
        self.image_task_uri = self.conn.extra_dejson.get("image_task_uri", "/data/imageBuildCallback")
        self.redis_cli = EQRedisHook(
            redis_conn_id=self.conn.extra_dejson.get("redis_conn_id", "dev-redis")
            ).get_conn()
        if status_hook_conn_id.endswith("test") or status_hook_conn_id.endswith("dev"):
            self.service_env = "dev"
        else:
            self.service_env = "prod"

    @retry_decorator(max_retries=5, delay=2)
    def refresh_service_token(self, refresh=False):
        logging.info("refresh service token")
        token_redis_key = self.conn.extra_dejson.get("token_redis_key", "eq-data-closedloop-service-token")
        self.service_token = self.redis_cli.get(token_redis_key)
        if self.service_token:
            self.service_token = self.service_token.decode('utf-8')
        if refresh or not self.service_token:
            login = Login(self.service_env, "dlcp")
            flag, res = login.login_in(self.conn.login, self.conn.password)
            if not flag:
                logging.error(f"login failed, {res}")
                return
            self.service_token = res.get("token")
            self.redis_cli.set(token_redis_key, self.service_token, ex=7200)
            return

    # 兼容旧版本
    @retry_decorator(max_retries=5, delay=2)
    def update_status(self, status, workflow_name):
        params = {
            "name": workflow_name,
            "status": status
        }
        logging.info(f"request {self.status_uri} with params: {params}")
        resp = requests.get(
            f"{self.conn.host}{self.status_uri}",
            params=params)
        logging.info(f"request {self.status_uri} response:{resp.text}")
        if resp.status_code == 200 and resp.text == "ok":
            return True
        else:
            return False

    @retry_decorator(max_retries=5, delay=2)
    def update_task_status(self, taskId, taskDetailId, status, info, errMsg):
        self.refresh_service_token()
        params = {
            "taskId": taskId,
            "taskDetailId": taskDetailId,
            "status": status,
            "info": info,
            "errMsg": errMsg
        }
        headers = {'token': self.service_token}
        logging.info(f"request {self.task_status_uri} with params: {params}")
        resp = requests.get(
            f"{self.conn.host}{self.task_status_uri}",
            params=params,
            headers=headers)
        logging.info(f"request {self.task_status_uri} response:{resp.text}")
        if resp.status_code == 200 and resp.text == "ok":
            return True
        else:
            return False

    @retry_decorator(max_retries=5, delay=2)
    def service_post_request(self, url, body):
        logging.info(f"request {url} with body: {body}")
        for try_time in range(3):
            self.refresh_service_token(try_time>0)
            headers = {'Content-Type': 'application/json', 'token': self.service_token}
            res = requests.post(
                f"{self.conn.host}{url}",
                headers=headers,
                data=json.dumps(body)
            )
            logging.info(f"request {url} response:{res.text}")
            if res.status_code == 200:
                if res.json().get("resultMsg") not in ("token失效，请重新登录", "token can not be blank"):
                    return res.json()
                else:
                    logging.info("token失效，正在重新获取")
                    continue
            else:
                logging.info(f"request {url} failed, {res.text}")
                continue
        return False

    @retry_decorator(max_retries=5, delay=2)
    def service_get_request(self, url, params):
        logging.info(f"request {url} with params: {params}")
        for try_time in range(3):
            self.refresh_service_token(try_time>0)
            headers = {'Content-Type': 'application/json', 'token': self.service_token}
            res = requests.get(
                f"{self.conn.host}{url}",
                headers=headers,
                params=params
            )
            logging.info(f"request {url} response:{res.text}")
            if res.status_code == 200:
                if res.json().get("resultMsg") not in ("token失效，请重新登录", "token can not be blank"):
                    return res.json()
                else:
                    logging.info("token失效，正在重新获取")
                    continue
            else:
                logging.info(f"request {url} failed, {res.text}")
                continue
        return False

    def get_interface_token(self):
        url = f"{self.conn.host}{self.interface_token_uri}"
        headers = {'Content-Type': 'application/json'}
        resp = requests.post(
            url,
            headers=headers,
            data=json.dumps({
                "userName": self.conn.login,
                "password": self.conn.password
            })
        )
        logging.info(resp.text)
        return resp.json().get("data", {}).get("token")

    def update_image_task(self, image, state, build_result):
        data = {"image": image, "state": state, "build_result": build_result}
        token = self.get_interface_token()
        headers = {'Content-Type': 'application/json', 'token': token}
        logging.info(f"request url: {self.conn.host}{self.image_task_uri}")
        logging.info(f"request body: {data}")
        logging.info(f"request headers: {headers}")
        resp = requests.post(
            f"{self.conn.host}{self.image_task_uri}",
            headers=headers,
            data=json.dumps(data)
        )
        logging.info(resp.text)
        if resp.status_code == 200:
            return True
        else:
            return False

    @retry_decorator(max_retries=3, delay=2)
    def interface_post_request(self, url, body):
        token = self.get_interface_token()
        headers = {'Content-Type': 'application/json', 'token': token}
        logging.info(f"request url: {self.conn.host}{url}")
        logging.info(f"request body: {body}")
        resp = requests.post(
            f"{self.conn.host}{url}",
            headers=headers,
            data=json.dumps(body)
        )
        logging.info(resp.text)
        if resp.status_code == 200:
            return True
        else:
            return False
