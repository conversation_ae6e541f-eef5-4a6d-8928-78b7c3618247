from airflow.providers.cncf.kubernetes.operators.pod import KubernetesPodOperator
from airflow.providers.cncf.kubernetes.utils import xcom_sidecar
from airflow.version import version as airflow_version
from airflow.settings import pod_mutation_hook
from airflow.exceptions import AirflowException
from airflow.utils.context import Context
from airflow.models import DagRun
from airflow.utils.state import State
from airflow import settings
from airflow.models.variable import Variable
from kubernetes.client import models as k8s
from kubernetes.client import exceptions
from utils.prometheus_metrics import node_statistics
from utils.k8s_util import get_resource_starving_pods
from utils.schedule_check import check_pod_schedule
from utils.pod_json_parser import PodJsonParser
from hooks.redis_hook import EQRedisLockHook
from datetime import timedelta
from airflow.utils import yaml
from airflow.utils.helpers import prune_dict
import logging
import time
import ast
import json
import copy
import re
from airflow.providers.cncf.kubernetes.callbacks import ExecutionMode
from airflow.providers.cncf.kubernetes.utils.pod_manager import PodPhase, container_is_completed

ENV = Variable.get('ENV', 'dev')

pod_operator_config = Variable.get('pod_operator_config', deserialize_json=True)

def is_valid_pod_name(name: str) -> bool:
    """
    检查字符串是否符合 Kubernetes Pod 名称规范。
    """
    if len(name) > 253:
        return False
    pattern = r'^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
    return bool(re.fullmatch(pattern, name))

def get_node_lock(time_out=7200):
    locker = EQRedisLockHook()
    lock_prefix = pod_operator_config.get("lock_prefix", "CLOSEDLOOP_ELASTIC_GPU_NODE_")
    for part in range(pod_operator_config.get("elastic_gpu_node_count", 10)):
        resource_id = f"{lock_prefix}_{part}"
        res, _ = locker.acquire_lock(resource_id, resource_id, time_out)
        if res:
            return resource_id
    return False

def release_node_lock(resource_id):
    locker = EQRedisLockHook()
    locker.release_lock(resource_id, resource_id)

def adjust_concurrency(add: bool, context: Context, slow_change: bool=True):
    dag_id = context['dag'].dag_id
    run_id = context['ti'].run_id
    logging.info(f"adjust concurrency for dag {dag_id}, run {run_id}")
    # 检查目前的运行和等待的运行数量
    session = settings.Session()
    running_dag_runs = session.query(DagRun).filter(
        DagRun.dag_id == dag_id,
        DagRun.state == State.RUNNING
    ).all()
    queued_dag_runs = session.query(DagRun).filter(
        DagRun.dag_id == dag_id,
        DagRun.state == State.QUEUED
    ).all()
    session.close()
    run_num, queue_num = len(running_dag_runs), len(queued_dag_runs)
    dag_config = Variable.get(dag_id, deserialize_json=True)
    priority_base = int(Variable.get("priority_base"))
    max_active_run = int(dag_config.get("max_active_runs", 4))
    priority = int(dag_config.get("priority", 0))
    max_active_run_upper_limit = 2*(priority_base**priority)
    max_active_run_lower_limit = int(dag_config.get("max_active_run_lower_limit", 4))
    if add:
        if run_num < max_active_run:
            logging.info(f"dag {dag_id} has not reached max_active_run {max_active_run}, continue")
            return
        else:
            if run_num == max_active_run_upper_limit:
                logging.info(f"dag {dag_id} has reached max_active_run_upper_limit {max_active_run}")
                return
            if run_num < max_active_run_upper_limit and queue_num < 5:
                logging.info(f"there is not much the queued dag_runs of dag {dag_id} left")
                return
    else:
        # 慢增快减
        slow_change = False
    locker = EQRedisLockHook()
    lock_uid = f"{dag_id}-{run_id}"
    lock_key = f"airflow.adjust-concurrency.{dag_id}"
    time_out = 600 if slow_change else 10
    lock_acquired, _ = locker.acquire_lock(
        unique_id=lock_uid, lock_key=lock_key, lock_expire_time=time_out)
    if lock_acquired:
        try:
            if add and max_active_run < max_active_run_upper_limit:
                max_active_run = max(max_active_run + 4, max_active_run_upper_limit)
                dag_config["max_active_runs"] = max_active_run
                Variable.set(dag_id, dag_config, serialize_json=True)
            if not add and max_active_run > max_active_run_lower_limit:
                max_active_run //= 2
                max_active_run = max(max_active_run, max_active_run_lower_limit)
                dag_config["max_active_runs"] = max_active_run
                Variable.set(dag_id, dag_config, serialize_json=True)
        except Exception as e:
            logging.error(f"adjust max_active_run failed for dag {dag_id}, run {run_id}")
            logging.error(f"adjust max_active_run failed: {e}")
        finally:
            if not slow_change:
                # 缓慢释放锁，避免频繁修改并发，目前1分钟可以修改一次
                locker.release_lock(unique_id=lock_uid, lock_key=lock_key)

def check_resource_enough(container_resources: k8s.V1ResourceRequirements, context: Context) -> bool:
    dag_id = context['dag'].dag_id
    run_id = context['ti'].run_id
    logging.info(f"check resource enough for dag {dag_id}, run {run_id}")
    if "m" in str(container_resources.requests.get("cpu")):
        cpu = int(container_resources.requests.get("cpu").replace('m', ''))
    else:
        cpu = int(container_resources.requests.get("cpu"))*1000
    if "Mi" in container_resources.requests.get("memory"):
        mem = int(container_resources.requests.get("memory").replace('Mi', ''))*1024*1024
    elif "Gi" in container_resources.requests.get("memory"):
        mem = int(container_resources.requests.get("memory").replace('Gi', ''))*1024*1024*1024
    else:
        logging.error(f"unknown memory unit {container_resources.requests.get('memory')}")
        raise AirflowException("unknown memory unit")
    gpu = int(container_resources.requests.get("nvidia.com/gpu", 0))
    gpu_mem = int(container_resources.requests.get("nvidia.com/gpumem", 0))
    gpu_core = int(container_resources.requests.get("nvidia.com/gpucores", 0))

    can_run_cnt = 0
    node_can_run_cnt = 0
    suggest_node = ""
    # check if there are long pending pods
    pending_pods = get_resource_starving_pods(timeout_minutes=10).values()
    if len(pending_pods) > 0:
        adjust_concurrency(add=False, context=context, slow_change=False)
        logging.info("There are %d long pending pods, decrease concurrency and waiting", len(pending_pods))
        return False, suggest_node
    left_rate = pod_operator_config.get("resource_left_rate", 0.4)
    locker = EQRedisLockHook()
    lock_uid = f"{dag_id}-{run_id}"
    lock_acquired, _ = locker.acquire_lock(unique_id=lock_uid, lock_expire_time=30)
    if lock_acquired:
        try:
            node_status = node_statistics()
            total_cpu = 0
            total_left_cpu = 0
            total_mem = 0
            total_left_mem = 0
            total_gpu = 0
            total_left_gpu = 0
            total_gpu_mem = 0
            total_left_gpu_mem = 0
            total_gpu_core = 0
            total_left_gpu_core = 0
            for node, val in node_status.items():
                left_cpu = val['allocatable_cpu'] - max(val['cpu_requests'], val['cpu_usage'])
                left_mem = val['allocatable_memory'] - max(val['memory_requests'], val['memory_usage'])
                left_gpu = pod_operator_config.get("gpu_num_each_node", 6) - val['gpu_task_num'] if val['gpu_core_total'] > 0 else 0
                left_gpu_mem = val['gpu_memory_total'] - val['gpu_memory_used']
                left_gpu_core = val['gpu_core_total'] - val['gpu_core_used']
                left_cpu = left_cpu if left_cpu > 0 else 0
                left_mem = left_mem if left_mem > 0 else 0
                logging.info(f"node {node} left cpu: {left_cpu}, left memory: {left_mem}, request cpu: {cpu}, request memory: {mem}")
                # if val['gpu_core_total'] > 0:
                logging.info(f"node {node} left gpu core: {left_gpu_core}, left gpu memory: {left_gpu_mem}, " + \
                         f"request gpu: {gpu}, request gpu memory: {gpu_mem}, request gpu core: {gpu_core}")
                if gpu > 0:
                    tmp = min(int(left_cpu/(cpu+1e-7)),
                        int(left_mem/(mem+1e-7)),
                        int(left_gpu/(gpu+1e-7)),
                        int(left_gpu_mem/(gpu_mem+1e-7)),
                        int(left_gpu_core/(gpu_core+1e-7)))
                else:
                    tmp = min(int(left_cpu/(cpu+1e-7)),
                        int(left_mem/(mem+1e-7)))
                can_run_cnt += tmp
                if tmp > node_can_run_cnt:
                    node_can_run_cnt = tmp
                    suggest_node = node
                # total
                total_cpu += val['allocatable_cpu']
                total_mem += val['allocatable_memory']
                total_gpu += pod_operator_config.get("gpu_num_each_node", 6) if val['gpu_core_total'] > 0 else 0
                total_gpu_mem += val['gpu_memory_total']
                total_gpu_core += val['gpu_core_total']
                # left
                total_left_cpu += left_cpu
                total_left_mem += left_mem
                total_left_gpu += left_gpu
                total_left_gpu_mem += left_gpu_mem
                total_left_gpu_core += left_gpu_core
            if can_run_cnt > 2:
                logging.info("can_run_cnt > 2")
                logging.info(f"cpu left rate {total_left_cpu / total_cpu}")
                logging.info(f"mem left rate {total_left_mem / total_mem}")
                logging.info(f"gpu left rate {total_left_gpu / total_gpu}")
                logging.info(f"gpu core left rate {total_left_gpu_core / total_gpu_core}")
                logging.info(f"gpu mem left rate {total_left_gpu_mem / total_gpu_mem}")
                if total_left_cpu/total_cpu > left_rate and total_left_mem/total_mem > left_rate:
                    if (gpu_mem > 0 and total_left_gpu/total_gpu > left_rate and total_left_gpu_mem/total_gpu_mem > \
                    left_rate and total_left_gpu_core/total_gpu_core > left_rate) or gpu_mem == 0:
                        logging.info(f"all resource left rate > {left_rate}, increase concurrency")
                        adjust_concurrency(True, context)
            elif 2 >= can_run_cnt > 0:
                ...
            else:
                logging.info(f"no node can run {dag_id}, decrease concurrency")
                adjust_concurrency(False, context)
        except Exception as e:
            logging.error(f"check resource enough failed: {e}")
        finally:
            locker.release_lock(unique_id=lock_uid)
    logging.info(f"can run task count: {can_run_cnt}")
    return can_run_cnt > 0, suggest_node


class EqPodOperator(KubernetesPodOperator):
    template_fields = KubernetesPodOperator.template_fields + ('resource_json', 'envs_json',)

    def __init__(self,
                 *,
                 cmds: list = [],
                 command: str = "",
                 resource_json: str = "",
                 envs_json: str = "",
                 mount_argo_pps_config: bool = False,
                 load_image_pull_secret: bool = True,
                 test_run: bool = False,
                 check_resource_interval: int = 30,
                 pvc_with_path: dict = {},
                 startup_timeout_seconds: int = 3600,
                 execution_timeout: int = timedelta(hours=2),
                 termination_grace_period_seconds: int = 20,
                 image_pull_policy="IfNotPresent",
                 config_file="~/.kube/config",
                 **kwargs):
        super().__init__(**kwargs)
        if cmds:
            raise AirflowException("cmds is not supported, please use command instead")
        self.cmds = ['/bin/bash', '-c']
        self.arguments = [command]
        self.resource_json = resource_json
        self.envs_json = envs_json
        self.volume_mounts = []
        self.volumes = []
        self.check_resource_interval = check_resource_interval
        self.startup_timeout_seconds = startup_timeout_seconds
        self.execution_timeout = execution_timeout
        self.termination_grace_period_seconds = termination_grace_period_seconds
        self.elastic_gpu = False
        self.image_pull_policy = image_pull_policy
        self.config_file = config_file

        if mount_argo_pps_config:
            self.volume_mounts.append(
                k8s.V1VolumeMount(mount_path='/data/app/config', name='argo-pps-config-volume')
            )
            config_name = 'argo-pps-config-dev' if test_run else 'argo-pps-config'
            self.volumes.append(
                k8s.V1Volume(
                    name='argo-pps-config-volume',
                    config_map=k8s.V1ConfigMapVolumeSource(name=config_name)
                )
            )

        for pvc, path in pvc_with_path.items():
            self.volume_mounts.append(k8s.V1VolumeMount(mount_path=path, name=pvc))
            self.volumes.append(
                k8s.V1Volume(
                    name=pvc,
                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(claim_name=pvc)
                )
            )

        self.image_pull_secrets = []
        if load_image_pull_secret:
            self.image_pull_secrets.append(
                k8s.V1SecretReference(name='docker-registry-secret', namespace='simulation'))
            self.image_pull_secrets.append(
                k8s.V1SecretReference(name='eqr-registry-secret', namespace='simulation'))

    def adjust_resources(self):
        if self.resource_json:
            resources = ast.literal_eval(self.resource_json)
            self.container_resources = k8s.V1ResourceRequirements(
                requests=resources['resource_requests'],
                limits=resources['resource_limits']
            )
            self.log.info(f"resources: {self.container_resources}")
            gpu = int(self.container_resources.requests.get(
                "nvidia.com/gpu", self.container_resources.limits.get("nvidia.com/gpu", 0)))
            gpumem = int(self.container_resources.requests.get(
                "nvidia.com/gpumem", self.container_resources.limits.get("nvidia.com/gpumem", 0)))
            if gpu > 0 and gpumem == 0:
                self.elastic_gpu = True

    def build_pod_request_obj(self, context: Context | None = None) -> k8s.V1Pod:
        if not is_valid_pod_name(self.name):
            raise AirflowException(f"Pod Operator name {self.name} is invalid")
        if self.envs_json:
            try:
                envs = ast.literal_eval(self.envs_json)
                self.env_vars = [k8s.V1EnvVar(name=k, value=v) for k, v in envs.items()]
            except Exception as e:
                self.log.error(f"envs_json is not valid json: {e}")
                self.env_vars = []

        self.log.info(f"envs: {self.env_vars}")
        pod_request_obj = super().build_pod_request_obj(context)
        pod_request_obj.spec.termination_grace_period_seconds = self.termination_grace_period_seconds
        if ENV == "dev":
            pod_request_obj.metadata.name = f"{pod_request_obj.metadata.name}-dev"
        return pod_request_obj

    def execute(self, context: Context):
        self.adjust_resources()
        if not self.elastic_gpu:
            self.pod_request_obj = self.build_pod_request_obj(context)
            logging.info(f"pull policy: {self.pod_request_obj.spec.containers[0].image_pull_policy}")
            logging.info(f"pod: {yaml.dump(prune_dict(self.pod_request_obj.to_dict(), mode='strict'))}")
            check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
            check_cnt = 0
            while not check_res.can_schedule:
                check_cnt += 1
                logging.info(f"can not schedule pod: {"\n" + "\n".join(f"  - {reason}" for reason in check_res.reasons)}")
                logging.info(f"pod: {print(yaml.dump(prune_dict(self.pod_request_obj.to_dict(), mode="strict")))}")
                check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
                time.sleep(self.check_resource_interval)
            logging.info(f"can schedule to node: {', '.join(check_res.schedulable_nodes)}")
            if check_cnt <= 2 and len(check_res.schedulable_nodes) > 1:
                adjust_concurrency(add=True, context=context, slow_change=True)
            else:
                pending_pods = get_resource_starving_pods(timeout_minutes=10).get(self.namespace, [])
                if len(pending_pods) > 0 and check_cnt > 5:
                    adjust_concurrency(add=False, context=context, slow_change=False)
        return super().execute(context)

    def execute_sync(self, context: Context):
        result = None
        try:
            if self.pod_request_obj is None:
                self.pod_request_obj = self.build_pod_request_obj(context)
            logging.info(f"pull policy: {self.pod_request_obj.spec.containers[0].image_pull_policy}")
            for callback in self.callbacks:
                callback.on_pod_manifest_created(
                    pod_request=self.pod_request_obj,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )
            logging.info(f"pull policy: {self.pod_request_obj.spec.containers[0].image_pull_policy}")
            if self.pod is None:
                self.pod = self.get_or_create_pod(  # must set `self.pod` for `on_kill`
                    pod_request_obj=self.pod_request_obj,
                    context=context,
                )
            # push to xcom now so that if there is an error we still have the values
            ti = context["ti"]
            ti.xcom_push(key="pod_name", value=self.pod.metadata.name)
            ti.xcom_push(key="pod_namespace", value=self.pod.metadata.namespace)
            logging.info(f"pull policy: {self.pod.spec.containers[0].image_pull_policy}")
            # get remote pod for use in cleanup methods
            self.remote_pod = self.find_pod(self.pod.metadata.namespace, context=context)
            for callback in self.callbacks:
                callback.on_pod_creation(
                    pod=self.remote_pod,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )

            self.await_init_containers_completion(pod=self.pod)
            logging.info(f"pull policy: {self.pod.spec.containers[0].image_pull_policy}")
            self.await_pod_start(pod=self.pod)
            if self.callbacks:
                pod = self.find_pod(self.pod.metadata.namespace, context=context)
                for callback in self.callbacks:
                    callback.on_pod_starting(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )
            logging.info(f"pull policy: {self.pod.spec.containers[0].image_pull_policy}")
            self.await_pod_completion(pod=self.pod)
            if self.callbacks:
                pod = self.find_pod(self.pod.metadata.namespace, context=context)
                for callback in self.callbacks:
                    callback.on_pod_completion(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )
                for callback in self.callbacks:
                    callback.on_pod_teardown(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )
            logging.info(f"pull policy: {self.pod.spec.containers[0].image_pull_policy}")
            if self.do_xcom_push:
                self.pod_manager.await_xcom_sidecar_container_start(pod=self.pod)
                result = self.extract_xcom(pod=self.pod)
                ti.xcom_push(key='pod_result', value=result)
            istio_enabled = self.is_istio_enabled(self.pod)
            self.remote_pod = self.pod_manager_await_pod_completion(
                self.pod, istio_enabled, self.base_container_name
            )
        except exceptions.ApiException as e:
            logging.info(f"failed with : {e}")
        finally:
            pod_to_clean = self.pod or self.pod_request_obj
            self.cleanup(
                pod=pod_to_clean,
                remote_pod=self.remote_pod,
            )
            for callback in self.callbacks:
                callback.on_pod_cleanup(
                    pod=pod_to_clean,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )

        if self.do_xcom_push:
            return result

    def pod_manager_await_pod_completion(self, pod: k8s.V1Pod, istio_enabled: bool = False, container_name: str = "base",
    ) -> k8s.V1Pod:
        st = time.time()
        while True:
            remote_pod = self.pod_manager.read_pod(pod)
            if remote_pod.status.phase in PodPhase.terminal_states:
                break
            if istio_enabled and container_is_completed(remote_pod, container_name):
                break
            logging.info("Pod %s has phase %s", pod.metadata.name, remote_pod.status.phase)
            time.sleep(2)
            if time.time() - st > 300:
                raise TimeoutError(f"Pod {pod.metadata.name} close timed out")
        return remote_pod


def add_xcom_sidecar(
    pod: k8s.V1Pod,
    *,
    sidecar_container_image: str | None = None,
    sidecar_container_resources: k8s.V1ResourceRequirements | dict | None = None,
) -> k8s.V1Pod:
    # check if sidecar is already added
    for vol in pod.spec.volumes:
        if vol.name == "xcom":
            logging.info("XCom sidecar is already added.")
            return pod
    """Add sidecar."""
    logging.info("Adding XCom sidecar.")
    pod_cp = copy.deepcopy(pod)
    pod_cp.spec.volumes = pod.spec.volumes or []
    pod_cp.spec.volumes.insert(0, xcom_sidecar.PodDefaults.VOLUME)
    for container in pod_cp.spec.containers:
        container.volume_mounts = container.volume_mounts or []
        container.volume_mounts.insert(0, xcom_sidecar.PodDefaults.VOLUME_MOUNT)
    sidecar = copy.deepcopy(xcom_sidecar.PodDefaults.SIDECAR_CONTAINER)
    sidecar.image = sidecar_container_image or xcom_sidecar.PodDefaults.SIDECAR_CONTAINER.image
    if sidecar_container_resources:
        sidecar.resources = sidecar_container_resources
    pod_cp.spec.containers.append(sidecar)

    return pod_cp


class MultiContainerPodOperator(KubernetesPodOperator):

    template_fields = KubernetesPodOperator.template_fields + \
        ('context_json', )
    def __init__(self,
                 *,
                 name:str="",
                 elastic_node:bool=False,
                 startup_timeout_seconds:int=3600,
                 check_resource_interval: int = 10,
                 context_json:str="",
                 base_container_name:str="sim-server",
                 termination_grace_period_seconds:int=20,
                 use_gpu:bool=False,
                 **kwargs):
        self.context_json = context_json
        self.elastic_node = elastic_node
        self.check_resource_interval = check_resource_interval
        super().__init__(**kwargs)
        self.name = name
        self.startup_timeout_seconds = startup_timeout_seconds
        self.base_container_name = base_container_name
        self.container_logs = self.base_container_name
        self.termination_grace_period_seconds = termination_grace_period_seconds
        self.use_gpu = use_gpu
        self.image_pull_secrets.append(
                k8s.V1SecretReference(name='docker-registry-secret', namespace='simulation'))
        self.image_pull_secrets.append(
                k8s.V1SecretReference(name='eqr-registry-secret', namespace='simulation'))

    def parse_context(self):
        logging.info(f'Parsing context json: {self.context_json}')
        self.pod_config = PodJsonParser(self.context_json)
        logging.info(f'pod containers_config: {self.pod_config.containers_config}')
        logging.info(f'Pod init_containers_config: {self.pod_config.init_containers_config}')
        logging.info(f'Pod spec_config: {self.pod_config.pod_spec_config}')
        self.containers = []
        for container_config in self.pod_config.containers_config:
            container = k8s.V1Container(
                name=container_config["name"],
                image=container_config["image"],
                image_pull_policy=container_config.get("image_pull_policy"),
                command=container_config.get("command"),
                args=container_config.get("args"),
                env=[k8s.V1EnvVar(name=env["name"], value=env["value"])
                     for env in container_config.get("env", [])],
                resources=k8s.V1ResourceRequirements(
                    requests=container_config.get("resources", {}).get("requests"),
                    limits=container_config.get("resources", {}).get("limits")
                ),
                volume_mounts=[k8s.V1VolumeMount(
                    name=vm["name"],
                    mount_path=vm["mount_path"]
                ) for vm in container_config.get("volume_mounts", [])]
            )
            self.containers.append(container)
        self.init_containers = []
        for init_config in self.pod_config.init_containers_config:
            init_container = k8s.V1Container(
                name=init_config["name"],
                image=init_config["image"],
                image_pull_policy=init_config.get("image_pull_policy"),
                command=init_config.get("command"),
                args=init_config.get("args"),
                env=[k8s.V1EnvVar(name=env["name"], value=env["value"])
                     for env in init_config.get("env", [])],
                resources=k8s.V1ResourceRequirements(
                    requests=init_config.get("resources", {}).get("requests"),
                    limits=init_config.get("resources", {}).get("limits")
                ),
                volume_mounts=[k8s.V1VolumeMount(
                    name=vm["name"],
                    mount_path=vm["mount_path"]
                ) for vm in init_config.get("volume_mounts", [])]
            )
            self.init_containers.append(init_container)
        self.volumes = []
        for volume_config in self.pod_config.pod_spec_config.get("volumes", []):
            volume = k8s.V1Volume(name=volume_config["name"])

            if "empty_dir" in volume_config:
                empty_dir_config = volume_config["empty_dir"]
                volume.empty_dir = k8s.V1EmptyDirVolumeSource(
                    medium=empty_dir_config.get("medium"),
                    size_limit=empty_dir_config.get("size_limit")
                )
            elif "config_map" in volume_config:
                config_map_config = volume_config["config_map"]
                volume.config_map = k8s.V1ConfigMapVolumeSource(
                    name=config_map_config["name"],
                    default_mode=config_map_config.get("default_mode"),
                    optional=config_map_config.get("optional")
                )

            self.volumes.append(volume)

    def build_pod_request_obj(self, context: Context | None = None) -> k8s.V1Pod:
        self.parse_context()
        if not is_valid_pod_name(self.name):
            raise AirflowException(f"Pod Operator name {self.name} is invalid")
        if self.elastic_node:
            if self.use_gpu:
                self.labels["hami.io/webhook"] = "ignore"
                self.annotations = {}
                self.annotations["vci.vke.volcengine.com/auto-imc-disk-size"] = "120"
                self.annotations["vci.vke.volcengine.com/desired-system-storage"] = "240"
                self.annotations["vke.volcengine.com/burst-to-vci"] = "enforce"
                self.annotations["vci.vke.volcengine.com/preferred-instance-family"] = "vci.gni3"
                self.annotations["vci.vke.volcengine.com/enable-auto-create-imc"] = "true"
                self.annotations["vci.vke.volcengine.com/gpu-driver-version"] = "tesla-535.161.07"
                logging.info(f"annotations: {self.annotations}")
                for container in self.containers:
                    if container.name == "alg-server":
                        container.resources.limits.pop("nvidia.com/gpumem", None)
                        container.resources.limits.pop("nvidia.com/gpucores", None)
                        container.resources.requests.pop("nvidia.com/gpumem", None)
                        container.resources.requests.pop("nvidia.com/gpucores", None)
        else:
            # self.labels["hami.io/webhook"] = "ignore"
            # remove annotations, do not schedule on elastic nodes
            if self.use_gpu:
                self.annotations = {}
                for container in self.containers:
                    if container.name == "alg-server":
                        container.resources.limits["nvidia.com/gpumem"] = 4500
                        container.resources.limits["nvidia.com/gpucores"] = 33
                        container.resources.limits["nvidia.com/gpu"] = 1
                        container.resources.requests["nvidia.com/gpumem"] = 4500
                        container.resources.requests["nvidia.com/gpucores"] = 33
                        container.resources.requests["nvidia.com/gpu"] = 1

        pod = k8s.V1Pod(
            api_version="v1",
            kind="Pod",
            metadata=k8s.V1ObjectMeta(
                namespace=self.pod_config.metadata_config.get("namespace", self.namespace),
                labels=self.labels,
                name=self.name if self.name != "" else self.pod_config.metadata_config.get("name"),
                annotations=self.annotations,
            ),
            spec=k8s.V1PodSpec(
                node_selector=self.pod_config.pod_spec_config.get("node_selector"),
                affinity=self.pod_config.pod_spec_config.get("affinity"),
                tolerations=self.pod_config.pod_spec_config.get("tolerations"),
                init_containers=self.init_containers,
                host_aliases=self.host_aliases,
                containers=self.containers,
                image_pull_secrets=self.image_pull_secrets,
                service_account_name=self.service_account_name,
                host_network=self.hostnetwork,
                hostname=self.hostname,
                subdomain=self.subdomain,
                security_context=self.security_context,
                dns_policy=self.dnspolicy,
                dns_config=self.dns_config,
                scheduler_name=self.schedulername,
                restart_policy="Never",
                priority_class_name=self.priority_class_name,
                volumes=self.volumes,
                active_deadline_seconds=self.active_deadline_seconds,
                termination_grace_period_seconds=self.termination_grace_period_seconds
            ),
        )
        # logging.info(f"create pod: {json.dumps(pod.to_dict())}")
        if self.do_xcom_push:
            logging.info("Adding xcom sidecar to task %s", self.task_id)
            pod = add_xcom_sidecar(
                pod,
                sidecar_container_image=self.hook.get_xcom_sidecar_container_image(),
                sidecar_container_resources=self.hook.get_xcom_sidecar_container_resources(),
            )

        labels = self._get_ti_pod_labels(context)
        self.log.info("Building pod %s with labels: %s", pod.metadata.name, labels)

        # Merge Pod Identifying labels with labels passed to operator
        pod.metadata.labels.update(labels)
        # Add Airflow Version to the label
        # And a label to identify that pod is launched by KubernetesPodOperator
        pod.metadata.labels.update(
            {
                "airflow_version": airflow_version.replace("+", "-"),
                "airflow_kpo_in_cluster": str(self.hook.is_in_cluster),
            }
        )
        pod_mutation_hook(pod)
        logging.info(f"create pod: {json.dumps(pod.to_dict())}")
        return pod

    def execute(self, context: Context):
        lock_id = None
        logging.info(f"container_logs is {self.container_logs}")
        self.pod_request_obj = self.build_pod_request_obj(context=context)
        if not self.use_gpu:
            self.pod_request_obj = self.build_pod_request_obj(context)
            check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
            check_cnt = 0
            while not check_res.can_schedule:
                check_cnt += 1
                logging.info(f"can not schedule pod: {"\n" + "\n".join(f"  - {reason}" for reason in check_res.reasons)}")
                check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
                time.sleep(self.check_resource_interval)
            logging.info(f"can schedule to node: {', '.join(check_res.schedulable_nodes)}")
            if check_cnt <= 2 and len(check_res.schedulable_nodes) > 1:
                adjust_concurrency(add=True, context=context, slow_change=True)
            else:
                pending_pods = get_resource_starving_pods(timeout_minutes=10).get(self.namespace, [])
                if len(pending_pods) > 0 and check_cnt > 5:
                    adjust_concurrency(add=False, context=context, slow_change=False)
        else:
            self.elastic_node = False
            self.pod_request_obj = self.build_pod_request_obj(context)
            check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
            check_cnt = 0
            while not check_res.can_schedule and check_cnt < pod_operator_config.get("gpu_check_try_count", 6):
                check_cnt += 1
                logging.info(f"can not schedule pod: {"\n" + "\n".join(f"  - {reason}" for reason in check_res.reasons)}")
                check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
                time.sleep(self.check_resource_interval)
            logging.info(f"can schedule to node: {', '.join(check_res.schedulable_nodes)}")
            if not check_res.can_schedule:
                self.elastic_node = True
                while True:
                    lock_id = get_node_lock()
                    check_cnt += 1
                    if lock_id:
                        self.pod_request_obj = self.build_pod_request_obj(context=context)
                        self.log.info(f"lock a elastic node")
                        break
                    time.sleep(self.check_resource_interval)
                pending_pods = get_resource_starving_pods(timeout_minutes=10).get(self.namespace, [])
                if len(pending_pods) > 0 or check_cnt > 60:
                    adjust_concurrency(add=False, context=context, slow_change=False)
                elif len(pending_pods) == 0 or check_cnt < 6:
                    adjust_concurrency(add=True, context=context, slow_change=True)
        try:
            return super().execute(context)
        except exceptions.ApiException as e:
            logging.info(f"failed with : {e}")
        finally:
            if lock_id:
                release_node_lock(lock_id)

    def execute_sync(self, context: Context):
        result = None
        try:
            if self.pod_request_obj is None:
                self.pod_request_obj = self.build_pod_request_obj(context)
            for callback in self.callbacks:
                callback.on_pod_manifest_created(
                    pod_request=self.pod_request_obj,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )
            if self.pod is None:
                self.pod = self.get_or_create_pod(  # must set `self.pod` for `on_kill`
                    pod_request_obj=self.pod_request_obj,
                    context=context,
                )
            # push to xcom now so that if there is an error we still have the values
            ti = context["ti"]
            ti.xcom_push(key="pod_name", value=self.pod.metadata.name)
            ti.xcom_push(key="pod_namespace", value=self.pod.metadata.namespace)

            # get remote pod for use in cleanup methods
            self.remote_pod = self.find_pod(self.pod.metadata.namespace, context=context)
            for callback in self.callbacks:
                callback.on_pod_creation(
                    pod=self.remote_pod,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )

            self.await_init_containers_completion(pod=self.pod)

            self.await_pod_start(pod=self.pod)
            if self.callbacks:
                pod = self.find_pod(self.pod.metadata.namespace, context=context)
                for callback in self.callbacks:
                    callback.on_pod_starting(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )

            self.await_pod_completion(pod=self.pod)
            if self.callbacks:
                pod = self.find_pod(self.pod.metadata.namespace, context=context)
                for callback in self.callbacks:
                    callback.on_pod_completion(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )
                for callback in self.callbacks:
                    callback.on_pod_teardown(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )

            if self.do_xcom_push:
                self.pod_manager.await_xcom_sidecar_container_start(pod=self.pod)
                result = self.extract_xcom(pod=self.pod)
                ti.xcom_push(key='pod_result', value=result)
            istio_enabled = self.is_istio_enabled(self.pod)
            self.remote_pod = self.pod_manager_await_pod_completion(
                self.pod, istio_enabled, self.base_container_name
            )
        except exceptions.ApiException as e:
            logging.info(f"failed with : {e}")
        finally:
            pod_to_clean = self.pod or self.pod_request_obj
            self.cleanup(
                pod=pod_to_clean,
                remote_pod=self.remote_pod,
            )
            for callback in self.callbacks:
                callback.on_pod_cleanup(
                    pod=pod_to_clean,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )

        if self.do_xcom_push:
            return result

    def pod_manager_await_pod_completion(self, pod: k8s.V1Pod, istio_enabled: bool = False, container_name: str = "base",
    ) -> k8s.V1Pod:
        st = time.time()
        while True:
            remote_pod = self.pod_manager.read_pod(pod)
            if remote_pod.status.phase in PodPhase.terminal_states:
                break
            if istio_enabled and container_is_completed(remote_pod, container_name):
                break
            logging.info("Pod %s has phase %s", pod.metadata.name, remote_pod.status.phase)
            time.sleep(2)
            if time.time() - st > 300:
                raise TimeoutError(f"Pod {pod.metadata.name} close timed out")
        return remote_pod
