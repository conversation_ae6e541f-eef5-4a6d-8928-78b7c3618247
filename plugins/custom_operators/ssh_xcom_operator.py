from airflow.providers.ssh.operators.ssh import SSHOperator
from base64 import b64encode
from select import select
import paramiko
from airflow.configuration import conf
from airflow.exceptions import AirflowException, AirflowSkipException
from airflow.utils.types import NOTSET, ArgNotSet
import logging

from paramiko.client import SSHClient

CMD_TIMEOUT = 10

class SSHXComOperator(SSHOperator):

    def __init__(self,
        conn_timeout:int=36000,
        cmd_timeout:int=36000,
        *args,
        **kwargs):
        super().__init__(conn_timeout=conn_timeout, cmd_timeout=cmd_timeout, *args, **kwargs)

    def exec_ssh_client_command(
        self,
        ssh_client: paramiko.SSHClient,
        command: str,
        get_pty: bool,
        environment: dict | None,
        timeout: float | ArgNotSet | None = NOTSET,
    ) -> tuple[int, bytes, bytes]:
        self.log.info("Running command: %s", command)

        cmd_timeout: float | None
        if not isinstance(timeout, ArgNotSet):
            cmd_timeout = timeout
        elif not isinstance(self.cmd_timeout, ArgNotSet):
            cmd_timeout = self.cmd_timeout
        else:
            cmd_timeout = CMD_TIMEOUT
        del timeout  # Too easy to confuse with "timedout" below.

        # set timeout taken as params
        stdin, stdout, stderr = ssh_client.exec_command(
            command=command,
            get_pty=get_pty,
            timeout=cmd_timeout,
            environment=environment,
        )
        # get channels
        channel = stdout.channel

        # closing stdin
        stdin.close()
        channel.shutdown_write()

        agg_stdout = ""
        agg_stderr = ""

        # capture any initial output in case channel is closed already
        stdout_buffer_length = len(stdout.channel.in_buffer)

        if stdout_buffer_length > 0:
            agg_stdout += stdout.channel.recv(stdout_buffer_length).decode("utf-8", "replace").strip("\n")

        timedout = False

        # read from both stdout and stderr
        while not channel.closed or channel.recv_ready() or channel.recv_stderr_ready():
            readq, _, _ = select([channel], [], [], cmd_timeout)
            if cmd_timeout is not None:
                timedout = not readq
            for recv in readq:
                if recv.recv_ready():
                    output = stdout.channel.recv(len(recv.in_buffer)).decode("utf-8", "replace").strip("\n")
                    agg_stdout += output
                    for line in output.splitlines():
                        self.log.info(line)
                if recv.recv_stderr_ready():
                    output = stderr.channel.recv_stderr(len(recv.in_stderr_buffer)).decode("utf-8", "replace").strip("\n")
                    agg_stderr += output
                    for line in output.splitlines():
                        self.log.warning(line)
            if (
                stdout.channel.exit_status_ready()
                and not stderr.channel.recv_stderr_ready()
                and not stdout.channel.recv_ready()
            ) or timedout:
                stdout.channel.shutdown_read()
                try:
                    stdout.channel.close()
                except Exception:
                    # there is a race that when shutdown_read has been called and when
                    # you try to close the connection, the socket is already closed
                    # We should ignore such errors (but we should log them with warning)
                    self.log.warning("Ignoring exception on close", exc_info=True)
                break

        stdout.close()
        stderr.close()

        if timedout:
            raise AirflowException("SSH command timed out")

        exit_status = stdout.channel.recv_exit_status()

        return exit_status, agg_stdout, agg_stderr

    def raise_for_status(self, exit_status: int, stderr: bytes, context=None) -> None:
        if context and self.do_xcom_push:
            ti = context.get("task_instance")
            ti.xcom_push(key="ssh_exit", value=exit_status)
        if exit_status in self.skip_on_exit_code:
            raise AirflowSkipException(f"SSH command returned exit code {exit_status}. Skipping.")
        if exit_status != 0:
            logging.error(f"SSH operator error: exit status = {exit_status}")

    def run_ssh_client_command(self, ssh_client: SSHClient, command: str, context=None) -> bytes:
        exit_status, agg_stdout, agg_stderr = self.hook.exec_ssh_client_command(
            ssh_client, command, timeout=self.cmd_timeout, environment=self.environment, get_pty=self.get_pty
        )
        self.raise_for_status(exit_status, agg_stderr, context=context)
        return agg_stdout

    def execute(self, context=None) -> bytes | str:
        result: bytes | str
        if self.command is None:
            raise AirflowException("SSH operator error: SSH command not specified. Aborting.")

        # Forcing get_pty to True if the command begins with "sudo".
        self.get_pty = self.command.startswith("sudo") or self.get_pty

        with self.get_ssh_client() as ssh_client:
            result = self.run_ssh_client_command(ssh_client, self.command, context=context)

        if len(result) > 10000:
            logging.warning(f"SSH operator result is too large ({len(result)}). Truncating to 10000.")
            result = result[-10000:]

        return {"return": result.decode('utf-8')}
