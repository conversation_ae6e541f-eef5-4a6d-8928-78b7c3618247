from kubernetes.client import models as k8s
from kubernetes.client import exceptions
from airflow.utils.context import Context
from airflow.exceptions import AirflowException
from airflow.models.variable import Variable
from custom_operators.pod_operator import adjust_concurrency
from hooks.redis_hook import EQRedisLockHook
from airflow.providers.cncf.kubernetes.operators.pod import KubernetesPodOperator
from airflow.providers.cncf.kubernetes.utils import xcom_sidecar
from airflow.version import version as airflow_version
from airflow.settings import pod_mutation_hook
from utils.k8s_util import create_affinity, AffinityItem
import json
import time
import logging
import yaml
from datetime import timedelta
from utils.k8s_util import get_resource_starving_pods
from utils.schedule_check import check_pod_schedule
from airflow.providers.cncf.kubernetes.utils.pod_manager import PodLaunchFailedException, PodPhase, container_is_completed
from airflow.providers.cncf.kubernetes.callbacks import ExecutionMode

OPERATOR_CONFIG = Variable.get("eq_job_operator_config", deserialize_json=True)
ENV = Variable.get('ENV', 'dev')

def get_node_lock(time_out=7200):
    locker = EQRedisLockHook()
    lock_prefix = OPERATOR_CONFIG.get("lock_prefix", "CLOSEDLOOP_ELASTIC_GPU_NODE_")
    for part in range(OPERATOR_CONFIG.get("elastic_gpu_node_count", 10)):
        resource_id = f"{lock_prefix}_{part}"
        res, _ = locker.acquire_lock(resource_id, resource_id, time_out)
        if res:
            return resource_id
    return False

def release_node_lock(resource_id):
    locker = EQRedisLockHook()
    locker.release_lock(resource_id, resource_id)

class RESTResponseJob:
    """deserialize only received a RESTResponse object,
    the data of the object is a json string
    """
    def __init__(self, v1_job_json: str):
        self.data = v1_job_json

class EqJobOperator(KubernetesPodOperator):

    template_fields = KubernetesPodOperator.template_fields + ('v1_job_json',)
    def __init__(self,
                 v1_job_json: str,
                 ttl_seconds_after_finished: int=120,
                 gpu: int=0,
                 gpu_mem: int=0,
                 gpu_core: int=0,
                 config_file: str="~/.kube/config",
                 increase_resource: dict={},
                 startup_timeout_seconds:int=7200,
                 execution_timeout:int=timedelta(hours=2),
                 check_resource_interval:int=30,
                 termination_grace_period_seconds:int=20,
                 *args,
                 **kwargs):
        self.config_file = config_file
        super().__init__(*args, **kwargs)
        self.v1_job_json = v1_job_json
        self.base_container_name = "sim-server"
        self.container_logs = self.base_container_name
        self.ttl_seconds_after_finished = ttl_seconds_after_finished
        self.increase_resource = increase_resource
        self.startup_timeout_seconds = startup_timeout_seconds
        self.execution_timeout = execution_timeout
        self.check_resource_interval = check_resource_interval
        self.termination_grace_period_seconds = termination_grace_period_seconds
        self.tolerations = []
        self.affinity = create_affinity([
            AffinityItem("node", "In", ["cpu"], 60),
            AffinityItem("gpu", "In", ["on"], 40)
        ])
        self.gpu = gpu
        self.gpu_mem = gpu_mem
        self.gpu_core = gpu_core
        if self.gpu:
            self.affinity = create_affinity([
                AffinityItem("gpu", "In", ["on"], 100)
            ])
            self.tolerations.append(k8s.V1Toleration(
                effect="NoSchedule",
                key="node",
                operator="Equal",
                value="gpu",
            ))
        self.xcom_added = False

    def format_container_resources(self, container: k8s.V1Container) -> dict:
        # this methon only can process resources from java
        resources = container.resources
        if resources is None:
            return
        max_resources = {}
        increase_memory = self.increase_resource.get("memory", 0)
        increase_cpu = self.increase_resource.get("cpu", 0)
        increase_gpu = self.increase_resource.get("gpu", 0)
        increase_gpumem = self.increase_resource.get("gpumem", 0)
        increase_gpucore = self.increase_resource.get("gpucore", 0)
        limits_memory = resources.limits.get('memory')
        if isinstance(limits_memory, str):
            try:
                limits_memory = json.loads(limits_memory.replace("'", '"'))
            except Exception as e:
                raise e
            if limits_memory.get("format") == "BINARY_SI":
                limits_memory = int(limits_memory.get('number')/1024/1024)
                resources.limits['memory'] = f"{limits_memory+increase_memory}Mi"
            elif limits_memory.get("format") == "DECIMAL_SI":
                limits_memory = int(limits_memory.get('number')/1000/1000)
                resources.limits['memory'] = f"{limits_memory+increase_memory}M"
            else:
                raise AirflowException(f"Unsupported memory format, {limits_memory}")

        limits_cpu = resources.limits.get('cpu')
        if isinstance(limits_cpu, str):
            try:
                limits_cpu = json.loads(limits_cpu.replace("'", '"'))
            except Exception as e:
                raise e
            if limits_cpu.get("format") == "DECIMAL_SI":
                limits_cpu = int(limits_cpu.get('number')*1000)
                resources.limits['cpu'] = f"{limits_cpu+increase_cpu}m"
            else:
                raise AirflowException(f"Unsupported cpu format, {limits_cpu}")

        limit_gpu = resources.limits.get('nvidia.com/gpu')
        if isinstance(limit_gpu, str):
            try:
                limit_gpu = json.loads(limit_gpu.replace("'", '"'))
            except Exception as e:
                raise e
            if limit_gpu.get("format") == "DECIMAL_SI":
                limit_gpu = int(limit_gpu.get('number'))
                resources.limits['nvidia.com/gpu'] = f"{limit_gpu+increase_gpu}"
            else:
                raise AirflowException(f"Unsupported gpu format, {limit_gpu}")
            max_resources['nvidia.com/gpu'] = limit_gpu + increase_gpu
            max_resources['nvidia.com/gpumem'] = self.gpu_mem + increase_gpumem
            max_resources['nvidia.com/gpucores'] = self.gpu_core + increase_gpucore

        requests_memory = resources.requests.get('memory')
        if isinstance(requests_memory, str):
            try:
                requests_memory = json.loads(requests_memory.replace("'", '"'))
            except Exception as e:
                raise e
            if requests_memory.get("format") == "BINARY_SI":
                requests_memory = int(requests_memory.get('number')/1024/1024)
                resources.requests['memory'] = f"{requests_memory}Mi"
            elif requests_memory.get("format") == "DECIMAL_SI":
                requests_memory = int(requests_memory.get('number')/1000/1000)
                resources.requests['memory'] = f"{requests_memory}M"
            else:
                raise AirflowException(f"Unsupported memory format, {requests_memory}")
            max_resources['memory'] = requests_memory

        requests_cpu = resources.requests.get('cpu')
        if isinstance(requests_cpu, str):
            try:
                requests_cpu = json.loads(requests_cpu.replace("'", '"'))
            except Exception as e:
                raise e
            if requests_cpu.get("format") == "DECIMAL_SI":
                requests_cpu = int(requests_cpu.get('number')*1000)
                # resources.requests['cpu'] = f"{requests_cpu}m"
                resources.requests['cpu'] = f"{requests_cpu}m" if self.gpu else f"{950}m"
            else:
                raise AirflowException(f"Unsupported cpu format, {requests_cpu}")
            max_resources['cpu'] = requests_cpu if self.gpu else 950

        requests_gpu = resources.requests.get('nvidia.com/gpu')
        if isinstance(requests_gpu, str):
            try:
                requests_gpu = json.loads(requests_gpu.replace("'", '"'))
            except Exception as e:
                raise e
            if requests_gpu.get("format") == "DECIMAL_SI":
                requests_gpu = int(requests_gpu.get('number'))
                resources.requests['nvidia.com/gpu'] = f"{requests_gpu+increase_gpu}"
            else:
                raise AirflowException(f"Unsupported gpu format, {requests_gpu}")
        return max_resources

    def update_container_resource(self, container: k8s.V1Container, elastic_node:bool=False):
        resources = container.resources
        if resources is None or resources.limits is None:
            return
        limit_gpu = resources.limits.get('nvidia.com/gpu')
        if limit_gpu:
            increase_gpumem = self.increase_resource.get("gpumem", 0)
            increase_gpucore = self.increase_resource.get("gpucore", 0)
            if self.gpu_mem and not elastic_node:
                # 固定GPU节点限制显存和显核使用
                resources.requests['nvidia.com/gpumem'] = self.gpu_mem + increase_gpumem
                resources.requests['nvidia.com/gpucores'] = self.gpu_core + increase_gpucore
                resources.limits['nvidia.com/gpumem'] = self.gpu_mem + increase_gpumem
                resources.limits['nvidia.com/gpucores'] = self.gpu_core + increase_gpucore
            if elastic_node:
                resources.requests.pop('nvidia.com/gpumem', None)
                resources.requests.pop('nvidia.com/gpucores', None)
                resources.limits.pop('nvidia.com/gpumem', None)
                resources.limits.pop('nvidia.com/gpucores', None)

    def format_volume(self, volumes: k8s.V1Volume):
        empty_dir = volumes.empty_dir
        if empty_dir is None:
            return
        if isinstance(empty_dir.size_limit, str):
            try:
                size_limit = json.loads(empty_dir.size_limit.replace("'", '"'))
                if size_limit.get("format") == "BINARY_SI":
                    size_limit = int(size_limit.get('number')/1024/1024)
                    empty_dir.size_limit = f"{size_limit}Mi"
                elif size_limit.get("format") == "DECIMAL_SI":
                    size_limit = int(size_limit.get('number')/1000/1000)
                    empty_dir.size_limit = f"{size_limit}M"
                else:
                    raise AirflowException(f"Unsupported memory format, {size_limit}")
                logging.info(empty_dir.size_limit)
            except Exception as e:
                raise e

    def check_fix_job(self, v1_job: k8s.V1Job):
        v1_job.api_version = "batch/v1"
        pod_spec = v1_job.spec.template.spec
        total_resources = {"memory": 0, "cpu": 0}
        for container in pod_spec.containers:
            resource = self.format_container_resources(container)
            total_resources['memory'] += resource.get('memory')
            total_resources['cpu'] += resource.get('cpu')
            if resource.get('nvidia.com/gpu'):
                total_resources['nvidia.com/gpu'] = resource.get('nvidia.com/gpu')
                total_resources['nvidia.com/gpumem'] = resource.get('nvidia.com/gpumem')
                total_resources['nvidia.com/gpucores'] = resource.get('nvidia.com/gpucores')
        total_resources["cpu"] = f"{total_resources.get('cpu')}m"
        total_resources["memory"] = f"{total_resources.get('memory')}Mi"
        self.max_need_resources = k8s.V1ResourceRequirements(
            requests=total_resources
        )
        for container in pod_spec.init_containers:
            self.format_container_resources(container)
        for volume in pod_spec.volumes:
            self.format_volume(volume)

    def parse_json_to_v1_job(self, v1_job_json: str) -> k8s.V1Job:
        client = self.hook.get_conn()
        # self.log.info(f"parse_json_to_v1_job: {v1_job_json}")
        eq_job = RESTResponseJob(v1_job_json)
        try:
            v1_job = client.deserialize(eq_job, k8s.V1Job)
            self.check_fix_job(v1_job)
            v1_job.spec.template.metadata.namespace = v1_job.metadata.namespace
            self.namespace = v1_job.metadata.namespace
            self.pod_request_obj = v1_job.spec.template
            # logging.info(f"parse_json_to_v1_job: {json.dumps(v1_job.to_dict())}")
            self.job_request_obj = v1_job
            return v1_job
        except Exception as e:
            raise e

    def build_job_request_obj(self) -> k8s.V1Job:
        return self.parse_json_to_v1_job(self.v1_job_json)

    def build_pod_request_obj(self, elastic_node:bool=True, context=None):
        if elastic_node:
            self.job_request_obj.spec.template.metadata.labels["hami.io/webhook"] = "ignore"
            self.labels = self.job_request_obj.spec.template.metadata.labels
            self.annotations = self.job_request_obj.spec.template.metadata.annotations
            self.annotations["vci.vke.volcengine.com/auto-imc-disk-size"] = "120"
            self.annotations["vci.vke.volcengine.com/desired-system-storage"] = "240"
            logging.info(f"type of annotations: {self.annotations}")
            self.affinity = None
            for c in self.pod_request_obj.spec.containers:
                self.update_container_resource(c, elastic_node=True)
        else:
            # remove annotations, do not schedule on elastic nodes
            self.annotations = {}
            for c in self.pod_request_obj.spec.containers:
                self.update_container_resource(c, elastic_node=False)
        pod = k8s.V1Pod(
            api_version="v1",
            kind="Pod",
            metadata=k8s.V1ObjectMeta(
                namespace=self.pod_request_obj.metadata.namespace,
                labels=self.labels,
                name=self.name,
                annotations=self.annotations,
            ),
            spec=k8s.V1PodSpec(
                node_selector=self.node_selector,
                affinity=self.affinity,
                tolerations=self.tolerations,
                init_containers=self.pod_request_obj.spec.init_containers,
                host_aliases=self.host_aliases,
                containers=self.pod_request_obj.spec.containers,
                image_pull_secrets=self.pod_request_obj.spec.image_pull_secrets,
                service_account_name=self.service_account_name,
                host_network=self.hostnetwork,
                hostname=self.hostname,
                subdomain=self.subdomain,
                security_context=self.security_context,
                dns_policy=self.dnspolicy,
                dns_config=self.dns_config,
                scheduler_name=self.schedulername,
                restart_policy="Never",
                priority_class_name=self.priority_class_name,
                volumes=self.pod_request_obj.spec.volumes,
                active_deadline_seconds=self.active_deadline_seconds,
                termination_grace_period_seconds=self.termination_grace_period_seconds,
            ),
        )
        if self.do_xcom_push and not self.xcom_added:
            self.log.debug("Adding xcom sidecar to task %s", self.task_id)
            pod = xcom_sidecar.add_xcom_sidecar(
                pod,
                sidecar_container_image=self.hook.get_xcom_sidecar_container_image(),
                sidecar_container_resources=self.hook.get_xcom_sidecar_container_resources(),
            )
            self.xcom_added = True

        labels = self._get_ti_pod_labels(context)
        self.log.info("Building pod %s with labels: %s", pod.metadata.name, labels)

        # Merge Pod Identifying labels with labels passed to operator
        pod.metadata.labels.update(labels)
        # Add Airflow Version to the label
        # And a label to identify that pod is launched by KubernetesPodOperator
        pod.metadata.labels.update(
            {
                "airflow_version": airflow_version.replace("+", "-"),
                "airflow_kpo_in_cluster": str(self.hook.is_in_cluster),
            }
        )
        pod_mutation_hook(pod)
        logging.info(f"create pod: {json.dumps(pod.to_dict())}")
        return pod

    def await_pod_start(self, pod: k8s.V1Pod) -> None:
        try:
            # logging.info(f"wait pod: {json.dumps(pod.to_dict())}")
            self.pod_manager.await_pod_start(
                pod=pod,
                startup_timeout=self.startup_timeout_seconds,
                startup_check_interval=self.startup_check_interval_seconds,
            )
        except PodLaunchFailedException:
            if self.log_events_on_failure:
                self._read_pod_events(pod, reraise=False)
            raise

    def execute(self, context: Context):
        self.job_request_obj = self.build_job_request_obj()
        env_suffix = "-dev" if ENV == "dev" else ""
        self.name = self.job_request_obj.metadata.name+env_suffix
        lock_id = None
        if self.gpu:
            self.pod_request_obj = self.build_pod_request_obj(elastic_node=False, context=context)
            check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
            check_cnt = 0
            while not check_res.can_schedule and check_cnt < OPERATOR_CONFIG.get("gpu_check_try_count", 6):
                check_cnt += 1
                logging.info(f"can not schedule pod: {"\n" + "\n".join(f"  - {reason}" for reason in check_res.reasons)}")
                check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
                time.sleep(self.check_resource_interval)
            if not check_res.can_schedule:
                while True:
                    lock_id = get_node_lock()
                    check_cnt += 1
                    if lock_id:
                        self.pod_request_obj = self.build_pod_request_obj(elastic_node=True, context=context)
                        self.log.info(f"lock a elastic node")
                        break
                    time.sleep(self.check_resource_interval)
                pending_pods = get_resource_starving_pods(timeout_minutes=10).get(self.namespace, [])
                if len(pending_pods) > 0 or check_cnt > 60:
                    adjust_concurrency(add=False, context=context, slow_change=False)
                elif len(pending_pods) == 0 or check_cnt < 6:
                    adjust_concurrency(add=True, context=context, slow_change=True)
        else:
            self.pod_request_obj = self.build_pod_request_obj(elastic_node=False, context=context)
            check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
            check_cnt = 0
            while not check_res.can_schedule:
                check_cnt += 1
                logging.info(f"can not schedule pod: {"\n" + "\n".join(f"  - {reason}" for reason in check_res.reasons)}")
                check_res = check_pod_schedule(self.pod_request_obj, self.namespace)
                time.sleep(self.check_resource_interval)
            logging.info(f"can schedule to node: {', '.join(check_res.schedulable_nodes)}")
            if check_cnt <= 2 and len(check_res.schedulable_nodes) > 1:
                adjust_concurrency(add=True, context=context, slow_change=True)
            else:
                pending_pods = get_resource_starving_pods(timeout_minutes=10).get(self.namespace, [])
                if len(pending_pods) > 0 and check_cnt > 5:
                    adjust_concurrency(add=False, context=context, slow_change=False)
        try:
            return super().execute(context)
        except Exception as e:
            raise e
        finally:
            if lock_id:
                release_node_lock(lock_id)

    def get_or_create_pod(self, pod_request_obj: k8s.V1Pod, context: Context) -> k8s.V1Pod:
        logging.info("Getting or creating pod")
        if self.reattach_on_restart:
            logging.info("Reattach on restart is enabled")
            pod = self.find_pod(pod_request_obj.metadata.namespace, context=context)
            if pod:
                return pod
        logging.info("Starting pod:\n%s", yaml.safe_dump(pod_request_obj.to_dict()))
        self.pod_manager.create_pod(pod=pod_request_obj)
        return pod_request_obj

    def execute_sync(self, context: Context):
        logging.info("Executing pod operator in sync mode")
        result = None
        try:
            if self.pod_request_obj is None:
                logging.info("Building pod request object")
                self.pod_request_obj = self.build_pod_request_obj(context)
            for callback in self.callbacks:
                callback.on_pod_manifest_created(
                    pod_request=self.pod_request_obj,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )
            if self.pod is None:
                logging.info(
                    "Pod is not created yet. Waiting for pod to be created..."
                )
                self.pod = self.get_or_create_pod(  # must set `self.pod` for `on_kill`
                    pod_request_obj=self.pod_request_obj,
                    context=context,
                )
            # push to xcom now so that if there is an error we still have the values
            ti = context["ti"]
            ti.xcom_push(key="pod_name", value=self.pod.metadata.name)
            ti.xcom_push(key="pod_namespace", value=self.pod.metadata.namespace)

            # get remote pod for use in cleanup methods
            self.remote_pod = self.find_pod(self.pod.metadata.namespace, context=context)
            for callback in self.callbacks:
                callback.on_pod_creation(
                    pod=self.remote_pod,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )

            self.await_init_containers_completion(pod=self.pod)

            self.await_pod_start(pod=self.pod)
            if self.callbacks:
                pod = self.find_pod(self.pod.metadata.namespace, context=context)
                for callback in self.callbacks:
                    callback.on_pod_starting(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )

            self.await_pod_completion(pod=self.pod)
            if self.callbacks:
                pod = self.find_pod(self.pod.metadata.namespace, context=context)
                for callback in self.callbacks:
                    callback.on_pod_completion(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )
                for callback in self.callbacks:
                    callback.on_pod_teardown(
                        pod=pod,
                        client=self.client,
                        mode=ExecutionMode.SYNC,
                        context=context,
                        operator=self,
                    )

            if self.do_xcom_push:
                self.pod_manager.await_xcom_sidecar_container_start(pod=self.pod)
                result = self.extract_xcom(pod=self.pod)
            istio_enabled = self.is_istio_enabled(self.pod)
            self.remote_pod = self.pod_manager_await_pod_completion(
                self.pod, istio_enabled, self.base_container_name
            )
        except exceptions.ApiException as e:
            logging.info(f"failed with : {e}")
        finally:
            pod_to_clean = self.pod or self.pod_request_obj
            self.cleanup(
                pod=pod_to_clean,
                remote_pod=self.remote_pod,
            )
            for callback in self.callbacks:
                callback.on_pod_cleanup(
                    pod=pod_to_clean,
                    client=self.client,
                    mode=ExecutionMode.SYNC,
                    context=context,
                    operator=self,
                )

        if self.do_xcom_push:
            return result

    def pod_manager_await_pod_completion(self, pod: k8s.V1Pod, istio_enabled: bool = False, container_name: str = "base",
    ) -> k8s.V1Pod:
        st = time.time()
        while True:
            remote_pod = self.pod_manager.read_pod(pod)
            if remote_pod.status.phase in PodPhase.terminal_states:
                break
            if istio_enabled and container_is_completed(remote_pod, container_name):
                break
            logging.info("Pod %s has phase %s", pod.metadata.name, remote_pod.status.phase)
            time.sleep(2)
            if time.time() - st > 300:
                raise TimeoutError(f"Pod {pod.metadata.name} close timed out")
        return remote_pod
