import logging
from enum import Enum
from pydantic import BaseModel
from typing import List, Tuple
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.common.sql.hooks.sql import fetch_one_handler
from airflow.exceptions import AirflowException
import re

# sil_track

class SimRepo:
    sil_oasis = "eq-simulation/eq-sil-oasis"
    sil_readers = "eq-simulation/eq_sil_pcap_readers"
    sil_parameter = "eq-simulation/beeos_parameter"

class BeeosRepo:
    beeos = "vehicle-developer/release/beeos"
    common_msg = "vehicle-developer/utility/eq-msg"
    common_utility = "vehicle-developer/utility/beeos-utility"
    common_runnable = "vehicle-developer/runnable/eq-runnable-common"
    common_algorithm = "vehicle-developer/algorithm/eq_auto_common"

    control_bsw = "vehicle-developer/algorithm/eq_bsw"
    control_matlab = "vehicle-developer/algorithm/eq_matlab"
    control_runnable = "vehicle-developer/runnable/eq-control-runnable"

    planning_runnable = "vehicle-developer/runnable/eq-planning_runnable"
    planning_decision_algorithm = "vehicle-developer/algorithm/eq_planning/eq_decision_algorithm"

    perception_lidar_detection = "vehicle-developer/algorithm/eq_perception/eq_lidar_detection"
    perception_lidar_inference= "vehicle-developer/algorithm/eq_perception/eq_lidar_inference"
    perception_multisensor_fusion = "vehicle-developer/algorithm/eq_perception/eq_multisensor_fusion"
    perception_radar_perception = "vehicle-developer/algorithm/eq_perception/eq_radar_perception"
    perception_semantic_detection = "vehicle-developer/algorithm/eq_perception/eq_semantic_detection"
    perception_common = "vehicle-developer/algorithm/eq_perception/perception_common"
    perception_runnable = "vehicle-developer/runnable/eq-perception_runnable"

    prediction_alg = "vehicle-developer/algorithm/eq_perception/eq_prediction"
    prediction_runnable = "vehicle-developer/runnable/eq-prediction-runnable"

    map_runnable = "vehicle-developer/runnable/eq-map_service_runnable"
    map_gis_common = "scheduleplatform/code/gis_app/eq-gis-common"
    map_global_plan = "vehicle-developer/algorithm/eq_planning/eq_global_plan"
    map_hdmap_engine = "scheduleplatform/code/sensor_app/eq-hdmap-engine"
    map_boundary_check = "vehicle-developer/algorithm/eq_auto_map/eq_map_boundary_check"
    map_lidar_algorithm = "vehicle-developer/algorithm/eq_map_lidar_algorithm"
    map_mission_plan = "vehicle-developer/algorithm/eq_planning/eq_mission_plan"

    loc_runnable = "vehicle-developer/runnable/eq-localization_runnable"
    loc_external_detection = "vehicle-developer/algorithm/eq_localization/eq_external_detection"
    loc_integration = "vehicle-developer/algorithm/eq_localization/eq_integration"
    loc_mapping = "vehicle-developer/algorithm/eq_localization/eq_mapping"
    loc_msf_loc = "vehicle-developer/algorithm/eq_localization/eq_msf_loc"
    loc_odometry = "vehicle-developer/algorithm/eq_localization/eq_odometry"
    loc_pf = "vehicle-developer/algorithm/eq_localization/eq_pf_localization"
    loc_common = "vehicle-developer/algorithm/eq_localization/localization_common"
    loc_lidar_odom = "matiantian/eq_lidar_odom"
    task_runnable = "vehicle-developer/runnable/eq-task_runnable"

class ImageType(str, Enum):
    sim = "sim"
    alg = "alg"
    update = "update"

class UpdateType(str, Enum):
    replace_file = "replace_file"
    compile = "compile"

class UpdateConfig(BaseModel):
    base_image: str | None = None
    rebuild_type: UpdateType | None = UpdateType.compile

class ImageModuleType(str, Enum):
    pdc = "pdc"
    perception = "perception"
    localization = "localization"
    all = "all"
    custom = "custom"

class Repo(BaseModel):
    repo_name: str | None = ""
    branch: str | None = ""
    tag: str | None = ""

class SimRepoConf(BaseModel):
    sil_oasis: Repo = Repo(repo_name=SimRepo.sil_oasis, branch="master")
    sil_reader: Repo = Repo(repo_name=SimRepo.sil_readers, branch="master")
    sil_param: Repo = Repo(repo_name=SimRepo.sil_parameter, branch="master")

class BeeosRepoConf(BaseModel):
    beeos: Repo = Repo(repo_name=BeeosRepo.beeos)
    sub_repo: List[Repo] = []

class ImageConfig(BaseModel):
    name: str | None = ""
    update_conf: UpdateConfig | None = UpdateConfig()
    image_type: ImageType | None = ImageType.sim
    module_type: ImageModuleType | None = ImageModuleType.pdc
    sim_repo: SimRepoConf | None = SimRepoConf()
    alg_repo: BeeosRepoConf | None = BeeosRepoConf()
    creator: str | None = None
    task_id: int | None = None

    def check(self) -> Tuple[bool, str]:
        if not self.name:
            return False, "镜像名为空"
        if not self.image_type:
            return False, "镜像类型为空"
        if self.image_type == ImageType.alg and not self.module_type:
            return False, "算法镜像，未指定具体类型"
        if not self.sim_repo:
            return False, "仿真仓库未指定"
        if self.image_type == ImageType.alg and not self.alg_repo.beeos.branch:
            return False, "算法主线分支未指定"
        return True, "参数自检成功"

class ImageBuildStatus(str, Enum):
    waiting = "等待中"
    start = "任务开始"
    prepare = "拉取代码"
    building = "构建中"
    success = "构建成功"
    failed = "构建失败"

def add_image_task(pg_cli:PostgresHook, config: ImageConfig):
    result, reason = config.check()
    if not result:
        raise AirflowException(f"参数自检失败: {reason}")
    if config.task_id == -1:
        insert_sql = f"""
        insert into image_build_table(
            timestamp,
            config,
            status,
            image_name,
            image_type,
            module_type,
            creator
        ) values (
            'NOW()',
            '{config.model_dump_json()}',
            '{ImageBuildStatus.waiting.value}',
            '{config.name}',
            '{config.image_type.value}',
            '{config.module_type.value}',
            '{config.creator}'
        ) RETURNING id;
        """
        tid = pg_cli.run(insert_sql, autocommit=True, handler=fetch_one_handler)
        if isinstance(tid, int):
            config.task_id = tid
            return config.task_id
        if isinstance(tid, tuple):
            config.task_id = tid[0]
            return config.task_id
    return config.task_id

def update_image_task(pg_cli:PostgresHook, id: int, status: ImageBuildStatus, error_info: str=""):
    update_sql = f"""
    update image_build_table set
        status = '{status}',
        error_info = '{error_info}'
    where id = {id};
    """
    return pg_cli.run(update_sql, autocommit=True)

pull_command = """
function init_dir() {

  rm -rf ${root_path}
  mkdir ${root_path}

  echo "git clone beeos"
  # git clone beeos
  cd ${root_path}
  git clone -b ${beeos_branch} --recursive ***************************:vehicle-developer/release/beeos.git
  # checkout all branch
  main_branch=`echo "$beeos_branch" | cut -d _ -f 1`_000
  cd ${beeos_path}
  git submodule foreach git pull
  git submodule foreach "git checkout ${main_branch}; git checkout ${beeos_branch} || true; git pull"

  if [ ${unity} != "master" ];then
    # checkout utility branch
    echo "============ checkout unity branch: ${unity} ============"
    cd ${beeos_path}/utility/
    git checkout ${unity} && git pull
  else
    cd ${beeos_path}/utility/
    git checkout ${beeos_branch}
  fi
  if [ ${eq_msg} != "master" ];then
    # checkout eq_msg branch
    echo "============ checkout eq_msg branch: ${eq_msg} ============"
    cd ${beeos_path}/msg/
    git checkout ${eq_msg} && git pull
  else
    cd ${beeos_path}/msg/
    git checkout ${beeos_branch}
  fi
  if [ ${eq_runnable_common} != "master" ];then
    # checkout eq_runnable_common branch
    echo "============ checkout eq_runnable_common branch: ${eq_runnable_common} ============"
    cd ${beeos_path}/runnable/common/
    git checkout ${eq_runnable_common} && git pull
  else
    cd ${beeos_path}/runnable/common/
    git checkout ${beeos_branch}
  fi
  if [ ${eq_auto_common} != "master" ];then
    echo "============ checkout eq_auto_common branch: ${eq_auto_common} ============"
    cd ${beeos_path}/algorithm/eq_auto_common/
    git checkout ${eq_auto_common} && git pull
  else
    cd ${beeos_path}/algorithm/eq_auto_common/
    git checkout ${eq_auto_common}
  fi


  if [ ${algorithm_branch} != "master" ];then
    # checkout algorithm branch
    echo "============ checkout algorithm branch: ${algorithm_branch} ============"
    cd ${beeos_path}/algorithm/planning/eq_decision_algorithm
    git checkout ${algorithm_branch} && git pull
  fi
  if [ ${runnable_branch} != "master" ];then
    # checkout runnable branch
    echo "============ checkout runnable branch: ${runnable_branch} ============"
    cd ${beeos_path}/runnable/planning
    git checkout ${runnable_branch} && git pull
  fi
  # check control alg and runnable branch
  if [ ${cab} != "master" ];then
    echo "============ checkout cab branch: ${cab} ============"
    cd ${beeos_path}/algorithm/control/eq_bsw
    git checkout ${cab} && git pull
  fi
  if [ ${cam} != "master" ];then
    echo "============ checkout cam branch: ${cam} ============"
    cd ${beeos_path}/algorithm/control/eq_matlab
    git checkout ${cam} && git pull
  fi
  if [ ${cr} != "master" ];then
    echo "============ checkout cr branch: ${cr} ============"
    cd ${beeos_path}/runnable/control
    git checkout ${cr} && git pull
  fi
  if [ ${eq_prediction_runnable} != "master" ];then
    echo "============ checkout eq_prediction_runnable branch: ${eq_prediction_runnable} ============"
    cd ${beeos_path}/runnable/prediction
    git checkout ${eq_prediction_runnable} && git pull
  fi
  if [ ${eq_perception_runnable} != "master" ];then
    echo "============ checkout eq_perception_runnable branch: ${eq_perception_runnable} ============"
    cd ${beeos_path}/runnable/perception
    git checkout ${eq_perception_runnable} && git pull
  fi
  if [ ${eq_lidar_detection} != "master" ];then
    echo "============ checkout eq_lidar_detection branch: ${eq_lidar_detection} ============"
    cd ${beeos_path}/algorithm/perception/eq_lidar_detection
    git checkout ${eq_lidar_detection} && git pull
  fi
  if [ ${eq_multisensor_fusion} != "master" ];then
    echo "============ checkout eq_multisensor_fusion branch: ${eq_multisensor_fusion} ============"
    cd ${beeos_path}/algorithm/perception/eq_multisensor_fusion
    git checkout ${eq_multisensor_fusion} && git pull
  fi
  if [ ${eq_semantic_detection} != "master" ];then
    echo "============ checkout eq_semantic_detection branch: ${eq_semantic_detection} ============"
    cd ${beeos_path}/algorithm/perception/eq_semantic_detection
    git checkout ${eq_semantic_detection} && git pull
  fi
  if [ ${perception_common} != "master" ];then
    echo "============ checkout perception_common branch: ${perception_common} ============"
    cd ${beeos_path}/algorithm/perception/perception_common
    git checkout ${perception_common} && git pull
  fi
  if [ ${eq_radar_perception} != "master" ];then
    echo "============ checkout eq_radar_perception branch: ${eq_radar_perception} ============"
    cd ${beeos_path}/algorithm/perception/eq_radar_perception
    git checkout ${eq_radar_perception} && git pull
  fi
  if [ ${eq_lidar_inference} != "master" ];then
    echo "============ checkout eq_lidar_inference branch: ${eq_lidar_inference} ============"
    cd ${beeos_path}/algorithm/perception/eq_lidar_inference
    git checkout ${eq_lidar_inference} && git pull
  fi
  if [ ${eq_prediction} != "master" ];then
    echo "============ checkout eq_prediction branch: ${eq_prediction} ============"
    cd ${beeos_path}/algorithm/perception/eq_prediction
    git checkout ${eq_prediction} && git pull
  fi
  #if [ ${pa} != "master" ];then
  #  echo "============ checkout cr branch: ${cr} ============"
  #  cd ${beeos_path}/algorithm/planning/eq_planning
  #  git checkout ${pa} && git pull
  #  cd ${beeos_path}/algorithm/planning
  #  sed -i '1s/eq_decision_algorithm/eq_planning/g' ${beeos_path}/algorithm/planning/CMakeLists.txt
  #fi
  echo "============ git clone eq-sil-oasis============ "
  sleep 1
  mkdir -p ${cache_codes_dir}
  cd ${cache_codes_dir}
  <NAME_EMAIL>:eq-simulation/eq-sil-oasis.git -b $oasis --recursive
  cd eq-sil-oasis/modules/beeos_adapter
  git pull || true
  git checkout ${beeos_branch} || git checkout master || true
  git pull || true
}
start=$(date +%s%N)
echo "========== 初始化目录 =========="
init_dir
sleep 1
dir_init_done=$(date +%s%N)
initcodes_cost=$(( (dir_init_done - start) / 1000000000 ))
echo "准备代码耗时：${initcodes_cost}s"
"""

def pull_code_command(params, date_id):
    command = f"""# beeos分支参数
beeos_branch={params.get("beeos","3.1.8_000")}
# 决策
algorithm_branch={params.get("eq_decision_algorithm","master")}
runnable_branch={params.get("eq_planning_runnable","master")}
# 控制
cab={params.get("eq_bsw","master")}
cam={params.get("eq_matlab","master")}
cr={params.get("eq_control_runnable","master")}
# oasis
oasis=master
pa={params.get("eq_planning_algorithm","master")}
eq_runnable_common={params.get("eq_runnable_common","master")}
eq_auto_common={params.get("eq_auto_common","master")}
unity={params.get("unity","master")}
eq_msg={params.get("eq_msg","master")}
# 感知
eq_prediction_runnable={params.get("eq_prediction_runnable","master")}
eq_perception_runnable={params.get("eq_perception_runnable","master")}
eq_lidar_detection={params.get("eq_lidar_detection","master")}
eq_multisensor_fusion={params.get("eq_multisensor_fusion","master")}
eq_semantic_detection={params.get("eq_semantic_detection","master")}
perception_common={params.get("perception_common","master")}
eq_radar_perception={params.get("eq_radar_perception","master")}
eq_lidar_inference={params.get("eq_lidar_inference","master")}
eq_prediction={params.get("eq_prediction","master")}
"""
    command += f"root_path=/tmp/data_close_loop_{date_id};"
    command += """
beeos_path="${root_path}/beeos"
cache_codes_dir="${root_path}/codes"
build_oasis_dir=${cache_codes_dir}/eq-sil-oasis
"""
    command += pull_command
    logging.info("拉取代码命令：  \n"+command)
    return command

def build_command(params, date_id):
    image_full_name = f"registry.eqfleetcmder.com/eq-sil/test_sil:data_close_loop_{params.get("tag")}"
    module = "perception" if "perception" in params.get("module", []) else ",".join(params.get("module", []))
    command = f"task_id=data_close_loop_{date_id};NEW_IMAGE_TAG={image_full_name};module={module}"
    command += """
oasis=master
root_path=/tmp/${task_id}
beeos_path="${root_path}/beeos"
cache_codes_dir="${root_path}/codes"
build_oasis_dir=${cache_codes_dir}/eq-sil-oasis
build_retcode=0
build_log_name=${task_id}_build.log
build_log_home=/root/workspace/codes/sil-track/log/dcl_build_logs
mkdir -p ${build_log_home}
build_log="${build_log_home}/${build_log_name}"
build_log_url="http://10.30.16.26:8049/dcl_build_logs/${build_log_name}"
function build_beeos() {
echo "============ 开始编译beeos and sim ============"
cd ${build_oasis_dir}
datetime=$(date +"%Y%m%d-%H%M%S")
if [ ${module} != "perception" ];then
    tt="-s ${oasis} -b ${beeos_path} -t ${NEW_IMAGE_TAG}"
else
    tt="-s ${oasis} -b ${beeos_path} -t ${NEW_IMAGE_TAG} -m perception"
fi
rm -fr ${build_log}
mkdir -p /tmp/image_build_dcl && buildtmpdir=`mktemp -d -p /tmp/image_build_dcl`
TEMP_TOP_DIR=${buildtmpdir} bash ./docker/build_image.sh --make_proc 12 --cache_dir=${cache_codes_dir} $tt 2>&1 | tee $build_log
build_retcode=${PIPESTATUS[0]}
[[ -n $buildtmpdir ]] && rm -fr $buildtmpdir
echo "编译完成,返回码:${build_retcode}=="
}
dir_init_done=$(date +%s%N)
echo "========== 编译BeeOS =========="
build_beeos
build_done=$(date +%s%N)
buildcode_cost=$(( (build_done - dir_init_done) / 1000000000 ))
sleep 1
rm -rf ${root_path}
if [[ ! -f ${build_log} ]];then
build_log_url=""
fi
echo "编译日志链接：${build_log_url} ; 返回码：${build_retcode}, 执行编译耗时：${buildcode_cost}s"
if [[ $build_retcode -ne 0 ]];then
echo "编译出错=="
echo "EEEEE"
else
echo "BUILD_SUCCESS_FINISH"
fi

exit $build_retcode
"""
    logging.info("构建镜像命令：  \n"+command)
    return command

def get_info_from_log(text):
    log_link_pattern = r'编译日志链接：(http://[^<]+) ;'
    return_code_pattern = r'返回码：(\d+)'
    compile_time_pattern = r'执行编译耗时：(\d+)s'
    log_link = re.search(log_link_pattern, text)
    return_code = re.search(return_code_pattern, text)
    compile_time = re.search(compile_time_pattern, text)
    return {
        "日志链接": f"[{log_link.group(1)}]({log_link.group(1)})",
        # "返回码": return_code.group(1),
        "编译耗时": f"{compile_time.group(1)}s"
    }

if __name__ == "__main__":
    print(
        get_info_from_log("编译日志链接：http://10.30.16.26:8049/dcl_build_logs/ LOGEND, 返回码：0, 执行编译耗时：2012s")
    )
