from kubernetes.client import models as k8s
from kubernetes import client, config
from collections import namedtuple
from datetime import datetime, timezone, timedelta
import logging
from typing import Dict, List

AffinityItem = namedtuple("Affinity", ["key", "operator", "values", "weight"])

default_affinitys = [
    AffinityItem("gpu", "In", ["on"], 60),
    AffinityItem("node", "In", ["cpu"], 40),
]

def create_affinity(affinitys_list:list=default_affinitys, dag_id:str=""):
    if len(affinitys_list) == 0:
        return None
    affinitys = k8s.V1Affinity(
        node_affinity=k8s.V1NodeAffinity(
            preferred_during_scheduling_ignored_during_execution=[
                k8s.V1PreferredSchedulingTerm(
                    preference=k8s.V1NodeSelectorTerm(
                        match_expressions=[
                            k8s.V1NodeSelectorRequirement(
                                key=affinitys_list[0].key,
                                operator=affinitys_list[0].operator,
                                values=affinitys_list[0].values,
                            )
                        ]
                    ),
                    weight=affinitys_list[0].weight
                )
            ]
        ),
        pod_anti_affinity=k8s.V1PodAntiAffinity(
            preferred_during_scheduling_ignored_during_execution=[
                k8s.V1WeightedPodAffinityTerm(
                    weight=70,
                    pod_affinity_term=k8s.V1PodAffinityTerm(
                        label_selector=k8s.V1LabelSelector(
                            match_labels={
                                "dag_id": dag_id
                            }
                        ),
                        topology_key="kubernetes.io/hostname"
                    )
                )
            ]
        )
    )
    for aff in affinitys_list[1:]:
        affinitys.node_affinity.preferred_during_scheduling_ignored_during_execution.append(
            k8s.V1PreferredSchedulingTerm(
                preference=k8s.V1NodeSelectorTerm(
                    match_expressions=[
                        k8s.V1NodeSelectorRequirement(
                            key=aff.key,
                            operator=aff.operator,
                            values=aff.values,
                        )
                    ]
                ),
                weight=aff.weight
            )
        )
    return affinitys


def get_k8s_client():
    """获取Kubernetes客户端"""
    # try:
    #     # 尝试加载集群内配置
    #     config.load_incluster_config()
    # except config.ConfigException:
    try:
        # 尝试加载本地配置
        config.load_kube_config()
    except config.ConfigException:
        logging.error("无法加载Kubernetes配置")
        raise

    return client.CoreV1Api()


def check_long_pending_pods(timeout_minutes: int = 5) -> Dict[str, List[Dict]]:
    """
    检查各个namespace下是否存在pending时长超过指定时间的pod

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟

    Returns:
        Dict[str, List[Dict]]: 格式为 {namespace: [pod_info_list]}
        pod_info包含: name, namespace, phase, creation_time, pending_duration, reason, message
    """
    try:
        k8s_client = get_k8s_client()
        current_time = datetime.now(timezone.utc)
        timeout_delta = timedelta(minutes=timeout_minutes)

        # 获取所有namespace
        namespaces = k8s_client.list_namespace()

        long_pending_pods = {}

        for namespace in namespaces.items:
            namespace_name = namespace.metadata.name

            # 跳过系统namespace（可选）
            if namespace_name.startswith('kube-'):
                continue

            try:
                # 获取该namespace下的所有pod
                pods = k8s_client.list_namespaced_pod(namespace=namespace_name)

                namespace_pending_pods = []

                for pod in pods.items:
                    # 检查pod状态是否为Pending
                    if pod.status.phase == 'Pending':
                        creation_time = pod.metadata.creation_timestamp

                        # 计算pending时长
                        pending_duration = current_time - creation_time

                        # 如果pending时长超过阈值
                        if pending_duration > timeout_delta:
                            # 获取pod的详细状态信息
                            reason = ""
                            message = ""

                            # 检查pod的condition
                            if pod.status.conditions:
                                for condition in pod.status.conditions:
                                    if condition.type == 'PodScheduled' and condition.status == 'False':
                                        reason = condition.reason or ""
                                        message = condition.message or ""
                                        break

                            # 检查容器状态
                            if not reason and pod.status.container_statuses:
                                for container_status in pod.status.container_statuses:
                                    if container_status.state.waiting:
                                        reason = container_status.state.waiting.reason or ""
                                        message = container_status.state.waiting.message or ""
                                        break

                            pod_info = {
                                'name': pod.metadata.name,
                                'namespace': namespace_name,
                                'phase': pod.status.phase,
                                'creation_time': creation_time.isoformat(),
                                'pending_duration_minutes': int(pending_duration.total_seconds() / 60),
                                'pending_duration_seconds': int(pending_duration.total_seconds()),
                                'reason': reason,
                                'message': message,
                                'node_name': pod.spec.node_name,
                                'labels': pod.metadata.labels or {},
                                'annotations': pod.metadata.annotations or {}
                            }

                            namespace_pending_pods.append(pod_info)

                # 如果该namespace有长时间pending的pod，添加到结果中
                if namespace_pending_pods:
                    long_pending_pods[namespace_name] = namespace_pending_pods

            except Exception as e:
                logging.error(f"检查namespace {namespace_name} 时出错: {e}")
                continue

        return long_pending_pods

    except Exception as e:
        logging.error(f"检查长时间pending的pod时出错: {e}")
        raise


def get_pending_pods_summary(timeout_minutes: int = 5) -> Dict:
    """
    获取长时间pending pod的摘要信息

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟

    Returns:
        Dict: 包含总数、按namespace统计、按原因统计等信息
    """
    try:
        long_pending_pods = check_long_pending_pods(timeout_minutes)

        total_pods = 0
        namespace_stats = {}
        reason_stats = {}

        for namespace, pods in long_pending_pods.items():
            namespace_stats[namespace] = len(pods)
            total_pods += len(pods)

            for pod in pods:
                reason = pod.get('reason', 'Unknown')
                if reason in reason_stats:
                    reason_stats[reason] += 1
                else:
                    reason_stats[reason] = 1

        summary = {
            'total_long_pending_pods': total_pods,
            'affected_namespaces': len(long_pending_pods),
            'namespace_stats': namespace_stats,
            'reason_stats': reason_stats,
            'timeout_minutes': timeout_minutes,
            'check_time': datetime.now(timezone.utc).isoformat(),
            'details': long_pending_pods
        }

        return summary

    except Exception as e:
        logging.error(f"获取pending pod摘要时出错: {e}")
        raise


def print_pending_pods_report(timeout_minutes: int = 5):
    """
    打印长时间pending pod的详细报告

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟
    """
    try:
        summary = get_pending_pods_summary(timeout_minutes)

        print("=" * 80)
        print(f"Kubernetes长时间Pending Pod检查报告")
        print("=" * 80)
        print(f"检查时间: {summary['check_time']}")
        print(f"超时阈值: {timeout_minutes} 分钟")
        print(f"总计长时间Pending Pod: {summary['total_long_pending_pods']}")
        print(f"受影响的Namespace: {summary['affected_namespaces']}")

        if summary['total_long_pending_pods'] == 0:
            print("\n✅ 没有发现长时间pending的pod")
            return

        # 按namespace统计
        print(f"\n📊 按Namespace统计:")
        for namespace, count in summary['namespace_stats'].items():
            print(f"  {namespace}: {count} 个pod")

        # 按原因统计
        print(f"\n🔍 按失败原因统计:")
        for reason, count in summary['reason_stats'].items():
            print(f"  {reason}: {count} 个pod")

        # 详细信息
        print(f"\n📋 详细信息:")
        for namespace, pods in summary['details'].items():
            print(f"\n🏷️  Namespace: {namespace}")
            print("-" * 60)

            for pod in pods:
                print(f"  Pod名称: {pod['name']}")
                print(f"  状态: {pod['phase']}")
                print(f"  Pending时长: {pod['pending_duration_minutes']} 分钟")
                print(f"  创建时间: {pod['creation_time']}")
                print(f"  失败原因: {pod['reason']}")
                if pod['message']:
                    print(f"  详细信息: {pod['message']}")
                if pod['node_name']:
                    print(f"  调度节点: {pod['node_name']}")

                # 显示重要的标签
                if pod['labels']:
                    important_labels = {k: v for k, v in pod['labels'].items()
                                      if k in ['app', 'component', 'version', 'dag_id', 'task_id']}
                    if important_labels:
                        print(f"  标签: {important_labels}")

                print()

        print("=" * 80)

    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")
        logging.error(f"生成pending pod报告时出错: {e}")


def check_and_alert_pending_pods(timeout_minutes: int = 5, alert_threshold: int = 1) -> bool:
    """
    检查长时间pending的pod并返回是否需要告警

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟
        alert_threshold: 告警阈值，超过这个数量的pod才告警，默认1

    Returns:
        bool: 是否需要告警
    """
    try:
        summary = get_pending_pods_summary(timeout_minutes)

        should_alert = summary['total_long_pending_pods'] >= alert_threshold

        if should_alert:
            logging.warning(f"发现 {summary['total_long_pending_pods']} 个长时间pending的pod，超过告警阈值 {alert_threshold}")

            # 记录详细信息到日志
            for namespace, pods in summary['details'].items():
                for pod in pods:
                    logging.warning(
                        f"长时间Pending Pod: {namespace}/{pod['name']}, "
                        f"时长: {pod['pending_duration_minutes']}分钟, "
                        f"原因: {pod['reason']}"
                    )
        else:
            logging.info(f"检查完成，发现 {summary['total_long_pending_pods']} 个长时间pending的pod，未超过告警阈值")

        return should_alert

    except Exception as e:
        logging.error(f"检查pending pod告警时出错: {e}")
        return True  # 出错时返回True，触发告警


def get_resource_starving_pods(timeout_minutes: int = 5) -> Dict[str, List[Dict]]:
    """
    获取因为资源不足而无法调度的pod（简化版本）

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟

    Returns:
        Dict[str, List[Dict]]: 格式为 {namespace: [pod_info_list]}
        pod_info包含: name, namespace, pending_duration_minutes, resource_type, message
    """
    try:
        k8s_client = get_k8s_client()
        current_time = datetime.now(timezone.utc)
        timeout_delta = timedelta(minutes=timeout_minutes)

        # 资源不足相关的关键词
        resource_keywords = [
            'Insufficient cpu',
            'Insufficient memory',
            'Insufficient nvidia.com/gpu',
            'Insufficient ephemeral-storage',
            'Insufficient hugepages',
            'insufficient cpu',
            'insufficient memory',
            'insufficient gpu'
        ]

        # 获取所有namespace
        namespaces = k8s_client.list_namespace()

        resource_starving_pods = {}

        for namespace in namespaces.items:
            namespace_name = namespace.metadata.name

            # 跳过系统namespace
            if namespace_name.startswith('kube-'):
                continue

            try:
                # 获取该namespace下的pending pod
                pods = k8s_client.list_namespaced_pod(
                    namespace=namespace_name,
                    field_selector='status.phase=Pending'
                )

                namespace_starving_pods = []

                for pod in pods.items:
                    creation_time = pod.metadata.creation_timestamp
                    pending_duration = current_time - creation_time

                    # 检查是否超过超时时间
                    if pending_duration > timeout_delta:
                        reason = ""
                        message = ""
                        resource_type = "unknown"

                        # 检查pod的调度条件
                        if pod.status.conditions:
                            for condition in pod.status.conditions:
                                if (condition.type == 'PodScheduled' and
                                    condition.status == 'False' and
                                    condition.message):

                                    message = condition.message
                                    reason = condition.reason or ""

                                    # 检查是否是资源不足导致的
                                    message_lower = message.lower()
                                    is_resource_issue = any(keyword.lower() in message_lower
                                                          for keyword in resource_keywords)

                                    if is_resource_issue:
                                        # 确定具体的资源类型
                                        if 'cpu' in message_lower:
                                            resource_type = "cpu"
                                        elif 'memory' in message_lower:
                                            resource_type = "memory"
                                        elif 'gpu' in message_lower or 'nvidia.com/gpu' in message_lower:
                                            resource_type = "gpu"
                                        elif 'storage' in message_lower:
                                            resource_type = "storage"
                                        elif 'hugepages' in message_lower:
                                            resource_type = "hugepages"
                                        else:
                                            resource_type = "resource"

                                        pod_info = {
                                            'name': pod.metadata.name,
                                            'namespace': namespace_name,
                                            'pending_duration_minutes': int(pending_duration.total_seconds() / 60),
                                            'resource_type': resource_type,
                                            'reason': reason,
                                            'message': message,
                                            'creation_time': creation_time.isoformat(),
                                            'labels': pod.metadata.labels or {}
                                        }

                                        namespace_starving_pods.append(pod_info)
                                        break  # 找到资源不足原因后跳出循环

                # 如果该namespace有资源不足的pod，添加到结果中
                if namespace_starving_pods:
                    resource_starving_pods[namespace_name] = namespace_starving_pods

            except Exception as e:
                logging.error(f"检查namespace {namespace_name} 的资源不足pod时出错: {e}")
                continue

        return resource_starving_pods

    except Exception as e:
        logging.error(f"检查资源不足pod时出错: {e}")
        raise


def get_resource_starving_summary(timeout_minutes: int = 5) -> Dict:
    """
    获取资源不足pod的摘要信息

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟

    Returns:
        Dict: 包含总数、按资源类型统计、按namespace统计等信息
    """
    try:
        starving_pods = get_resource_starving_pods(timeout_minutes)

        total_pods = 0
        namespace_stats = {}
        resource_type_stats = {}

        for namespace, pods in starving_pods.items():
            namespace_stats[namespace] = len(pods)
            total_pods += len(pods)

            for pod in pods:
                resource_type = pod.get('resource_type', 'unknown')
                if resource_type in resource_type_stats:
                    resource_type_stats[resource_type] += 1
                else:
                    resource_type_stats[resource_type] = 1

        summary = {
            'total_resource_starving_pods': total_pods,
            'affected_namespaces': len(starving_pods),
            'namespace_stats': namespace_stats,
            'resource_type_stats': resource_type_stats,
            'timeout_minutes': timeout_minutes,
            'check_time': datetime.now(timezone.utc).isoformat(),
            'details': starving_pods
        }

        return summary

    except Exception as e:
        logging.error(f"获取资源不足pod摘要时出错: {e}")
        raise


def print_resource_starving_report(timeout_minutes: int = 5):
    """
    打印资源不足pod的详细报告

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟
    """
    try:
        summary = get_resource_starving_summary(timeout_minutes)

        print("=" * 80)
        print(f"Kubernetes资源不足Pod检查报告")
        print("=" * 80)
        print(f"检查时间: {summary['check_time']}")
        print(f"超时阈值: {timeout_minutes} 分钟")
        print(f"总计资源不足Pod: {summary['total_resource_starving_pods']}")
        print(f"受影响的Namespace: {summary['affected_namespaces']}")

        if summary['total_resource_starving_pods'] == 0:
            print("\n✅ 没有发现因资源不足而无法调度的pod")
            return

        # 按资源类型统计
        print(f"\n📊 按资源类型统计:")
        for resource_type, count in summary['resource_type_stats'].items():
            print(f"  {resource_type}: {count} 个pod")

        # 按namespace统计
        print(f"\n🏷️  按Namespace统计:")
        for namespace, count in summary['namespace_stats'].items():
            print(f"  {namespace}: {count} 个pod")

        # 详细信息
        print(f"\n📋 详细信息:")
        for namespace, pods in summary['details'].items():
            print(f"\n🏷️  Namespace: {namespace}")
            print("-" * 60)

            for pod in pods:
                print(f"  Pod名称: {pod['name']}")
                print(f"  资源类型: {pod['resource_type']}")
                print(f"  Pending时长: {pod['pending_duration_minutes']} 分钟")
                print(f"  创建时间: {pod['creation_time']}")
                print(f"  失败原因: {pod['reason']}")
                if pod['message']:
                    # 截取消息的前100个字符，避免过长
                    message = pod['message'][:100] + "..." if len(pod['message']) > 100 else pod['message']
                    print(f"  详细信息: {message}")

                # 显示重要的标签
                if pod['labels']:
                    important_labels = {k: v for k, v in pod['labels'].items()
                                      if k in ['app', 'component', 'version', 'dag_id', 'task_id']}
                    if important_labels:
                        print(f"  标签: {important_labels}")

                print()

        print("=" * 80)

    except Exception as e:
        print(f"❌ 生成资源不足报告时出错: {e}")
        logging.error(f"生成资源不足pod报告时出错: {e}")


def check_resource_starvation_alert(timeout_minutes: int = 5, alert_threshold: int = 1) -> bool:
    """
    检查资源不足的pod并返回是否需要告警

    Args:
        timeout_minutes: 超时时间（分钟），默认5分钟
        alert_threshold: 告警阈值，超过这个数量的pod才告警，默认1

    Returns:
        bool: 是否需要告警
    """
    try:
        summary = get_resource_starving_summary(timeout_minutes)

        should_alert = summary['total_resource_starving_pods'] >= alert_threshold

        if should_alert:
            logging.warning(f"发现 {summary['total_resource_starving_pods']} 个资源不足的pod，超过告警阈值 {alert_threshold}")

            # 记录详细信息到日志
            for namespace, pods in summary['details'].items():
                for pod in pods:
                    logging.warning(
                        f"资源不足Pod: {namespace}/{pod['name']}, "
                        f"资源类型: {pod['resource_type']}, "
                        f"时长: {pod['pending_duration_minutes']}分钟"
                    )
        else:
            logging.info(f"检查完成，发现 {summary['total_resource_starving_pods']} 个资源不足的pod，未超过告警阈值")

        return should_alert

    except Exception as e:
        logging.error(f"检查资源不足pod告警时出错: {e}")
        return True  # 出错时返回True，触发告警
