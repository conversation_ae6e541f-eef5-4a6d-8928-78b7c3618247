# a decorator for retrying a function
import time
import functools
import logging
import traceback

def retry_decorator(max_retries=3, delay=1):
    """
    重试装饰器
    :param max_retries: 最大重试次数，默认为3
    :param delay: 每次重试之间的延迟时间（秒），默认为1秒
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            attempts = 0
            while attempts < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempts += 1
                    logging.info(f"尝试 {attempts}/{max_retries} 失败，错误信息：{e}")
                    logging.error(f"完整错误堆栈:\n{traceback.format_exc()}")
                    time.sleep(delay)
            raise Exception(f"函数 {func.__name__} 在 {max_retries} 次重试后仍然失败\n最后错误信息:\n{traceback.format_exc()}")
        return wrapper
    return decorator

@retry_decorator(max_retries=5, delay=2)
def test_func(ti):
    import random
    if random.random() < 0.1:  # 假设80%的概率会报错
        raise ValueError("随机报错")
    return ti

if __name__ == "__main__":
    print(test_func(1))
