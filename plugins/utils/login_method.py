import requests


class Login:
    def __init__(self, env, part):
        if env == 'dev':
            self.url = 'https://closeloop-testdms.eqfleetcmder.com/data/clientLogin'
        elif env == 'prod':
            self.url = 'https://closeloop-dmp-dms.eqfleetcmder.com/data/clientLogin'
        else:
            raise Exception('Invalid environment')

    def login_in(self, username, password):
        payload = {
            "userName": username,
            "password": password
        }
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.post(self.url, json=payload, headers=headers, verify=False)
        if response.status_code == 200:
            return True, response.json().get("data")
        else:
            return False, response.text


if __name__ == '__main__':
    login = Login('prod', 'partner')
    print(login.login_in('airflow', '123456'))
