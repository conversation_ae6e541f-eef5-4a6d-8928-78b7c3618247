#!/usr/bin/env python3
"""
使用Kubernetes API直接获取集群资源使用情况
替代prometheus_metrics.py中基于Prometheus的实现
"""

from kubernetes import client, config
from kubernetes.client.rest import ApiException
import requests
import logging
from typing import Dict, List
from airflow.models.variable import Variable

# 获取配置
try:
    pod_operator_config = Variable.get('pod_operator_config', deserialize_json=True)
    hami_api_config = Variable.get('hami_api_config', deserialize_json=True, default_var={})
except Exception:
    pod_operator_config = {}
    hami_api_config = {}

def get_k8s_clients():
    """获取Kubernetes客户端"""
    try:
        # 尝试加载集群内配置
        config.load_incluster_config()
    except config.ConfigException:
        try:
            # 尝试加载本地配置
            config.load_kube_config()
        except config.ConfigException:
            logging.error("无法加载Kubernetes配置")
            raise

    return {
        'core_v1': client.CoreV1Api(),
        'metrics_v1beta1': client.CustomObjectsApi(),
        'apps_v1': client.AppsV1Api()
    }

def check_metrics_server() -> bool:
    """检查metrics-server是否运行"""
    try:
        clients = get_k8s_clients()
        v1 = clients['core_v1']

        pods = v1.list_namespaced_pod(namespace="kube-system")
        metrics_server_installed = any("metrics-server" in pod.metadata.name for pod in pods.items)

        if not metrics_server_installed:
            logging.warning("metrics-server未安装，无法获取实时使用情况")
            return False

        return True
    except ApiException as e:
        logging.error(f"检查metrics-server时出错: {e}")
        return False

def get_nodes() -> List[str]:
    """获取集群节点列表"""
    try:
        clients = get_k8s_clients()
        v1 = clients['core_v1']

        nodes = v1.list_node()
        # 过滤掉虚拟节点
        return [
            node.metadata.name for node in nodes.items
            if not node.metadata.name.startswith("vci-") and
               node.metadata.name != "vci-node1-cn-beijing-a"
        ]
    except ApiException as e:
        logging.error(f"获取节点列表时出错: {e}")
        return []

def get_node_allocatable(node_name: str) -> Dict[str, str]:
    """获取节点可分配资源"""
    try:
        clients = get_k8s_clients()
        v1 = clients['core_v1']

        node = v1.read_node(name=node_name)
        return node.status.allocatable
    except ApiException as e:
        logging.error(f"获取节点 {node_name} 可分配资源时出错: {e}")
        return {}

def get_node_capacity(node_name: str) -> Dict[str, str]:
    """获取节点总容量"""
    try:
        clients = get_k8s_clients()
        v1 = clients['core_v1']

        node = v1.read_node(name=node_name)
        return node.status.capacity
    except ApiException as e:
        logging.error(f"获取节点 {node_name} 总容量时出错: {e}")
        return {}

def get_node_images_size(node_name: str) -> int:
    """获取节点镜像总大小"""
    try:
        clients = get_k8s_clients()
        v1 = clients['core_v1']

        node = v1.read_node(name=node_name)
        if node.status.images:
            return sum(image.size_bytes or 0 for image in node.status.images)
        return 0
    except ApiException as e:
        logging.error(f"获取节点 {node_name} 镜像大小时出错: {e}")
        return 0

def get_node_metrics(node_name: str) -> Dict[str, str]:
    """获取节点实时使用指标"""
    try:
        clients = get_k8s_clients()
        custom_api = clients['metrics_v1beta1']

        # 获取节点metrics
        metrics = custom_api.get_cluster_custom_object(
            group="metrics.k8s.io",
            version="v1beta1",
            plural="nodes",
            name=node_name
        )

        return metrics.get('usage', {})
    except Exception as e:
        logging.warning(f"获取节点 {node_name} 实时指标时出错: {e}")
        return {}

def get_pod_resource_requests_limits() -> Dict[str, Dict[str, float]]:
    """获取所有Pod的资源请求和限制"""
    try:
        clients = get_k8s_clients()
        v1 = clients['core_v1']

        # 初始化节点统计
        node_stats = {}

        # 获取所有Pod
        pods = v1.list_pod_for_all_namespaces()

        for pod in pods.items:
            # 跳过已完成或失败的Pod
            if pod.status.phase in ['Succeeded', 'Failed']:
                continue

            node_name = pod.spec.node_name
            if not node_name:
                continue

            if node_name not in node_stats:
                node_stats[node_name] = {
                    'cpu_requests': 0,
                    'cpu_limits': 0,
                    'memory_requests': 0,
                    'memory_limits': 0
                }

            # 遍历容器
            containers = (pod.spec.containers or []) + (pod.spec.init_containers or [])

            for container in containers:
                if container.resources:
                    # 处理资源请求
                    if container.resources.requests:
                        cpu_req = container.resources.requests.get('cpu', '0')
                        memory_req = container.resources.requests.get('memory', '0')

                        node_stats[node_name]['cpu_requests'] += parse_cpu_value(cpu_req)
                        node_stats[node_name]['memory_requests'] += parse_memory_value(memory_req)

                    # 处理资源限制
                    if container.resources.limits:
                        cpu_limit = container.resources.limits.get('cpu', '0')
                        memory_limit = container.resources.limits.get('memory', '0')

                        node_stats[node_name]['cpu_limits'] += parse_cpu_value(cpu_limit)
                        node_stats[node_name]['memory_limits'] += parse_memory_value(memory_limit)

        return node_stats
    except ApiException as e:
        logging.error(f"获取Pod资源请求和限制时出错: {e}")
        return {}

def parse_cpu_value(cpu_str: str) -> float:
    """解析CPU值，转换为millicores"""
    if not cpu_str or cpu_str == '0':
        return 0

    cpu_str = str(cpu_str).lower()

    if cpu_str.endswith('m'):
        return float(cpu_str[:-1])
    elif cpu_str.endswith('n'):
        return float(cpu_str[:-1]) / 1000000
    elif cpu_str.endswith('u'):
        return float(cpu_str[:-1]) / 1000
    else:
        # 假设是核心数
        return float(cpu_str) * 1000

def parse_memory_value(memory_str: str) -> float:
    """解析内存值，转换为字节"""
    if not memory_str or memory_str == '0':
        return 0

    memory_str = str(memory_str).upper()

    # 定义单位转换
    units = {
        'K': 1024,
        'M': 1024**2,
        'G': 1024**3,
        'T': 1024**4,
        'KI': 1024,
        'MI': 1024**2,
        'GI': 1024**3,
        'TI': 1024**4,
        'KB': 1000,
        'MB': 1000**2,
        'GB': 1000**3,
        'TB': 1000**4
    }

    for unit, multiplier in units.items():
        if memory_str.endswith(unit):
            return float(memory_str[:-len(unit)]) * multiplier

    # 如果没有单位，假设是字节
    return float(memory_str)

def fetch_gpu_status() -> Dict[str, Dict[str, int]]:
    """通过HAMI API获取GPU使用情况"""
    gpu_status = {}

    try:
        # 从配置中获取HAMI API地址
        hami_api_url = hami_api_config.get('api_url', 'http://hami-scheduler:9090')

        # 获取GPU状态
        response = requests.get(f"{hami_api_url}/api/v1/gpu/status", timeout=10)

        if response.status_code == 200:
            data = response.json()

            # 解析GPU数据
            for node_data in data.get('nodes', []):
                node_name = node_data.get('name', '')
                if node_name:
                    gpu_status[node_name] = {
                        'gpu_core_used': node_data.get('gpu_core_used', 0),
                        'gpu_memory_used': node_data.get('gpu_memory_used', 0),
                        'gpu_core_total': node_data.get('gpu_core_total', 0),
                        'gpu_memory_total': node_data.get('gpu_memory_total', 0),
                        'gpu_task_num': node_data.get('gpu_task_num', 0)
                    }
        else:
            logging.warning(f"获取GPU状态失败: HTTP {response.status_code}")

    except Exception as e:
        logging.warning(f"获取GPU状态时出错: {e}")

    return gpu_status


def node_statistics() -> Dict[str, Dict]:
    """
    获取集群节点统计信息
    返回格式与prometheus_metrics.py中的node_statistics()函数相同
    """
    if not check_metrics_server():
        logging.warning("Metrics server is not running")
        return {}

    try:
        nodes = get_nodes()
        if not nodes:
            logging.warning("未找到可用节点")
            return {}

        status_this_time = {}

        # 获取GPU状态
        gpu_status = fetch_gpu_status()

        # 获取Pod资源请求和限制
        pod_resources = get_pod_resource_requests_limits()

        for node in nodes:
            try:
                # 获取节点基本信息
                allocatable = get_node_allocatable(node)
                capacity = get_node_capacity(node)
                images_size = get_node_images_size(node)

                # 获取实时使用情况
                node_metrics = get_node_metrics(node)

                # 解析allocatable资源
                allocatable_cpu = parse_cpu_value(allocatable.get('cpu', '0'))
                allocatable_memory = parse_memory_value(allocatable.get('memory', '0')) // 1000  # 转换为KB
                allocatable_storage = parse_memory_value(allocatable.get('ephemeral-storage', '0'))

                # 解析capacity资源
                capacity_cpu = parse_cpu_value(capacity.get('cpu', '0'))
                capacity_memory = parse_memory_value(capacity.get('memory', '0'))
                capacity_storage = parse_memory_value(capacity.get('ephemeral-storage', '0'))

                # 解析实时使用情况
                cpu_usage = parse_cpu_value(node_metrics.get('cpu', '0'))
                memory_usage = parse_memory_value(node_metrics.get('memory', '0'))

                # 获取Pod资源统计
                pod_stats = pod_resources.get(node, {
                    'cpu_requests': 0,
                    'cpu_limits': 0,
                    'memory_requests': 0,
                    'memory_limits': 0
                })

                # 构建节点状态
                status_this_time[node] = {
                    # Pod资源请求和限制
                    'cpu_requests': int(pod_stats['cpu_requests']),
                    'cpu_limits': int(pod_stats['cpu_limits']),
                    'memory_requests': int(pod_stats['memory_requests']),
                    'memory_limits': int(pod_stats['memory_limits']),

                    # 实时使用情况
                    'cpu_usage': int(cpu_usage),
                    'memory_usage': int(memory_usage),

                    # GPU信息（来自HAMI API）
                    'gpu_core_used': gpu_status.get(node, {}).get("gpu_core_used", 0),
                    'gpu_memory_used': gpu_status.get(node, {}).get("gpu_memory_used", 0),
                    'gpu_core_total': gpu_status.get(node, {}).get("gpu_core_total", 0),
                    'gpu_memory_total': gpu_status.get(node, {}).get("gpu_memory_total", 0),
                    'gpu_task_num': gpu_status.get(node, {}).get("gpu_task_num", 0),

                    # 可分配资源
                    'allocatable_cpu': int(allocatable_cpu),
                    'allocatable_memory': int(allocatable_memory),
                    'allocatable_pod': int(allocatable.get('pods', '0')),
                    'allocatable_ephemeral_storage': int(allocatable_storage),

                    # 总容量
                    'capacity_cpu': int(capacity_cpu),
                    'capacity_memory': int(capacity_memory),
                    'capacity_pod': int(capacity.get('pods', '0')),
                    'capacity_ephemeral_storage': int(capacity_storage),

                    # 镜像大小
                    'images_size': images_size
                }

                logging.info(f"成功获取节点 {node} 的统计信息")

            except Exception as e:
                logging.error(f"获取节点 {node} 统计信息时出错: {e}")
                # 创建默认值，避免整个函数失败
                status_this_time[node] = {
                    'cpu_requests': 0, 'cpu_limits': 0, 'memory_requests': 0, 'memory_limits': 0,
                    'cpu_usage': 0, 'memory_usage': 0,
                    'gpu_core_used': 0, 'gpu_memory_used': 0, 'gpu_core_total': 0,
                    'gpu_memory_total': 0, 'gpu_task_num': 0,
                    'allocatable_cpu': 0, 'allocatable_memory': 0, 'allocatable_pod': 0,
                    'allocatable_ephemeral_storage': 0,
                    'capacity_cpu': 0, 'capacity_memory': 0, 'capacity_pod': 0,
                    'capacity_ephemeral_storage': 0,
                    'images_size': 0
                }

        logging.info(f"成功获取 {len(status_this_time)} 个节点的统计信息")
        return status_this_time

    except Exception as e:
        logging.error(f"获取节点统计信息时出错: {e}")
        return {}


def get_cluster_summary() -> Dict:
    """获取集群资源使用摘要"""
    try:
        node_stats = node_statistics()

        if not node_stats:
            return {}

        # 汇总所有节点的资源
        summary = {
            'total_nodes': len(node_stats),
            'total_cpu_requests': 0,
            'total_cpu_limits': 0,
            'total_memory_requests': 0,
            'total_memory_limits': 0,
            'total_cpu_usage': 0,
            'total_memory_usage': 0,
            'total_gpu_core_used': 0,
            'total_gpu_memory_used': 0,
            'total_gpu_core_total': 0,
            'total_gpu_memory_total': 0,
            'total_gpu_task_num': 0,
            'total_allocatable_cpu': 0,
            'total_allocatable_memory': 0,
            'total_capacity_cpu': 0,
            'total_capacity_memory': 0,
            'total_images_size': 0
        }

        for node, stats in node_stats.items():
            for key in summary:
                if key.startswith('total_') and key != 'total_nodes':
                    stat_key = key.replace('total_', '')
                    summary[key] += stats.get(stat_key, 0)

        # 计算使用率
        if summary['total_allocatable_cpu'] > 0:
            summary['cpu_request_utilization'] = (summary['total_cpu_requests'] / summary['total_allocatable_cpu']) * 100
            summary['cpu_usage_utilization'] = (summary['total_cpu_usage'] / summary['total_allocatable_cpu']) * 100

        if summary['total_allocatable_memory'] > 0:
            summary['memory_request_utilization'] = (summary['total_memory_requests'] / summary['total_allocatable_memory']) * 100
            summary['memory_usage_utilization'] = (summary['total_memory_usage'] / summary['total_allocatable_memory']) * 100

        if summary['total_gpu_core_total'] > 0:
            summary['gpu_core_utilization'] = (summary['total_gpu_core_used'] / summary['total_gpu_core_total']) * 100

        if summary['total_gpu_memory_total'] > 0:
            summary['gpu_memory_utilization'] = (summary['total_gpu_memory_used'] / summary['total_gpu_memory_total']) * 100

        return summary

    except Exception as e:
        logging.error(f"获取集群摘要时出错: {e}")
        return {}


def print_node_statistics():
    """打印节点统计信息（用于调试）"""
    stats = node_statistics()

    if not stats:
        print("❌ 无法获取节点统计信息")
        return

    print("🖥️  集群节点资源统计")
    print("=" * 80)

    for node, data in stats.items():
        print(f"\n📊 节点: {node}")
        print("-" * 60)

        # CPU信息
        cpu_req_pct = (data['cpu_requests'] / data['allocatable_cpu'] * 100) if data['allocatable_cpu'] > 0 else 0
        cpu_usage_pct = (data['cpu_usage'] / data['allocatable_cpu'] * 100) if data['allocatable_cpu'] > 0 else 0
        print(f"🔧 CPU:")
        print(f"   请求: {data['cpu_requests']}m / {data['allocatable_cpu']}m ({cpu_req_pct:.1f}%)")
        print(f"   使用: {data['cpu_usage']}m / {data['allocatable_cpu']}m ({cpu_usage_pct:.1f}%)")
        print(f"   限制: {data['cpu_limits']}m")

        # 内存信息
        mem_req_pct = (data['memory_requests'] / data['allocatable_memory'] * 100) if data['allocatable_memory'] > 0 else 0
        mem_usage_pct = (data['memory_usage'] / data['allocatable_memory'] * 100) if data['allocatable_memory'] > 0 else 0
        print(f"💾 内存:")
        print(f"   请求: {data['memory_requests']//1024//1024}Mi / {data['allocatable_memory']//1024//1024}Mi ({mem_req_pct:.1f}%)")
        print(f"   使用: {data['memory_usage']//1024//1024}Mi / {data['allocatable_memory']//1024//1024}Mi ({mem_usage_pct:.1f}%)")
        print(f"   限制: {data['memory_limits']//1024//1024}Mi")

        # GPU信息
        if data['gpu_core_total'] > 0:
            gpu_core_pct = (data['gpu_core_used'] / data['gpu_core_total'] * 100) if data['gpu_core_total'] > 0 else 0
            gpu_mem_pct = (data['gpu_memory_used'] / data['gpu_memory_total'] * 100) if data['gpu_memory_total'] > 0 else 0
            print(f"🎮 GPU:")
            print(f"   核心: {data['gpu_core_used']} / {data['gpu_core_total']} ({gpu_core_pct:.1f}%)")
            print(f"   显存: {data['gpu_memory_used']}Mi / {data['gpu_memory_total']}Mi ({gpu_mem_pct:.1f}%)")
            print(f"   任务: {data['gpu_task_num']}")

        # 存储信息
        print(f"💿 存储:")
        print(f"   镜像大小: {data['images_size']//1024//1024//1024:.1f}GB")
        print(f"   可用存储: {data['allocatable_ephemeral_storage']//1024//1024//1024:.1f}GB")


if __name__ == '__main__':
    # 测试功能
    print("🚀 测试K8s统计功能")

    # 测试节点列表
    nodes = get_nodes()
    print(f"发现节点: {nodes}")

    # 测试节点统计
    print_node_statistics()

    # 测试集群摘要
    summary = get_cluster_summary()
    if summary:
        print(f"\n📈 集群摘要:")
        print(f"节点数量: {summary['total_nodes']}")
        print(f"CPU使用率: {summary.get('cpu_usage_utilization', 0):.1f}%")
        print(f"内存使用率: {summary.get('memory_usage_utilization', 0):.1f}%")
        if summary.get('total_gpu_core_total', 0) > 0:
            print(f"GPU使用率: {summary.get('gpu_core_utilization', 0):.1f}%")
