#!/usr/bin/env python3
"""
Kubernetes Pod调度可行性检查工具
检查Pod是否能够成功调度并立即执行
"""

from kubernetes import client, config
from kubernetes.client.rest import ApiException
from kubernetes.client import V1Pod, V1Taint, V1Toleration
import logging, traceback
from typing import Dict, List, Any
from dataclasses import dataclass
import re
import requests
from utils.retry_wrapper import retry_decorator
from airflow.models.variable import Variable

try:
    pod_operator_config = Variable.get('pod_operator_config', deserialize_json=True)
except Exception:
    pod_operator_config = {}


@retry_decorator(max_retries=5, delay=3)
def fetch_gpu_status():
    gpu_nodes = pod_operator_config.get("gpu_nodes", ["***********", "***********", "***********"])
    gpu_status = {k: {} for k in gpu_nodes}
    ip_pattern = r'(?:\d{1,3}\.){3}\d{1,3}'
    for gpu_node in gpu_nodes:
        response = requests.get(f"http://{gpu_node}:31993/metrics")
        if response.status_code == 200:
            for line in response.text.split("\n"):
                if "GPUDeviceCoreAllocated" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_core_used"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceMemoryAllocated" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_memory_used"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceCoreLimit" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_core_total"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceMemoryLimit" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_memory_total"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceSharedNum" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_task_num"] = int(float(line.split(" ")[-1]))
            break
        else:
            logging.warning(f"Error fetching gpu data: {response.status_code}")
    return gpu_status

@dataclass
class ScheduleCheckResult:
    """调度检查结果"""
    can_schedule: bool
    schedulable_nodes: List[str]
    reasons: List[str]
    warnings: List[str]
    resource_analysis: Dict[str, Any]

    def __str__(self):
        status = "✅ 可以调度" if self.can_schedule else "❌ 无法调度"
        result = f"{status}\n"

        if self.schedulable_nodes:
            result += f"可调度节点: {', '.join(self.schedulable_nodes)}\n"

        if self.reasons:
            result += "原因:\n" + "\n".join(f"  - {reason}" for reason in self.reasons)

        if self.warnings:
            result += "\n警告:\n" + "\n".join(f"  - {warning}" for warning in self.warnings)

        return result

class PodScheduleChecker:
    """Pod调度检查器"""

    def __init__(self):
        self.k8s_clients = self._get_k8s_clients()
        self.logger = logging.getLogger(__name__)
        self.gpu_status = fetch_gpu_status()

    def _get_k8s_clients(self) -> Dict[str, Any]:
        """获取Kubernetes客户端"""
        # try:
        #     config.load_incluster_config()
        # except config.ConfigException:
        try:
            config.load_kube_config()
        except config.ConfigException:
            raise Exception("无法加载Kubernetes配置")

        return {
            'core_v1': client.CoreV1Api(),
            'apps_v1': client.AppsV1Api(),
            'metrics': client.CustomObjectsApi(),
            'policy_v1': client.PolicyV1Api()
        }

    def check_pod_schedulability(self, pod: V1Pod, namespace: str = "default") -> ScheduleCheckResult:
        """
        检查Pod是否可以调度

        Args:
            pod: V1Pod对象
            namespace: 命名空间

        Returns:
            ScheduleCheckResult: 检查结果
        """
        reasons = []
        warnings = []
        schedulable_nodes = []
        resource_analysis = {}

        try:
            # 1. Dry Run检查
            dry_run_result = self._dry_run_check(pod, namespace)
            if not dry_run_result['success']:
                reasons.extend(dry_run_result['reasons'])
            else:
                warnings.extend(dry_run_result['warnings'])

            # 2. 获取可用节点
            nodes = self._get_available_nodes()
            if not nodes:
                reasons.append("集群中没有可用节点")
                return ScheduleCheckResult(False, [], reasons, warnings, resource_analysis)

            # 3. 逐个检查节点
            for node in nodes:
                node_check = self._check_node_compatibility(pod, node)

                if node_check['compatible']:
                    schedulable_nodes.append(node.metadata.name)
                else:
                    # 收集失败原因用于分析
                    for reason in node_check['reasons']:
                        if reason not in reasons:
                            reasons.append(f"节点 {node.metadata.name}: {reason}")

            # 4. 资源分析
            resource_analysis = self._analyze_resource_requirements(pod, nodes)

            # 5. 集群策略检查
            policy_check = self._check_cluster_policies(pod, namespace)
            if not policy_check['success']:
                reasons.extend(policy_check['reasons'])

            can_schedule = len(schedulable_nodes) > 0 and len([r for r in reasons if not r.startswith("节点")]) == 0

            return ScheduleCheckResult(
                can_schedule=can_schedule,
                schedulable_nodes=schedulable_nodes,
                reasons=reasons,
                warnings=warnings,
                resource_analysis=resource_analysis
            )

        except Exception as e:
            self.logger.error(f"调度检查时出错: {e}")
            logging.error(f"完整错误堆栈:\n{traceback.format_exc()}")
            return ScheduleCheckResult(
                can_schedule=False,
                schedulable_nodes=[],
                reasons=[f"检查过程出错: {str(e)}"],
                warnings=[],
                resource_analysis={}
            )

    def _dry_run_check(self, pod: V1Pod, namespace: str) -> Dict[str, Any]:
        """执行Dry Run检查"""
        try:
            self.k8s_clients['core_v1'].create_namespaced_pod(
                namespace=namespace,
                body=pod,
                dry_run="All"
            )
            return {'success': True, 'reasons': [], 'warnings': []}

        except ApiException as e:
            reasons = []
            if e.status == 403:
                reasons.append("权限不足，无法创建Pod")
            elif e.status == 422:
                reasons.append(f"Pod配置无效: {e.reason}")
            else:
                reasons.append(f"Dry run失败: {e.reason}")

            return {'success': False, 'reasons': reasons, 'warnings': []}
        except Exception as e:
            return {'success': False, 'reasons': [f"Dry run检查出错: {str(e)}"], 'warnings': []}

    def _get_available_nodes(self) -> List[Any]:
        """获取可用节点列表"""
        try:
            nodes_list = self.k8s_clients['core_v1'].list_node()
            available_nodes = []

            for node in nodes_list.items:
                # 检查节点是否Ready
                is_ready = False
                is_schedulable = True

                if node.status.conditions:
                    for condition in node.status.conditions:
                        if condition.type == "Ready" and condition.status == "True":
                            is_ready = True
                            break

                # 检查节点是否可调度
                if node.spec.unschedulable:
                    is_schedulable = False

                if is_ready and is_schedulable and node.metadata.name != "vci-node1-cn-beijing-a":
                    available_nodes.append(node)

            return available_nodes

        except Exception as e:
            self.logger.error(f"获取节点列表时出错: {e}")
            return []

    def _check_node_compatibility(self, pod: V1Pod, node: Any) -> Dict[str, Any]:
        """检查Pod与节点的兼容性"""
        reasons = []

        # 1. 检查NodeSelector
        if not self._check_node_selector(pod, node):
            reasons.append("NodeSelector不匹配")

        # 2. 检查污点和容忍
        if not self._check_taints_tolerations(pod, node):
            reasons.append("无法容忍节点污点")

        # 3. 检查节点亲和性
        if not self._check_node_affinity(pod, node):
            reasons.append("节点亲和性不满足")

        # 4. 检查资源可用性
        resource_check = self._check_node_resources(pod, node)
        if not resource_check['sufficient']:
            reasons.extend(resource_check['reasons'])

        # 5. 检查Pod亲和性/反亲和性
        affinity_check = self._check_pod_affinity(pod, node)
        if not affinity_check['compatible']:
            reasons.extend(affinity_check['reasons'])

        return {
            'compatible': len(reasons) == 0,
            'reasons': reasons
        }

    def _check_node_selector(self, pod: V1Pod, node: Any) -> bool:
        """检查NodeSelector"""
        if not pod.spec.node_selector:
            return True

        node_labels = node.metadata.labels or {}

        for key, value in pod.spec.node_selector.items():
            if node_labels.get(key) != value:
                return False

        return True

    def _check_taints_tolerations(self, pod: V1Pod, node: Any) -> bool:
        """检查污点和容忍"""
        if not node.spec.taints:
            return True

        pod_tolerations = pod.spec.tolerations or []

        for taint in node.spec.taints:
            # 检查是否有匹配的容忍
            tolerated = False

            for toleration in pod_tolerations:
                if self._toleration_matches_taint(toleration, taint):
                    tolerated = True
                    break

            # 如果污点效果是NoSchedule或NoExecute，且没有容忍，则不能调度
            if not tolerated and taint.effect in ["NoSchedule", "NoExecute"]:
                return False

        return True

    def _toleration_matches_taint(self, toleration: Any, taint: V1Taint) -> bool:
        """检查容忍是否匹配污点"""
        # 检查key匹配
        if isinstance(toleration, dict):
            toleration = V1Toleration(
                key=toleration.get("key"),
                operator=toleration.get("operator"),
                value=toleration.get("value"),
                effect=toleration.get("effect")
            )
        if isinstance(toleration, V1Toleration):
            ...
        if toleration.key and toleration.key != taint.key:
            return False

        # 检查operator
        if toleration.operator == "Equal":
            return toleration.value == taint.value
        elif toleration.operator == "Exists":
            return True

        return False

    def _check_node_affinity(self, pod: V1Pod, node: Any) -> bool:
        """检查节点亲和性"""
        if not pod.spec.affinity or not pod.spec.affinity.node_affinity:
            return True

        node_affinity = pod.spec.affinity.node_affinity
        node_labels = node.metadata.labels or {}

        # 检查required亲和性
        if node_affinity.required_during_scheduling_ignored_during_execution:
            required_terms = node_affinity.required_during_scheduling_ignored_during_execution.node_selector_terms

            # 至少一个term必须匹配
            term_matched = False
            for term in required_terms:
                if self._node_selector_term_matches(term, node_labels):
                    term_matched = True
                    break

            if not term_matched:
                return False

        return True

    def _node_selector_term_matches(self, term: Any, node_labels: Dict[str, str]) -> bool:
        """检查节点选择器term是否匹配"""
        # 检查matchExpressions
        if term.match_expressions:
            for expr in term.match_expressions:
                if not self._match_expression_satisfied(expr, node_labels):
                    return False

        # 检查matchFields（通常用于节点字段匹配）
        if term.match_fields:
            for field in term.match_fields:
                # 这里简化处理，实际可能需要更复杂的字段匹配逻辑
                if not self._match_field_satisfied(field, node_labels):
                    return False

        return True

    def _match_expression_satisfied(self, expr: Any, labels: Dict[str, str]) -> bool:
        """检查匹配表达式是否满足"""
        key = expr.key
        operator = expr.operator
        values = expr.values or []

        label_value = labels.get(key)

        if operator == "In":
            return label_value in values
        elif operator == "NotIn":
            return label_value not in values
        elif operator == "Exists":
            return key in labels
        elif operator == "DoesNotExist":
            return key not in labels
        elif operator == "Gt":
            try:
                return label_value and int(label_value) > int(values[0])
            except (ValueError, IndexError):
                return False
        elif operator == "Lt":
            try:
                return label_value and int(label_value) < int(values[0])
            except (ValueError, IndexError):
                return False

        return False

    def _match_field_satisfied(self, field: Any, node_labels: Dict[str, str]) -> bool:
        """检查字段匹配是否满足（简化实现）"""
        # 这里可以根据需要实现更复杂的字段匹配逻辑
        return True

    def _check_node_resources(self, pod: V1Pod, node: Any) -> Dict[str, Any]:
        """检查节点资源是否充足"""
        reasons = []

        try:
            # 获取节点可分配资源
            allocatable = node.status.allocatable

            # 获取节点当前资源使用情况
            node_usage = self._get_node_resource_usage(node.metadata.name)

            # 计算Pod资源需求
            pod_resources = self._calculate_pod_resources(pod)

            # 检查CPU
            available_cpu = self._parse_cpu_value(allocatable.get('cpu', '0')) - node_usage.get('cpu', 0)
            required_cpu = pod_resources.get('cpu', 0)

            if required_cpu > available_cpu:
                reasons.append(f"CPU不足: 需要{required_cpu}m, 可用{available_cpu}m")

            # 检查内存
            available_memory = self._parse_memory_value(allocatable.get('memory', '0')) - node_usage.get('memory', 0)
            required_memory = pod_resources.get('memory', 0)

            if required_memory > available_memory:
                reasons.append(f"内存不足: 需要{required_memory//1024//1024}Mi, 可用{available_memory//1024//1024}Mi")

            # 检查存储
            if 'ephemeral-storage' in allocatable:
                available_storage = self._parse_memory_value(allocatable['ephemeral-storage']) - node_usage.get('storage', 0)
                required_storage = pod_resources.get('storage', 0)

                if required_storage > available_storage:
                    reasons.append(f"存储不足: 需要{required_storage//1024//1024}Mi, 可用{available_storage//1024//1024}Mi")

            # GPU Num检查
            allocatable_gpu = 6 if self.gpu_status.get(node.metadata.name) else 0
            available_gpu = allocatable_gpu - self.gpu_status.get(node.metadata.name, {}).get('gpu_task_num', 0)
            required_gpu = pod_resources.get('gpu', 0)
            if required_gpu > available_gpu:
                reasons.append(f"GPU不足: 需要{required_gpu}, 可用{available_gpu}")

            # GPU Core检查
            available_gpu_core = self.gpu_status.get(node.metadata.name, {}).get("gpu_core_total", 0) - self.gpu_status.get(node.metadata.name, {}).get("gpu_core_used", 0)
            required_gpu_core = pod_resources.get('gpu_cores', 0)
            if required_gpu_core > available_gpu_core:
                reasons.append(f"GPU Core不足: 需要{required_gpu_core}, 可用{available_gpu_core}")

            # GPU Memory检查
            available_gpu_memory = self.gpu_status.get(node.metadata.name, {}).get("gpu_memory_total", 0) - self.gpu_status.get(node.metadata.name, {}).get("gpu_memory_used", 0)
            required_gpu_memory = pod_resources.get('gpu_memory', 0)
            if required_gpu_memory > available_gpu_memory:
                reasons.append(f"GPU Memory不足: 需要{required_gpu_memory}, 可用{available_gpu_memory}")

            return {
                'sufficient': len(reasons) == 0,
                'reasons': reasons
            }

        except Exception as e:
            return {
                'sufficient': False,
                'reasons': [f"资源检查出错: {str(e)}"]
            }

    def _get_node_resource_usage(self, node_name: str) -> Dict[str, Any]:
        """获取节点当前资源使用情况"""
        try:
            # 尝试从metrics API获取
            metrics = self.k8s_clients['metrics'].get_cluster_custom_object(
                group="metrics.k8s.io",
                version="v1beta1",
                plural="nodes",
                name=node_name
            )

            usage = metrics.get('usage', {})
            return {
                'cpu': self._parse_cpu_value(usage.get('cpu', '0')),
                'memory': self._parse_memory_value(usage.get('memory', '0')),
                'storage': 0,  # metrics API通常不包含存储使用情况
                'extended': {}
            }

        except Exception:
            # 如果metrics API不可用，返回保守估计
            return {'cpu': 0, 'memory': 0, 'storage': 0, 'extended': {}}

    def _calculate_pod_resources(self, pod: V1Pod) -> Dict[str, Any]:
        """计算Pod资源需求"""
        total_cpu = 0
        total_memory = 0
        total_storage = 0
        total_gpu = 0
        total_gpu_cores = 0
        total_gpu_memory = 0

        # 遍历所有容器
        containers = (pod.spec.containers or []) + (pod.spec.init_containers or [])

        for container in containers:
            if container.resources and container.resources.requests:
                requests = container.resources.requests

                # CPU
                if 'cpu' in requests:
                    total_cpu += self._parse_cpu_value(requests['cpu'])

                # 内存
                if 'memory' in requests:
                    total_memory += self._parse_memory_value(requests['memory'])

                # 存储
                if 'ephemeral-storage' in requests:
                    total_storage += self._parse_memory_value(requests['ephemeral-storage'])

                # GPU num
                if 'nvidia.com/gpu' in requests:
                    total_gpu += self._parse_int(requests['nvidia.com/gpu'])

                # GPU Core
                if 'nvidia.com/gpucores' in requests:
                    total_gpu_cores += self._parse_int(requests['nvidia.com/gpucores'])

                # GPU Memory
                if 'nvidia.com/gpumem' in requests:
                    total_gpu_memory += self._parse_memory_value(f"{requests['nvidia.com/gpumem']}Mi")

        return {
            'cpu': total_cpu,
            'memory': total_memory,
            'storage': total_storage,
            'gpu': total_gpu,
            'gpu_cores': total_gpu_cores,
            'gpu_memory': total_gpu_memory
        }

    def _parse_cpu_value(self, cpu_str: str) -> float:
        """解析CPU值，转换为millicores"""
        if not cpu_str or cpu_str == '0':
            return 0

        cpu_str = str(cpu_str).lower()

        if cpu_str.endswith('m'):
            return float(cpu_str[:-1])
        elif cpu_str.endswith('n'):
            return float(cpu_str[:-1]) / 1000000
        elif cpu_str.endswith('u'):
            return float(cpu_str[:-1]) / 1000
        else:
            # 假设是核心数
            return float(cpu_str) * 1000

    def _parse_memory_value(self, memory_str: str) -> float:
        """解析内存值，转换为字节"""
        if not memory_str or memory_str == '0':
            return 0

        memory_str_original = str(memory_str)
        memory_str = memory_str_original.upper()

        # 处理milli-bytes单位（Kubernetes特有）
        # 检查原始字符串是否以小写m结尾（表示milli-bytes）
        if memory_str_original.endswith('m') and not memory_str.endswith(('MI', 'MB')):
            # 这是milli-bytes，转换为字节
            return float(memory_str_original[:-1]) / 1000

        # 定义标准单位转换
        units = {
            'K': 1024, 'M': 1024**2, 'G': 1024**3, 'T': 1024**4,
            'KI': 1024, 'MI': 1024**2, 'GI': 1024**3, 'TI': 1024**4,
            'KB': 1000, 'MB': 1000**2, 'GB': 1000**3, 'TB': 1000**4
        }

        for unit, multiplier in units.items():
            if memory_str.endswith(unit):
                return float(memory_str[:-len(unit)]) * multiplier

        # 如果没有单位，假设是字节
        return float(memory_str)

    def _parse_int(self, resource_str: str) -> int:
        if isinstance(resource_str, int):
            return resource_str
        """解析整数资源值"""
        resource_str = re.sub(r'\D', '', resource_str)
        if not resource_str or resource_str == '0':
            return 0

        try:
            return int(resource_str)
        except ValueError:
            return 0

    def _parse_resource_value(self, resource_str: str) -> float:
        """解析资源值（通用）"""
        if not resource_str or resource_str == '0':
            return 0

        try:
            return float(resource_str)
        except ValueError:
            # 如果包含单位，尝试解析
            return self._parse_memory_value(resource_str)

    def _check_pod_affinity(self, pod: V1Pod, node: Any) -> Dict[str, Any]:
        """检查Pod亲和性/反亲和性"""
        reasons = []

        if not pod.spec.affinity:
            return {'compatible': True, 'reasons': []}

        try:
            # 获取节点上现有的Pod
            existing_pods = self.k8s_clients['core_v1'].list_pod_for_all_namespaces(
                field_selector=f"spec.nodeName={node.metadata.name}"
            )

            # 检查Pod亲和性
            if pod.spec.affinity.pod_affinity:
                affinity_satisfied = self._check_pod_affinity_rules(
                    pod.spec.affinity.pod_affinity, existing_pods.items, True
                )
                if not affinity_satisfied:
                    reasons.append("Pod亲和性规则不满足")

            # 检查Pod反亲和性
            if pod.spec.affinity.pod_anti_affinity:
                anti_affinity_satisfied = self._check_pod_affinity_rules(
                    pod.spec.affinity.pod_anti_affinity, existing_pods.items, False
                )
                if not anti_affinity_satisfied:
                    reasons.append("Pod反亲和性规则冲突")

            return {
                'compatible': len(reasons) == 0,
                'reasons': reasons
            }

        except Exception as e:
            return {
                'compatible': True,  # 出错时假设兼容，避免误判
                'reasons': [f"Pod亲和性检查出错: {str(e)}"]
            }

    def _check_pod_affinity_rules(self, affinity_rules: Any, existing_pods: List[Any], is_affinity: bool) -> bool:
        """检查Pod亲和性规则"""
        # 简化实现，实际情况可能更复杂
        if not affinity_rules:
            return True

        # 检查required规则
        if hasattr(affinity_rules, 'required_during_scheduling_ignored_during_execution'):
            required_terms = affinity_rules.required_during_scheduling_ignored_during_execution or []

            for term in required_terms:
                if not self._pod_affinity_term_satisfied(term, existing_pods, is_affinity):
                    return False

        return True

    def _pod_affinity_term_satisfied(self, term: Any, existing_pods: List[Any], is_affinity: bool) -> bool:
        """检查Pod亲和性term是否满足"""
        # 简化实现
        matching_pods = []

        for pod in existing_pods:
            if pod.status.phase in ['Running', 'Pending']:
                pod_labels = pod.metadata.labels or {}

                # 检查标签选择器
                if term.label_selector:
                    if self._label_selector_matches(term.label_selector, pod_labels):
                        matching_pods.append(pod)

        # 对于亲和性，需要有匹配的Pod；对于反亲和性，不能有匹配的Pod
        if is_affinity:
            return len(matching_pods) > 0
        else:
            return len(matching_pods) == 0

    def _label_selector_matches(self, selector: Any, labels: Dict[str, str]) -> bool:
        """检查标签选择器是否匹配"""
        # 检查matchLabels
        if hasattr(selector, 'match_labels') and selector.match_labels:
            for key, value in selector.match_labels.items():
                if labels.get(key) != value:
                    return False

        # 检查matchExpressions
        if hasattr(selector, 'match_expressions') and selector.match_expressions:
            for expr in selector.match_expressions:
                if not self._match_expression_satisfied(expr, labels):
                    return False

        return True

    def _check_cluster_policies(self, pod: V1Pod, namespace: str) -> Dict[str, Any]:
        """检查集群策略（ResourceQuota、LimitRange等）"""
        reasons = []

        try:
            # 检查ResourceQuota
            quota_check = self._check_resource_quota(pod, namespace)
            if not quota_check['success']:
                reasons.extend(quota_check['reasons'])

            # 检查LimitRange
            limit_check = self._check_limit_range(pod, namespace)
            if not limit_check['success']:
                reasons.extend(limit_check['reasons'])

            return {
                'success': len(reasons) == 0,
                'reasons': reasons
            }

        except Exception as e:
            return {
                'success': True,  # 出错时假设通过，避免误判
                'reasons': [f"策略检查出错: {str(e)}"]
            }

    def _check_resource_quota(self, pod: V1Pod, namespace: str) -> Dict[str, Any]:
        """检查ResourceQuota限制"""
        try:
            quotas = self.k8s_clients['core_v1'].list_namespaced_resource_quota(namespace=namespace)

            if not quotas.items:
                return {'success': True, 'reasons': []}

            pod_resources = self._calculate_pod_resources(pod)
            reasons = []

            for quota in quotas.items:
                if quota.status and quota.status.hard:
                    hard_limits = quota.status.hard
                    used_resources = quota.status.used or {}

                    # 检查CPU配额
                    if 'requests.cpu' in hard_limits:
                        cpu_limit = self._parse_cpu_value(hard_limits['requests.cpu'])
                        cpu_used = self._parse_cpu_value(used_resources.get('requests.cpu', '0'))
                        cpu_needed = pod_resources.get('cpu', 0)

                        if cpu_used + cpu_needed > cpu_limit:
                            reasons.append(f"CPU配额不足: 需要{cpu_needed}m, 剩余{cpu_limit - cpu_used}m")

                    # 检查内存配额
                    if 'requests.memory' in hard_limits:
                        memory_limit = self._parse_memory_value(hard_limits['requests.memory'])
                        memory_used = self._parse_memory_value(used_resources.get('requests.memory', '0'))
                        memory_needed = pod_resources.get('memory', 0)

                        if memory_used + memory_needed > memory_limit:
                            reasons.append(f"内存配额不足: 需要{memory_needed//1024//1024}Mi, 剩余{(memory_limit - memory_used)//1024//1024}Mi")

            return {
                'success': len(reasons) == 0,
                'reasons': reasons
            }

        except Exception as e:
            return {'success': True, 'reasons': [f"ResourceQuota检查出错: {str(e)}"]}

    def _check_limit_range(self, pod: V1Pod, namespace: str) -> Dict[str, Any]:
        """检查LimitRange限制"""
        try:
            limit_ranges = self.k8s_clients['core_v1'].list_namespaced_limit_range(namespace=namespace)

            if not limit_ranges.items:
                return {'success': True, 'reasons': []}

            reasons = []

            for limit_range in limit_ranges.items:
                if limit_range.spec and limit_range.spec.limits:
                    for limit in limit_range.spec.limits:
                        if limit.type in ['Container', 'Pod']:
                            limit_check = self._check_container_limits(pod, limit)
                            if not limit_check['success']:
                                reasons.extend(limit_check['reasons'])

            return {
                'success': len(reasons) == 0,
                'reasons': reasons
            }

        except Exception as e:
            return {'success': True, 'reasons': [f"LimitRange检查出错: {str(e)}"]}

    def _check_container_limits(self, pod: V1Pod, limit: Any) -> Dict[str, Any]:
        """检查容器资源限制"""
        reasons = []

        containers = (pod.spec.containers or []) + (pod.spec.init_containers or [])

        for container in containers:
            if container.resources:
                # 检查requests限制
                if container.resources.requests and limit.min:
                    for resource, min_value in limit.min.items():
                        container_value = container.resources.requests.get(resource)
                        if container_value:
                            if resource == 'cpu':
                                if self._parse_cpu_value(container_value) < self._parse_cpu_value(min_value):
                                    reasons.append(f"容器{container.name} CPU请求过低: {container_value} < {min_value}")
                            elif resource == 'memory':
                                if self._parse_memory_value(container_value) < self._parse_memory_value(min_value):
                                    reasons.append(f"容器{container.name} 内存请求过低: {container_value} < {min_value}")

                # 检查limits限制
                if container.resources.limits and limit.max:
                    for resource, max_value in limit.max.items():
                        container_value = container.resources.limits.get(resource)
                        if container_value:
                            if resource == 'cpu':
                                if self._parse_cpu_value(container_value) > self._parse_cpu_value(max_value):
                                    reasons.append(f"容器{container.name} CPU限制过高: {container_value} > {max_value}")
                            elif resource == 'memory':
                                if self._parse_memory_value(container_value) > self._parse_memory_value(max_value):
                                    reasons.append(f"容器{container.name} 内存限制过高: {container_value} > {max_value}")

        return {
            'success': len(reasons) == 0,
            'reasons': reasons
        }

    def _analyze_resource_requirements(self, pod: V1Pod, nodes: List[Any]) -> Dict[str, Any]:
        """分析资源需求"""
        pod_resources = self._calculate_pod_resources(pod)

        # 统计集群资源情况
        total_allocatable_cpu = 0
        total_allocatable_memory = 0
        total_available_cpu = 0
        total_available_memory = 0

        for node in nodes:
            allocatable = node.status.allocatable
            usage = self._get_node_resource_usage(node.metadata.name)

            node_cpu = self._parse_cpu_value(allocatable.get('cpu', '0'))
            node_memory = self._parse_memory_value(allocatable.get('memory', '0'))

            total_allocatable_cpu += node_cpu
            total_allocatable_memory += node_memory
            total_available_cpu += (node_cpu - usage.get('cpu', 0))
            total_available_memory += (node_memory - usage.get('memory', 0))

        return {
            'pod_requirements': pod_resources,
            'cluster_allocatable': {
                'cpu': total_allocatable_cpu,
                'memory': total_allocatable_memory
            },
            'cluster_available': {
                'cpu': total_available_cpu,
                'memory': total_available_memory
            },
            'resource_pressure': {
                'cpu': (pod_resources.get('cpu', 0) / total_available_cpu * 100) if total_available_cpu > 0 else 0,
                'memory': (pod_resources.get('memory', 0) / total_available_memory * 100) if total_available_memory > 0 else 0
            }
        }

global_pod_schedule_checker = PodScheduleChecker()

# 便捷函数
def check_pod_schedule(pod: V1Pod, namespace: str = "default") -> ScheduleCheckResult:
    """
    检查Pod是否可以调度的便捷函数

    Args:
        pod: V1Pod对象
        namespace: 命名空间

    Returns:
        ScheduleCheckResult: 检查结果
    """
    result = global_pod_schedule_checker.check_pod_schedulability(pod, namespace)
    logging.info(f"check pod {pod.metadata.name} schedulability result: {result}")
    return result


if __name__ == '__main__':
    # 使用示例
    from kubernetes.client import V1Pod, V1ObjectMeta, V1PodSpec, V1Container, V1ResourceRequirements

    # 创建一个测试Pod
    test_pod = V1Pod(
        metadata=V1ObjectMeta(name="test-pod"),
        spec=V1PodSpec(
            containers=[
                V1Container(
                    name="test-container",
                    image="nginx:latest",
                    resources=V1ResourceRequirements(
                        requests={"cpu": "20", "memory": "12Gi"},
                        limits={"cpu": "24", "memory": "51Gi"}
                        # requests={"cpu": "20", "memory": "12Gi", "nvidia.com/gpu":"1", "nvidia.com/gpumem":"5000", "nvidia.com/gpucores":"20"},
                        # limits={"cpu": "24", "memory": "51Gi", "nvidia.com/gpu":"1", "nvidia.com/gpumem":"5000", "nvidia.com/gpucores":"20"}
                    ),
                    image_pull_policy="IfNotPresent"
                )
            ],
            node_selector={"node-type": "database"}
        )
    )

    # 检查调度可行性
    result = check_pod_schedule(test_pod, "default")
    print(result)
    print(result.resource_analysis)
