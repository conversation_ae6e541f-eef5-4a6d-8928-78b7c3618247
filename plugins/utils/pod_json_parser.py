#!/usr/bin/env python3
"""
Pod JSON Parser - 将JSON中的pod信息按照k8s SDK格式进行划分和转换
"""

import json, logging
from kubernetes.client import models as k8s
from typing import Dict, List, Any, Optional


class PodJsonParser:
    def __init__(self, json_text: str):
        """初始化解析器"""
        self.json_text = json_text
        self.raw_data = self._parse_json_string(json_text)

        # 四个主要部分的变量
        self.metadata_config = {}
        self.pod_spec_config = {}
        self.containers_config = []
        self.init_containers_config = []

        # 解析数据
        self._parse_data()

    def _parse_json_string(self, json_str: str) -> Dict[str, Any]:
        """解析JSON字符串"""
        if not json_str:
            return {}
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            return {}

    def _parse_env_string(self, env_str: str) -> List[Dict[str, str]]:
        """解析环境变量字符串"""
        if not env_str:
            return []
        try:
            env_list = json.loads(env_str)
            return [{"name": env["name"], "value": env["value"]} for env in env_list]
        except (json.JSONDecodeError, KeyError):
            return []

    def _parse_volumes_string(self, volumes_str: str) -> List[Dict[str, Any]]:
        """解析volumes字符串"""
        if not volumes_str:
            return []
        try:
            volumes_data = json.loads(volumes_str)
            return volumes_data.get("volumes", [])
        except json.JSONDecodeError:
            return []

    def _parse_volume_mounts(self, volumes_str: str) -> List[Dict[str, str]]:
        """解析volume mounts"""
        if not volumes_str:
            return []
        try:
            volumes_data = json.loads(volumes_str)
            return volumes_data.get("volume_mounts", [])
        except json.JSONDecodeError:
            return []

    def _validate_node_selector(self, node_selector: Dict[str, str]) -> Dict[str, Any]:
        """校验node selector配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "data": node_selector
        }

        if not isinstance(node_selector, dict):
            validation_result["valid"] = False
            validation_result["errors"].append("node_selector must be a dictionary")
            return validation_result

        # 校验键值对格式
        for key, value in node_selector.items():
            if not isinstance(key, str) or not isinstance(value, str):
                validation_result["errors"].append(f"node_selector key-value pairs must be strings: {key}={value}")
                validation_result["valid"] = False

            # 校验kubernetes标签格式
            if not self._is_valid_k8s_label_key(key):
                validation_result["warnings"].append(f"node_selector key may not be valid k8s label: {key}")

            if not self._is_valid_k8s_label_value(value):
                validation_result["warnings"].append(f"node_selector value may not be valid k8s label: {value}")

        return validation_result

    def _validate_affinity(self, affinity: Dict[str, Any]) -> Dict[str, Any]:
        """校验affinity配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "data": affinity
        }

        if not isinstance(affinity, dict):
            validation_result["valid"] = False
            validation_result["errors"].append("affinity must be a dictionary")
            return validation_result

        # 校验node_affinity结构
        if "node_affinity" in affinity:
            node_affinity = affinity["node_affinity"]
            if not isinstance(node_affinity, dict):
                validation_result["errors"].append("node_affinity must be a dictionary")
                validation_result["valid"] = False
            else:
                # 校验preferred_during_scheduling_ignored_during_execution
                if "preferred_during_scheduling_ignored_during_execution" in node_affinity:
                    preferred = node_affinity["preferred_during_scheduling_ignored_during_execution"]
                    if not isinstance(preferred, list):
                        validation_result["errors"].append("preferred_during_scheduling_ignored_during_execution must be a list")
                        validation_result["valid"] = False
                    else:
                        for i, item in enumerate(preferred):
                            if not isinstance(item, dict):
                                validation_result["errors"].append(f"preferred item {i} must be a dictionary")
                                validation_result["valid"] = False
                                continue

                            # 校验weight字段
                            if "weight" in item and not isinstance(item["weight"], int):
                                validation_result["errors"].append(f"preferred item {i} weight must be an integer")
                                validation_result["valid"] = False

                            # 校验preference字段
                            if "preference" not in item:
                                validation_result["errors"].append(f"preferred item {i} must have preference field")
                                validation_result["valid"] = False

        return validation_result

    def _validate_tolerations(self, tolerations: List[Dict[str, str]]) -> Dict[str, Any]:
        """校验tolerations配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "data": tolerations
        }

        if not isinstance(tolerations, list):
            validation_result["valid"] = False
            validation_result["errors"].append("tolerations must be a list")
            return validation_result

        valid_operators = ["Exists", "Equal"]
        valid_effects = ["NoSchedule", "PreferNoSchedule", "NoExecute"]

        for i, toleration in enumerate(tolerations):
            if not isinstance(toleration, dict):
                validation_result["errors"].append(f"toleration {i} must be a dictionary")
                validation_result["valid"] = False
                continue

            # 校验operator字段
            if "operator" in toleration:
                if toleration["operator"] not in valid_operators:
                    validation_result["errors"].append(f"toleration {i} operator must be one of {valid_operators}")
                    validation_result["valid"] = False

            # 校验effect字段
            if "effect" in toleration:
                if toleration["effect"] not in valid_effects:
                    validation_result["errors"].append(f"toleration {i} effect must be one of {valid_effects}")
                    validation_result["valid"] = False

            # 校验key和value的组合
            if toleration.get("operator") == "Equal" and "value" not in toleration:
                validation_result["warnings"].append(f"toleration {i} with Equal operator should have a value")

            if toleration.get("operator") == "Exists" and "value" in toleration:
                validation_result["warnings"].append(f"toleration {i} with Exists operator should not have a value")

        return validation_result

    def _is_valid_k8s_label_key(self, key: str) -> bool:
        """校验kubernetes标签键格式"""
        import re
        # 简化的k8s标签键校验
        if len(key) > 63:
            return False
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-_.]*[a-zA-Z0-9])?$'
        return bool(re.match(pattern, key))

    def _is_valid_k8s_label_value(self, value: str) -> bool:
        """校验kubernetes标签值格式"""
        import re
        # 简化的k8s标签值校验
        if len(value) > 63:
            return False
        if not value:  # 空值是允许的
            return True
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-_.]*[a-zA-Z0-9])?$'
        return bool(re.match(pattern, value))

    def _convert_resources(self, container_key: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
        """转换资源配置"""
        resources = {
            "requests": {},
            "limits": {}
        }

        # CPU资源 (millicores)
        if container_key.get("cpuRequestSize"):
            resources["requests"]["cpu"] = f"{container_key['cpuRequestSize']}m"
        if container_key.get("cpuLimitSize"):
            resources["limits"]["cpu"] = f"{container_key['cpuLimitSize']}m"

        # 内存资源 (MiB)
        if container_key.get("memoryRequestSize"):
            resources["requests"]["memory"] = f"{container_key['memoryRequestSize']}Mi"
        if container_key.get("memoryLimitSize"):
            resources["limits"]["memory"] = f"{container_key['memoryLimitSize']}Mi"

        # 存储资源 (MiB)
        if container_key.get("storageRequestSize"):
            resources["requests"]["ephemeral-storage"] = f"{container_key['storageRequestSize']}Mi"
        if container_key.get("storageLimitSize"):
            resources["limits"]["ephemeral-storage"] = f"{container_key['storageLimitSize']}Mi"

        # GPU资源
        if container_key.get("gpuRequestSize"):
            resources["requests"]["nvidia.com/gpu"] = str(container_key["gpuRequestSize"])
        if container_key.get("gpuLimitSize"):
            resources["limits"]["nvidia.com/gpu"] = str(container_key["gpuLimitSize"])

        return resources

    def _parse_containers(self):
        """解析containers配置"""
        container_map = self.raw_data.get("containerMap", {})

        for container_name, container_data in container_map.items():
            logging.info("container_name: %s" % container_name)
            container_key = container_data.get("key", {})
            container_value = container_data.get("value", {})

            # 解析环境变量
            env_vars = self._parse_env_string(container_key.get("envString", ""))

            # 解析volume mounts
            volume_mounts = self._parse_volume_mounts(container_key.get("volumesString", ""))

            # 解析资源配置
            resources = self._convert_resources(container_key)

            # 构建container配置
            container_config = {
                "name": container_name,
                "image": f"{container_value.get('imageName', '')}:{container_value.get('imageVersion', 'latest')}",
                "image_pull_policy": container_value.get("pullPolicy", "IfNotPresent"),
                "command": ["/bin/bash", "-c"] if container_value.get("runningArgs") else None,
                "args": [container_value.get("runningArgs")] if container_value.get("runningArgs") else None,
                "env": env_vars,
                "resources": resources,
                "volume_mounts": volume_mounts
            }

            # 清理None值
            container_config = {k: v for k, v in container_config.items() if v is not None}
            logging.info(f"container_config: {container_config}")
            self.containers_config.append(container_config)

    def _parse_init_containers(self):
        """解析init containers配置"""
        init_container_list = self.raw_data.get("initContainerList", [])

        for init_container_data in init_container_list:
            container_key = init_container_data.get("key", {})
            container_value = init_container_data.get("value", {})

            # 解析环境变量
            env_vars = self._parse_env_string(container_key.get("envString", ""))

            # 解析volume mounts
            volume_mounts = self._parse_volume_mounts(container_key.get("volumesString", ""))

            # 解析资源配置
            resources = self._convert_resources(container_key)

            # 构建init container配置
            init_container_config = {
                "name": f"init-{container_value.get('id', 'container')}",
                "image": f"{container_value.get('imageName', '')}:{container_value.get('imageVersion', 'latest')}",
                "image_pull_policy": container_value.get("pullPolicy", "IfNotPresent"),
                "command": ["/bin/bash", "-c"] if container_value.get("runningArgs") else None,
                "args": [container_value.get("runningArgs")] if container_value.get("runningArgs") else None,
                "env": env_vars,
                "resources": resources,
                "volume_mounts": volume_mounts
            }

            # 清理None值
            init_container_config = {k: v for k, v in init_container_config.items() if v is not None}
            logging.info(f"init_container_config: {init_container_config}")
            self.init_containers_config.append(init_container_config)

    def _parse_metadata(self):
        """解析metadata配置"""
        self.metadata_config = {
            "name": self.raw_data.get("taskType", "").lower().replace("_", "-") + "-" + self.raw_data.get("id", ""),
            "labels": {
                "task-name": self.raw_data.get("taskName", ""),
                "task-id": self.raw_data.get("id", ""),
                "task-type": self.raw_data.get("taskType", "")
            },
            "annotations": {
                "description": self.raw_data.get("taskDesc", ""),
                "task-type": self.raw_data.get("taskType", "")
            }
        }

    def _parse_pod_spec(self):
        """解析pod spec配置（不包括containers和init_containers）"""
        # 从第一个container获取通用配置
        first_container_key = {}
        if self.raw_data.get("containerMap"):
            first_container = list(self.raw_data["containerMap"].values())[0]
            first_container_key = first_container.get("key", {})

        # 解析node selector
        node_selector = self._parse_json_string(first_container_key.get("nodeSelector", ""))

        # 解析affinity
        affinity_data = self._parse_json_string(first_container_key.get("affinity", ""))
        affinity = None
        if affinity_data:
            affinity = {
                "node_affinity": {
                    "preferred_during_scheduling_ignored_during_execution":
                        affinity_data.get("preferredDuringSchedulingIgnoredDuringExecution", [])
                }
            }

        # 解析tolerations
        tolerations_data = self._parse_json_string(first_container_key.get("tolerations", ""))
        tolerations = []
        if tolerations_data:
            tolerations = [tolerations_data]

        # 解析volumes
        volumes = self._parse_volumes_string(self.raw_data.get("volumesString", ""))

        self.pod_spec_config = {
            "restart_policy": "Never",
            "node_selector": node_selector,
            "affinity": affinity,
            "tolerations": tolerations,
            "volumes": volumes,
            "priority_class_name": None,  # 可以根据priority字段设置
            "service_account_name": "default"
        }

        # 清理None值
        self.pod_spec_config = {k: v for k, v in self.pod_spec_config.items() if v is not None}

    def _parse_data(self):
        """解析所有数据"""
        self._parse_metadata()
        self._parse_containers()
        self._parse_init_containers()
        self._parse_pod_spec()

    def get_k8s_pod_spec(self) -> k8s.V1PodSpec:
        """获取k8s PodSpec对象"""
        # 转换containers
        containers = []
        for container_config in self.containers_config:
            container = k8s.V1Container(
                name=container_config["name"],
                image=container_config["image"],
                image_pull_policy=container_config.get("image_pull_policy"),
                command=container_config.get("command"),
                args=container_config.get("args"),
                env=[k8s.V1EnvVar(name=env["name"], value=env["value"])
                     for env in container_config.get("env", [])],
                resources=k8s.V1ResourceRequirements(
                    requests=container_config.get("resources", {}).get("requests"),
                    limits=container_config.get("resources", {}).get("limits")
                ),
                volume_mounts=[k8s.V1VolumeMount(
                    name=vm["name"],
                    mount_path=vm["mount_path"]
                ) for vm in container_config.get("volume_mounts", [])]
            )
            containers.append(container)

        # 转换init containers
        init_containers = []
        for init_config in self.init_containers_config:
            init_container = k8s.V1Container(
                name=init_config["name"],
                image=init_config["image"],
                image_pull_policy=init_config.get("image_pull_policy"),
                command=init_config.get("command"),
                args=init_config.get("args"),
                env=[k8s.V1EnvVar(name=env["name"], value=env["value"])
                     for env in init_config.get("env", [])],
                resources=k8s.V1ResourceRequirements(
                    requests=init_config.get("resources", {}).get("requests"),
                    limits=init_config.get("resources", {}).get("limits")
                )
            )
            init_containers.append(init_container)

        # 转换volumes
        volumes = []
        for volume_config in self.pod_spec_config.get("volumes", []):
            volume = k8s.V1Volume(name=volume_config["name"])

            if "empty_dir" in volume_config:
                empty_dir_config = volume_config["empty_dir"]
                volume.empty_dir = k8s.V1EmptyDirVolumeSource(
                    medium=empty_dir_config.get("medium"),
                    size_limit=empty_dir_config.get("size_limit")
                )
            elif "config_map" in volume_config:
                config_map_config = volume_config["config_map"]
                volume.config_map = k8s.V1ConfigMapVolumeSource(
                    name=config_map_config["name"],
                    default_mode=config_map_config.get("default_mode"),
                    optional=config_map_config.get("optional")
                )

            volumes.append(volume)

        # 创建PodSpec
        pod_spec = k8s.V1PodSpec(
            containers=containers,
            init_containers=init_containers if init_containers else None,
            restart_policy=self.pod_spec_config.get("restart_policy"),
            node_selector=self.pod_spec_config.get("node_selector"),
            tolerations=[k8s.V1Toleration(
                key=tol.get("key"),
                operator=tol.get("operator"),
                value=tol.get("value"),
                effect=tol.get("effect")
            ) for tol in self.pod_spec_config.get("tolerations", [])],
            volumes=volumes if volumes else None,
            service_account_name=self.pod_spec_config.get("service_account_name")
        )

        return pod_spec

    def get_k8s_metadata(self) -> k8s.V1ObjectMeta:
        """获取k8s ObjectMeta对象"""
        return k8s.V1ObjectMeta(
            name=self.metadata_config["name"],
            labels=self.metadata_config["labels"],
            annotations=self.metadata_config["annotations"]
        )

    def get_k8s_pod(self) -> k8s.V1Pod:
        """获取完整的k8s Pod对象"""
        return k8s.V1Pod(
            api_version="v1",
            kind="Pod",
            metadata=self.get_k8s_metadata(),
            spec=self.get_k8s_pod_spec()
        )

    def validate_configurations(self) -> Dict[str, Any]:
        """校验所有配置"""
        validation_results = {
            "overall_valid": True,
            "node_selector": None,
            "affinity": None,
            "tolerations": None,
            "summary": {
                "total_errors": 0,
                "total_warnings": 0,
                "components_with_errors": [],
                "components_with_warnings": []
            }
        }

        # 校验node_selector
        if self.pod_spec_config.get("node_selector"):
            validation_results["node_selector"] = self._validate_node_selector(
                self.pod_spec_config["node_selector"]
            )
            if not validation_results["node_selector"]["valid"]:
                validation_results["overall_valid"] = False
                validation_results["summary"]["components_with_errors"].append("node_selector")

            validation_results["summary"]["total_errors"] += len(validation_results["node_selector"]["errors"])
            validation_results["summary"]["total_warnings"] += len(validation_results["node_selector"]["warnings"])

            if validation_results["node_selector"]["warnings"]:
                validation_results["summary"]["components_with_warnings"].append("node_selector")

        # 校验affinity
        if self.pod_spec_config.get("affinity"):
            validation_results["affinity"] = self._validate_affinity(
                self.pod_spec_config["affinity"]
            )
            if not validation_results["affinity"]["valid"]:
                validation_results["overall_valid"] = False
                validation_results["summary"]["components_with_errors"].append("affinity")

            validation_results["summary"]["total_errors"] += len(validation_results["affinity"]["errors"])
            validation_results["summary"]["total_warnings"] += len(validation_results["affinity"]["warnings"])

            if validation_results["affinity"]["warnings"]:
                validation_results["summary"]["components_with_warnings"].append("affinity")

        # 校验tolerations
        if self.pod_spec_config.get("tolerations"):
            validation_results["tolerations"] = self._validate_tolerations(
                self.pod_spec_config["tolerations"]
            )
            if not validation_results["tolerations"]["valid"]:
                validation_results["overall_valid"] = False
                validation_results["summary"]["components_with_errors"].append("tolerations")

            validation_results["summary"]["total_errors"] += len(validation_results["tolerations"]["errors"])
            validation_results["summary"]["total_warnings"] += len(validation_results["tolerations"]["warnings"])

            if validation_results["tolerations"]["warnings"]:
                validation_results["summary"]["components_with_warnings"].append("tolerations")

        return validation_results

    def print_validation_results(self, validation_results: Dict[str, Any] = None):
        """打印校验结果"""
        if validation_results is None:
            validation_results = self.validate_configurations()

        print("=" * 60)
        print("Configuration Validation Results")
        print("=" * 60)

        # 总体状态
        status = "✓ PASSED" if validation_results["overall_valid"] else "✗ FAILED"
        print(f"\nOverall Status: {status}")

        summary = validation_results["summary"]
        print(f"Total Errors: {summary['total_errors']}")
        print(f"Total Warnings: {summary['total_warnings']}")

        if summary["components_with_errors"]:
            print(f"Components with Errors: {', '.join(summary['components_with_errors'])}")

        if summary["components_with_warnings"]:
            print(f"Components with Warnings: {', '.join(summary['components_with_warnings'])}")

        # 详细结果
        components = ["node_selector", "affinity", "tolerations"]
        for component in components:
            result = validation_results.get(component)
            if result is None:
                continue

            print(f"\n--- {component.upper()} ---")
            status = "✓ Valid" if result["valid"] else "✗ Invalid"
            print(f"Status: {status}")

            if result["errors"]:
                print("Errors:")
                for error in result["errors"]:
                    print(f"  - {error}")

            if result["warnings"]:
                print("Warnings:")
                for warning in result["warnings"]:
                    print(f"  - {warning}")

            if not result["errors"] and not result["warnings"]:
                print("No issues found")

    def print_config_summary(self):
        """打印配置摘要"""
        print("=" * 60)
        print("Pod Configuration Summary")
        print("=" * 60)

        print("\n1. Metadata Configuration:")
        print(f"   Name: {self.metadata_config['name']}")
        print(f"   Labels: {self.metadata_config['labels']}")
        print(f"   Annotations: {self.metadata_config['annotations']}")

        print(f"\n2. Pod Spec Configuration (excluding containers):")
        for key, value in self.pod_spec_config.items():
            if key not in ['containers', 'init_containers']:
                print(f"   {key}: {value}")

        print(f"\n3. Containers Configuration ({len(self.containers_config)} containers):")
        for i, container in enumerate(self.containers_config):
            print(f"   Container {i+1}: {container['name']}")
            print(f"     Image: {container['image']}")
            print(f"     Resources: {container.get('resources', {})}")
            print(f"     Env vars: {len(container.get('env', []))} variables")
            print(f"     Volume mounts: {len(container.get('volume_mounts', []))} mounts")

        print(f"\n4. Init Containers Configuration ({len(self.init_containers_config)} init containers):")
        for i, init_container in enumerate(self.init_containers_config):
            print(f"   Init Container {i+1}: {init_container['name']}")
            print(f"     Image: {init_container['image']}")
            print(f"     Resources: {init_container.get('resources', {})}")
            print(f"     Env vars: {len(init_container.get('env', []))} variables")


def main():
    """主函数 - 示例用法"""
    # 创建解析器实例
    with open("/home/<USER>/code/airflow_pipeline/tmp/c1.json") as f:
        pod_spec = f.read()
    parser = PodJsonParser(pod_spec)

    # 打印配置摘要
    parser.print_config_summary()

    # 执行配置校验
    print("\n" + "=" * 60)
    print("Configuration Validation")
    print("=" * 60)

    validation_results = parser.validate_configurations()
    parser.print_validation_results(validation_results)

    # 获取各个部分的配置（可以直接修改这些变量）
    print("\n" + "=" * 60)
    print("Configuration Variables (可以直接修改):")
    print("=" * 60)

    print("\n# Metadata配置变量:")
    print("metadata_config =", json.dumps(parser.metadata_config, indent=2, ensure_ascii=False))

    print("\n# Pod Spec配置变量:")
    print("pod_spec_config =", json.dumps(parser.pod_spec_config, indent=2, ensure_ascii=False))

    print("\n# Containers配置变量:")
    print("containers_config =", json.dumps(parser.containers_config, indent=2, ensure_ascii=False))

    print("\n# Init Containers配置变量:")
    print("init_containers_config =", json.dumps(parser.init_containers_config, indent=2, ensure_ascii=False))

    # 演示如何修改配置
    print("\n" + "=" * 60)
    print("修改配置示例:")
    print("=" * 60)

    # 修改metadata
    parser.metadata_config["labels"]["environment"] = "test"
    parser.metadata_config["annotations"]["modified"] = "true"

    # 修改第一个container的资源
    if parser.containers_config:
        parser.containers_config[0]["resources"]["requests"]["cpu"] = "500m"
        parser.containers_config[0]["resources"]["limits"]["memory"] = "2Gi"

    # 获取k8s对象
    try:
        k8s_pod = parser.get_k8s_pod()
        print("✓ 成功创建k8s Pod对象")
        print(f"  Pod名称: {k8s_pod.metadata.name}")
        print(f"  容器数量: {len(k8s_pod.spec.containers)}")
        print(f"  初始化容器数量: {len(k8s_pod.spec.init_containers) if k8s_pod.spec.init_containers else 0}")
    except Exception as e:
        print(f"✗ 创建k8s Pod对象失败: {e}")


if __name__ == "__main__":
    main()
