from kubernetes import client, config
from kubernetes.client.rest import ApiException
import requests
import logging
from utils.retry_wrapper import retry_decorator
import re
from airflow.models.variable import Variable

pod_operator_config = Variable.get('pod_operator_config', deserialize_json=True)

promql_dict = {
    'requests_cpu': 'sum(kube_pod_container_resource_requests{resource="cpu"}) by (node)',
    'requests_memory': 'sum(kube_pod_container_resource_requests{resource="memory"}) by (node)',
    'limits_cpu': 'sum(kube_pod_container_resource_limits{resource="cpu"}) by (node)',
    'limits_memory': 'sum(kube_pod_container_resource_limits{resource="memory"}) by (node)',
    'usage_cpu': 'sum(irate(node_cpu_seconds_total{mode!="idle"}[1m]) * 1000) by (instance)',
    'usage_memory': 'sum(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) by (instance)'
}

pod_promql_dict = {
    'pod_start_time': 'min_over_time(kube_pod_start_time{pod=~"{pod}"}[1h])',
    'pod_container_start_time': 'min_over_time(container_start_time_seconds{container=~"{container}",pod=~"{pod}"}[1h])',
    'pod_container_end_time': 'max_over_time(container_last_seen{container=~"{container}",pod=~"{pod}"}[1h])'
}

def check_metrics_server():
    config.load_kube_config()
    v1 = client.CoreV1Api()
    try:
        pods = v1.list_namespaced_pod(namespace="kube-system")
        metrics_server_installed = any("metrics-server" in pod.metadata.name for pod in pods.items)
        if not metrics_server_installed:
            print("Error: metrics-server未安装，请先安装metrics-server。")
            return False
        return True
    except ApiException as e:
        print(f"Exception when calling CoreV1Api->list_namespaced_pod: {e}")
        return False

def get_nodes():
    config.load_kube_config()
    v1 = client.CoreV1Api()
    try:
        return [
            node.metadata.name for node in v1.list_node().items if node.metadata.name != "vci-node1-cn-beijing-a"
            ]
    except ApiException as e:
        print(f"Exception when calling CoreV1Api->list_node: {e}")
        return []

def get_node_status(node):
    config.load_kube_config()
    v1 = client.CoreV1Api()
    node_status = v1.read_node(node)
    return node_status

def get_node_allocatable(node):
    return get_node_status(node).status.allocatable

def get_node_capacity(node):
    return get_node_status(node).status.capacity

def get_node_images(node):
    return get_node_status(node).status.images

@retry_decorator(delay=5, max_retries=3)
def fetch_prometheus(promql, prometheus_url=pod_operator_config.get("prometheus_url", 'http://10.30.36.193:32548')):
    response = requests.get(f'{prometheus_url}/api/v1/query', params={'query': promql})
    response.raise_for_status()
    return response.json()

def get_pod_start_time(pod):
    template = pod_promql_dict['pod_start_time']
    promql = template.format(pod=pod)
    res = fetch_prometheus(promql).get('data', {}).get('result', [])
    if len(res) == 0:
        return None
    else:
        return int(res[0]['value'][1])

def get_container_start_time(pod, container):
    template = pod_promql_dict['pod_container_start_time']
    promql = template.format(container=container, pod=pod)
    res = fetch_prometheus(promql).get('data', {}).get('result', [])
    if len(res) == 0:
        return None
    else:
        return int(res[0]['value'][1])

def get_container_end_time(pod, container):
    template = pod_promql_dict['pod_container_end_time']
    promql = template.format(container=container, pod=pod)
    res = fetch_prometheus(promql).get('data', {}).get('result', [])
    if len(res) == 0:
        return None
    else:
        return int(res[0]['value'][1])

def get_cpu_requests(status_this_time: dict):
    fetch_res = fetch_prometheus(promql_dict['requests_cpu'])
    if fetch_res['status'] == 'success':
        for item in fetch_res['data']['result']:
            if item['metric'].get("node", "empty_node") in status_this_time:
                status_this_time[item['metric']['node']]['cpu_requests'] = float(item['value'][1])*1000
        return True
    else:
        logging.warning(f"Error fetching prometheus data: {fetch_res}")
        return False

def get_cpu_limits(status_this_time: dict):
    fetch_res = fetch_prometheus(promql_dict['limits_cpu'])
    if fetch_res['status'] == 'success':
        for item in fetch_res['data']['result']:
            if item['metric'].get("node", "empty_node") in status_this_time:
                status_this_time[item['metric']['node']]['cpu_limits'] = float(item['value'][1])*1000
        return True
    else:
        logging.warning(f"Error fetching prometheus data: {fetch_res}")
        return False

def get_memory_requests(status_this_time: dict):
    fetch_res = fetch_prometheus(promql_dict['requests_memory'])
    if fetch_res['status'] == 'success':
        for item in fetch_res['data']['result']:
            if item['metric'].get("node", "empty_node") in status_this_time:
                status_this_time[item['metric']['node']]['memory_requests'] = float(item['value'][1])
        return True
    else:
        logging.warning(f"Error fetching prometheus data: {fetch_res}")
        return False

def get_memory_limits(status_this_time: dict):
    fetch_res = fetch_prometheus(promql_dict['limits_memory'])
    if fetch_res['status'] == 'success':
        for item in fetch_res['data']['result']:
            if item['metric'].get("node", "empty_node") in status_this_time:
                status_this_time[item['metric']['node']]['memory_limits'] = float(item['value'][1])
        return True
    else:
        logging.warning(f"Error fetching prometheus data: {fetch_res}")
        return False

def get_cpu_usage(status_this_time: dict):
    fetch_res = fetch_prometheus(promql_dict['usage_cpu'])
    if fetch_res['status'] == 'success':
        for item in fetch_res['data']['result']:
            node = item['metric']['instance'].split(':')[0]
            if node in status_this_time:
                status_this_time[node]['cpu_usage'] = float(item['value'][1])
        return True
    else:
        logging.warning(f"Error fetching prometheus data: {fetch_res}")
        return False

def get_memory_usage(status_this_time: dict):
    fetch_res = fetch_prometheus(promql_dict['usage_memory'])
    if fetch_res['status'] == 'success':
        for item in fetch_res['data']['result']:
            node = item['metric']['instance'].split(':')[0]
            if node in status_this_time:
                status_this_time[node]['memory_usage'] = float(item['value'][1])
        return True
    else:
        logging.warning(f"Error fetching prometheus data: {fetch_res}")

@retry_decorator(max_retries=5, delay=3)
def fetch_gpu_status():
    gpu_nodes = pod_operator_config.get("gpu_nodes", ["***********", "***********", "***********"])
    gpu_status = {k: {} for k in gpu_nodes}
    ip_pattern = r'(?:\d{1,3}\.){3}\d{1,3}'
    for gpu_node in gpu_nodes:
        response = requests.get(f"http://{gpu_node}:31993/metrics")
        if response.status_code == 200:
            for line in response.text.split("\n"):
                if "GPUDeviceCoreAllocated" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_core_used"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceMemoryAllocated" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_memory_used"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceCoreLimit" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_core_total"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceMemoryLimit" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_memory_total"] = int(float(line.split(" ")[-1]))
                if "GPUDeviceSharedNum" in line:
                    node = re.findall(ip_pattern, line)
                    if node:
                        gpu_status[node[0]]["gpu_task_num"] = int(float(line.split(" ")[-1]))
            break
        else:
            logging.warning(f"Error fetching gpu data: {response.status_code}")
    return gpu_status

def node_statistics():
    if check_metrics_server():
        nodes = get_nodes()
        status_this_time = {}
        gpu_status = fetch_gpu_status()
        for node in nodes:
            allocatable = get_node_allocatable(node)
            capacity = get_node_capacity(node)
            images = get_node_images(node)
            images_size = sum([image.size_bytes for image in images])
            status_this_time[node] = {
                'cpu_requests': 0,
                'cpu_limits': 0,
                'memory_requests': 0,
                'memory_limits': 0,
                'cpu_usage': 0,
                'memory_usage': 0,
                'gpu_core_used': gpu_status.get(node, {}).get("gpu_core_used", 0),
                'gpu_memory_used': gpu_status.get(node, {}).get("gpu_memory_used", 0),
                'gpu_core_total': gpu_status.get(node, {}).get("gpu_core_total", 0),
                'gpu_memory_total': gpu_status.get(node, {}).get("gpu_memory_total", 0),
                'gpu_task_num': gpu_status.get(node, {}).get("gpu_task_num", 0),
                'allocatable_cpu': int(allocatable['cpu'].replace('m', '')),
                'allocatable_memory': int(allocatable['memory'].replace('m', ''))//1000,
                'allocatable_pod': int(allocatable['pods']),
                'allocatable_ephemeral_storage': int(allocatable['ephemeral-storage']),
                'capacity_cpu': int(capacity['cpu'])*1000,
                'capacity_memory': int(capacity['memory'].replace('Ki', ''))*1024,
                'capacity_pod': int(capacity['pods']),
                'capacity_ephemeral_storage': int(capacity['ephemeral-storage'].replace('Ki', ''))*1024,
                'images_size': images_size
            }
        get_cpu_requests(status_this_time)
        get_cpu_limits(status_this_time)
        get_memory_requests(status_this_time)
        get_memory_limits(status_this_time)
        get_cpu_usage(status_this_time)
        get_memory_usage(status_this_time)
        return status_this_time
    else:
        logging.warning("Metrics server is not running")
        return {}

if __name__ == '__main__':
    nodes = get_nodes()
    print(nodes)
    print(node_statistics())
