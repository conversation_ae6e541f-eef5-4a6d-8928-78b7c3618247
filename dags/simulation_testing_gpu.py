from custom_operators.eq_job_operator import EqJobOperator
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException
from hooks.status_hook import StatusHook
import datetime, logging
from airflow.operators.python import BranchPythonOperator

DAG_ID = "SIMULATION_TESTING_GPU"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
ENV = Variable.get("ENV", "dev")

default_args = {
    'retries': 2
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG.get("tags", ["simulation_testing"]),
    max_active_runs=DAG_CONFIG.get("max_active_runs", 10),
    default_args=default_args
)
def simulation_testing():


    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(input)
        params = input.get("ori_params", {})
        job_json = params.get("data", {}).get("params", {}).get("job_json")
        if job_json:
            ti.xcom_push(key="job_json", value=job_json)
        else:
            raise AirflowException("job_json is empty")

    sim_job = EqJobOperator(
        task_id="sim_job",
        v1_job_json="{{ ti.xcom_pull(task_ids='init', key='job_json') }}",
        gpu=1,
        gpu_mem=4500,
        gpu_core=33,
        execution_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("job_timeout_min", 90)),
        do_xcom_push=True,
        retry_delay=datetime.timedelta(seconds=60),
        retries=1
    )

    def _retry_check(**kwargs):
        ti = kwargs["ti"]
        return_value = ti.xcom_pull(task_ids="sim_job", key="return_value")
        if not return_value:
            return "status_update"
        if isinstance(return_value, dict) and return_value.get("retry_status") == False:
            return "status_update"
        else:
            ti.xcom_push(key="retry", value=True)
            return "retry_sim_job"

    retry_check = BranchPythonOperator(
        task_id="retry_check",
        python_callable=_retry_check,
        provide_context=True,
        trigger_rule="all_done"
    )

    retry_sim_job = EqJobOperator(
        task_id="retry_sim_job",
        v1_job_json="{{ ti.xcom_pull(task_ids='init', key='job_json') }}",
        gpu=1,
        gpu_mem=4500,
        gpu_core=20,
        execution_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("job_timeout_min", 120)),
        do_xcom_push=True,
        increase_resource=DAG_CONFIG.get(
            "increase_resource",
            {
                "memory": 0,
                "cpu": 0,
                "gpu": 0,
                "gpumem": 4500,
                "gpucore": 20
            }),
        retry_delay=datetime.timedelta(seconds=60),
        retries=1
    )

    @task(task_id="status_update", trigger_rule="all_done")
    def _status_update(**kwargs):
        ti = kwargs["ti"]
        retry = ti.xcom_pull(key="retry")
        sim_task_id = "sim_job_retry" if retry else "sim_job"
        return_value = ti.xcom_pull(task_ids=sim_task_id, key="return_value")
        task_status = "true"
        if not isinstance(return_value, dict) or (isinstance(return_value, dict) and return_value.get("retry_status") != False):
            task_status = "false"
        input = kwargs["params"]
        logging.info(input)
        params = input.get("ori_params", {})
        workflow_name = params.get("data", {}).get("workflowDO", {}).get("workflowName")
        dev = "_dev" if ENV=="dev" else "_prod"
        cli = StatusHook(status_hook_conn_id=f"dms_status_hook{dev}")
        if cli.update_status(status=task_status, workflow_name=workflow_name):
            logging.info("status update success")
        else:
            raise AirflowException("status update failed")

    status_update = _status_update()

    init() >> sim_job >> retry_check >> status_update
    retry_check >> retry_sim_job >> status_update

simulation_testing()
