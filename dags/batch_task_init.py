from airflow.decorators import dag
from airflow.sensors.python import PythonSensor
from airflow.models.variable import Variable
from datetime import datetime, timedelta
from hooks.tos_hook import TosHook
import logging, json, time, re
from airflow.api.common.trigger_dag import trigger_dag
import os

DAG_ID = "batch_task_init"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
tos_cli = TosHook(DAG_CONFIG.get("tos_hook_id", "tos-gpu-data"))
default_args = {
    'retries': 2
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime(2025, 1, 1),
    catchup=False,
    tags=DAG_CONFIG.get("tags", ["simulation_testing"]),
    max_active_runs=1,
    default_args=default_args
)
def batch_task_init():

    def build_basic_info(input, sim_task_id):
        # sim_task_id == batch_id in sim test
        prefix = DAG_CONFIG.get("task_frontend_prefix")
        url = f"{prefix}?id={sim_task_id}"
        info = [
            {
                "key": "任务名称",
                "value": input.get("taskName", "未能获取到名称"),
            },
            {
                "key": "任务类型",
                "value": "仿真测试任务",
            },
            {
                "key": "任务页面",
                "value": f"[{url}]({url})",
            }
        ]
        return info

    def trigger_manager_dag(input, monitored_dag_id, tmp_file_name):
        monitor_dag_id = DAG_CONFIG.get("monitor_dag_id", "dag_run_monitor")
        batch_id = input.get("batchId")
        notify_list = build_basic_info(input, batch_id)
        with open(tmp_file_name, "r") as f:
            for line in f.readlines():
                line = json.loads(line)
                create_user_email = line.get("create_user_email", "")
                create_user_mobile = line.get("create_user_mobile", "")
                break
        conf = {
            "create_user_email": create_user_email,
            "create_user_mobile": create_user_mobile,
            "notify_list": notify_list,
            "title": "仿真测试任务通知",
            "batch_id": batch_id,
            "monitor_dag_id": monitored_dag_id
        }
        try:
            trigger_dag(dag_id=monitor_dag_id,
                        run_id=f"{monitor_dag_id}.{batch_id}",
                        conf=conf,
                        replace_microseconds=False)
        except Exception as e:
            logging.error(f"trigger_dag error: {e}")

    def trigger_batch_dag(input, monitored_dag_id, tmp_file_name):
        batch_id = input.get("batchId")
        with open(tmp_file_name, "r") as f:
            for line in f.readlines():
                line = json.loads(line)
                run_id = f"{monitored_dag_id}.{batch_id}.{str(int(time.time()*1000))}"
                try:
                    trigger_dag(dag_id=monitored_dag_id,
                                run_id=run_id,
                                conf=line,
                                replace_microseconds=False)
                except Exception as e:
                    logging.error(f"trigger_dag error: {e}")

    def creat_batch_tasks(**kwargs):
        input = kwargs["params"]
        tos_bucket = input.get("tosBucket")
        tos_key_list = input.get("tosKeyList")
        use_gpu = input.get("useGpu")
        monitored_dag_id = "SIMULATION_TESTING"
        if use_gpu:
            monitored_dag_id = "SIMULATION_TESTING_GPU"
        for tos_key in tos_key_list:
            if not tos_cli.head_object(key=tos_key, bucket_name=tos_bucket):
                return False
        time.sleep(10)
        for i, tos_key in enumerate(tos_key_list):
            tmp_file_name = tos_cli.download_file(key=tos_key, bucket_name=tos_bucket)
            if i == 0:
                trigger_manager_dag(input, monitored_dag_id, tmp_file_name)
            trigger_batch_dag(input, monitored_dag_id, tmp_file_name)
            os.remove(tmp_file_name)
        return True

    monitor = PythonSensor(
        task_id="monitor",
        python_callable=creat_batch_tasks,
        poke_interval=timedelta(minutes=2),
        mode="reschedule",
        timeout=timedelta(minutes=10),
    )

    monitor

m = batch_task_init()
