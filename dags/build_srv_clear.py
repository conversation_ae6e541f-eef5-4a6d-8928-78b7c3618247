from airflow.decorators import dag, task
from airflow.sensors.python import PythonSensor
from airflow.models import DagRun
from airflow.utils.state import State
from airflow import settings
from custom_operators.ssh_xcom_operator import SSHXComOperator
from airflow.models.variable import Variable
from hooks.feishu_hook import <PERSON><PERSON>uHook
import datetime


DAG_ID = "build_srv_clear"
DAG_CONFIG = Variable.get("build_srv_clear", deserialize_json=True)


@dag(
    dag_id=DAG_ID,
    schedule_interval=DAG_CONFIG.get("schedule_interval", "@hourly"),
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG.get("tags", ["build_srv_clear"]),
    max_active_runs=DAG_CONFIG.get("max_active_runs", 1),
)
def build_srv_clear():

    @task(task_id="init")
    def init(**kwargs):
        command = "bash /root/daily.sh; rm -rf /tmp/data_closed_loop*; rm -rf /tmp/tmp.*; rm -rf /tmp/image_build_dcl/tmp.*"
        ti = kwargs["ti"]
        ti.xcom_push(key="command", value=command)

    def check_running():
        session = settings.Session()
        running_dag_runs_1 = session.query(DagRun).filter(
            DagRun.dag_id == 'BEEOS_IMAGE_BUILD',
            DagRun.state == State.RUNNING
        ).all()
        running_dag_runs_2 = session.query(DagRun).filter(
            DagRun.dag_id == 'BEEOS_IMAGE_BUILD_dev',
            DagRun.state == State.RUNNING
        ).all()
        session.close()
        return not running_dag_runs_1 and not running_dag_runs_2
    
    can_run = PythonSensor(
        task_id="can_run",
        python_callable=check_running,
        timeout=7200,
        poke_interval=600,
        mode="reschedule",
    )

    tasks = []
    for srv in DAG_CONFIG.get("srv", ["build_image_srv_dev", "build_image_srv_prod"]):
        clear = SSHXComOperator(
            task_id=f"clear_{srv}",
            ssh_conn_id=srv,
            command="{{ ti.xcom_pull(task_ids='init', key='command') }}",
            do_xcom_push=True
        )
        tasks.append(clear)

    @task(task_id="notify_failed_reason", trigger_rule="one_failed")
    def notify_failed_reason():
        message = "您的镜像编译机器清理失败"
        FeishuHook().send_group_msg(msg=message)

    init() >> can_run >> tasks >> notify_failed_reason()

build_srv_clear()
