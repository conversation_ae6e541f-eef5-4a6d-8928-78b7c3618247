from airflow.decorators import dag
from custom_operators.ssh_xcom_operator import SSHXComOperator
from airflow.models.variable import Variable
from airflow.operators.python import BranchPythonOperator
from custom_operators.pod_operator import EqPodOperator
import datetime
import logging


DAG_ID = "dag_sync"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)


@dag(
    dag_id=DAG_ID,
    schedule_interval=DAG_CONFIG.get("schedule_interval", "@hourly"),
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG.get("tags", ["dag_sync"]),
    max_active_runs=DAG_CONFIG.get("max_active_runs", 1),
    params={
        "env": "dev"
    }
)
def dag_sync():

    def branch(**kwargs):
        input = kwargs["params"]
        ti = kwargs["ti"]
        logging.info(input)
        resource = {
            "resource_requests": {
                "cpu": "100m",
                "memory": '100Mi',
            },
            "resource_limits": {
                "cpu": "1000m",
                "memory": '1000Mi',
            }
        }
        ti.xcom_push(key="resource", value=resource)
        return input.get("env", "dev")

    branch_op = BranchPythonOperator(
        task_id="branch",
        python_callable=branch
    )

    dev = SSHXComOperator(
        task_id="dev",
        ssh_conn_id=DAG_CONFIG.get("srv", "build_image_srv_dev"),
        command=f"""cd /home/<USER>/airflow_pipeline && git stash && git stash clear \
            && git checkout {DAG_CONFIG.get('dev_branch', 'pipeline-only')} && git pull""",
        do_xcom_push=True,
        retries=2
    )
    dev.template_ext = ()

    prod = SSHXComOperator(
        task_id="prod",
        ssh_conn_id=DAG_CONFIG.get("srv", "build_image_srv_dev"),
        command="""cd /home/<USER>/prod && git stash && git stash clear \
            && git checkout pipeline-only && git pull && bash pcode.sh""",
        do_xcom_push=True,
        retries=2
    )
    prod.template_ext = ()

    copy_to_tos_prod = EqPodOperator(
        task_id="copy_to_tos_prod",
        name="copy-to-tos-prod",
        namespace="airflow",
        image=DAG_CONFIG.get("image", "registry.eqfleetcmder.com/eq-sim/airflow:larger"),
        image_pull_policy="always",
        command=DAG_CONFIG.get("cmd","python3 /root/update_k8s_code.py"),
        do_xcom_push=True,
        retries=2,
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        pvc_with_path={"airflow-pvc":"/opt/airflow"}
    )

    # copy_to_tos_dev = EqPodOperator(
    #     task_id="copy_to_tos_dev",
    #     name="copy_to_tos_dev",
    #     namespace="airflow",
    #     image=DAG_CONFIG.get("dev_image", "registry.eqfleetcmder.com/eq-sim/airflow:dev"),
    #     image_pull_policy="always",
    #     command=DAG_CONFIG.get("dev_cmd","python3 /root/update_k8s_code.py"),
    #     do_xcom_push=True,
    #     retries=2,
    #     pvc_with_path={"airflow-pvc-test":"/opt/airflow"}
    # )

    branch_op >> [dev, prod]
    prod >> copy_to_tos_prod
    # dev >> copy_to_tos_dev

dag_sync()
