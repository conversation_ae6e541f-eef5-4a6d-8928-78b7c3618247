# -*- coding: utf-8 -*-
from custom_operators.pod_operator import <PERSON><PERSON><PERSON><PERSON><PERSON>perator
from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import <PERSON>Hook
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException

import json
import datetime
import logging

DAG_ID = "PROCESS_NUSCENES"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = Variable.get("ENV", "dev")
TEST = ENV in ("test", "dev")

default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG["tags"],
    default_args=default_args
)
def nuscene_data_process():
    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(input)
        logging.info(type(input))

        airflow_params = input.get("airflowParams")
        airflow_params = json.loads(airflow_params)
        logging.info(airflow_params)
        logging.info(type(airflow_params))

        data_info_key = airflow_params.get("data_info_key")
        vehicle = airflow_params.get("vehicleVin", "unknown")
        if vehicle == "":
            vehicle = "unknown"
        bag_file = airflow_params.get("input_data")
        output_path = airflow_params.get("output_path")
        template_config = input.get("taskRunningConfig").get("configUrl")
        task_info_id = airflow_params.get("task_info_id")
        task_detail_id = input.get("id")

        if DAG_CONFIG.get("resource"):
            resource = DAG_CONFIG["resource"]
        else:
            resource = {
                "resource_requests": {
                    "cpu": input.get("taskResource").get("cpuRequestSize"),
                    "memory": f'{input.get("taskResource").get("memoryRequestSize")}Mi'
                },
                "resource_limits": {
                    "cpu": input.get("taskResource").get("cpuLimitSize"),
                    "memory": f'{input.get("taskResource").get("memoryLimitSize")}Mi'
                }
            }

        if DAG_CONFIG.get("image"):
            image = DAG_CONFIG["image"]
        else:
            image = input.get("taskImage").get("imageName") + ":" + input.get("taskImage").get("imageVersion")

        ti.xcom_push(key="image", value=image)
        ti.xcom_push(key="workflow_name", value=input.get("taskName"))
        ti.xcom_push(key="pod_name_prefix", value=input.get("taskName").lower().replace("_", "-"))
        ti.xcom_push(key="image_pull_policy", value=input.get("taskImage").get("pullPolicy"))
        ti.xcom_push(key="resource", value=resource)
        ti.xcom_push(key='vehicle', value=vehicle)
        ti.xcom_push(key='template_config', value=template_config) # data/task/config/36c792dc3904409f82b8dae5e4fbf0d7/config.json
        ti.xcom_push(key='running_config', value=data_info_key) # data/task/2025/05/23/workflow-transfer-nuscenes-dev-0b44bc/config.json
        ti.xcom_push(key='bag_file', value=bag_file)  # data/vizbag/2025/03/13/753c1d/Lat_20250313_193051.bag
        ti.xcom_push(key='output_path', value=output_path) # data/nuscenes/2025/03/13/753c1d/
        ti.xcom_push(key='task_info_id', value=task_info_id)
        ti.xcom_push(key='task_detail_id', value=task_detail_id)

        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)

        update_task_status = status_cli.update_task_status(
            taskId=task_info_id,
            taskDetailId=task_detail_id,
            status="Running",
            info="",
            errMsg=""
        )
        if update_task_status:
            logging.info(f"{status_hook_id} post request success")
        else:
            logging.error(f"{status_hook_id} post request failed")


    sensor_parse_task = EqPodOperator(
        task_id="nuscenes_sensor_parse_pod",
        name="{{ ti.xcom_pull(key='pod_name_prefix') }}-prepare",
        namespace=DAG_CONFIG.get("namespace"),
        image=DAG_CONFIG.get("sensor_parse_image"),
        image_pull_policy=DAG_CONFIG.get("image_pull_policy"),
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        command="python3 analyze_tf_static.py" \
                " -i /{{ ti.xcom_pull(key='bag_file') }}" \
                " -o /{{ ti.xcom_pull(key='output_path') }}" \
                " -v {{ ti.xcom_pull(key='vehicle') }}" \
                " -tc /{{ ti.xcom_pull(key='template_config') }}" \
                " -rc /{{ ti.xcom_pull(key='running_config') }};",
        do_xcom_push=True,
        mount_argo_pps_config=True,
        test_run=TEST,
        # on_finish_action='keep_pod',
        # pvc_with_path={
        #     "data": "/data"
        # },
        pvc_with_path={
            "data" if TEST else "closedloop-pvc-rw": "/data"
        },
        retries=0 if TEST else 2,
        retry_delay=datetime.timedelta(seconds=300),
        execution_timeout=datetime.timedelta(hours=2)
     )

    transfor_nuscene_task = EqPodOperator(
        task_id="transfor_nuscenes_pod",
        name="{{ ti.xcom_pull(key='pod_name_prefix') }}",
        namespace=DAG_CONFIG.get("namespace"),
        image="{{ ti.xcom_pull(key='image') }}",
        image_pull_policy="{{ ti.xcom_pull(key='image_pull_policy') }}",
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        # command= "/run.sh --cp=/{{ ti.xcom_pull(key='running_config') }};" + \
        #         r' status=$?; echo "{\"status\": $status}" > /airflow/xcom/return.json; exit $status',
        command= "/run.sh --cp=/{{ ti.xcom_pull(key='running_config') }}",
        do_xcom_push=True,
        mount_argo_pps_config=True,
        test_run=TEST,
        # on_finish_action='keep_pod',
        # pvc_with_path={
        #     "data": "/data"
        # },
        pvc_with_path={
            "data" if TEST else "closedloop-pvc-rw": "/data"
        },
        retries=0 if TEST else 1,
        retry_delay=datetime.timedelta(seconds=300),
        execution_timeout=datetime.timedelta(hours=4)
     )

    @task(trigger_rule="all_done")
    def update_status(**kwargs):
        ti = kwargs["ti"]
        workflow_name = ti.xcom_pull(key="workflow_name")

        # status = ti.xcom_pull(task_ids="transfor_nuscenes_pod")
        pod_result = ti.xcom_pull(task_ids="transfor_nuscenes_pod", key="pod_result")
        if pod_result is None:
            run_status = "1"
        else:
            run_status = pod_result.get("status")

        logging.info(f"status : {run_status}")

        if run_status is not None and run_status != "0000":
            err_msg = DAG_CONFIG.get("err_code", {}).get(run_status, "未知错误")
            err_msg = f"[{run_status}] {err_msg}"
            status = "Failed"
        else:
            status = "Succeeded"
            err_msg = ""

        logging.info(f"{status}, {workflow_name}")

        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)

        update_task_status = status_cli.update_task_status(
            taskId=ti.xcom_pull(key='task_info_id'),
            taskDetailId=ti.xcom_pull(key='task_detail_id'),
            status=status,
            info="",
            errMsg=err_msg
        )

        if update_task_status:
            logging.info(f"{status_hook_id} post request success")
            ti.xcom_push(key="update_result", value="success")
        else:
            ti.xcom_push(key="update_result", value="failed")
            raise AirflowException("update status failed")


    def build_basic_info(input, success, failed_reason, bag_file):
        failed_reason_msg = ""
        if "update_status_failed" in failed_reason:
            failed_reason_msg += "我们已经进行了多次重试，但向后端更新状态失败。\n"
        if "pod_failed" in failed_reason:
            failed_reason_msg += "我们已经进行了多次重试，但pod执行依然失败。"
        return [
            {
                "key": "任务名称",
                "value": input.get("ori_params", {}).get("taskDO", {}).get("taskName"),
            },
            {
                "key": "任务ID",
                "value": input.get("ori_params", {}).get("taskDO", {}).get("id"),
            },
            {
                "key": "Bag文件",
                "value": bag_file,
            },
            {
                "key": "任务类型",
                "value": "Bag数据转Nuscenes任务",
            },
            {
                "key": "任务状态",
                "value": "成功" if success else "失败",
            },
            {
                "key": "失败原因",
                "value": "Nuscenes数据生成失败"
            }
        ]

    @task(task_id="failed_notify", trigger_rule="one_failed", retries=0)
    def _failed_notify(**kwargs):
        ti = kwargs["ti"]
        status = ti.xcom_pull(task_ids="transfor_nuscenes_pod")
        update_result = ti.xcom_pull(task_ids="update_status")
        bag_file = ti.xcom_pull(key='bag_file')
        failed_reason = []
        if not (isinstance(status, dict) and status.get("status")==0):
            failed_reason.append("pod_failed")
        if update_result == "failed":
            failed_reason.append("update_status_failed")
        input = kwargs["params"]
        logging.info(f"email: {input.get('email')}, phone: {input.get('phone')}")
        logging.info(input)
        # feishu_cli = FeishuHook()
        # basic_info = build_basic_info(input, False, failed_reason, bag_file)
        # feishu_cli.send_msg_card(
        #     title="Nuscenes数据生成通知",
        #     notify_list=basic_info,
        #     user_email=input.get("email"),
        #     user_mobile=input.get("phone")
        # )
        # 强制使 DAG 失败
        raise AirflowException("任务失败，已发送通知")


    failed_notify = _failed_notify()

    init() >> sensor_parse_task >> transfor_nuscene_task >>  update_status() >> failed_notify
    transfor_nuscene_task >> failed_notify

nuscene_data_process()
