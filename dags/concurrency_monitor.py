import datetime
from airflow.models.dag import DAG
from airflow.decorators import task
from airflow.models.variable import Variable
import logging
import subprocess
from airflow.utils.session import NEW_SESSION, provide_session
from sqlalchemy.orm import Session
from airflow.models import DagRun
from airflow.utils.state import State

DAG_ID = "concurrency_monitor"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
AUTO_ADJUST_DAGS = DAG_CONFIG.get(
    "auto_adjust_dags", {
        "SIMULATION_TESTING_GPU": 0,
        "LOG_SIM_GPU": 0,
        "LOG_SIM": 0,
        "SIMULATION_TESTING": 0,
        "TRANSFER_NUSCENES": 1,
        "LOCK_VIZ_PROCESS": 1,
        "LOCK_DATA_PROCESS": 1
    })


with DAG(
    dag_id=DAG_ID,
    schedule="*/5 * * * *",
    start_date=datetime.datetime(2022, 1, 1),
    dagrun_timeout=datetime.timedelta(minutes=60),
    tags=["cluster"],
    max_active_runs=DAG_CONFIG.get("max_active_runs", 1),
    catchup=False
) as dag:

    @provide_session
    def get_dag_runs(dag_id: str, session: Session = NEW_SESSION):
        cnt = session.query(DagRun).filter(
            DagRun.dag_id == dag_id,
            DagRun.state.in_([State.RUNNING, State.QUEUED])
        ).count()
        logging.info(f"DAG {dag_id} has {cnt} runs")
        return cnt

    def add_taint():
        for node in DAG_CONFIG.get("allow_schedule_gpu_nodes", ["***********", "***********"]):
            cmd = f"kubectl taint nodes {node} node=gpu:NoSchedule"
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            out, err = process.communicate()
            logging.info(f"{cmd}\n {out}\n {err}")

    def remove_taint():
        for node in DAG_CONFIG.get("allow_schedule_gpu_nodes", ["***********", "***********"]):
            cmd = f"kubectl taint nodes {node} node=gpu:NoSchedule-"
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            out, err = process.communicate()
            logging.info(f"{cmd}\n {out}\n {err}")

    def decrease_max_active_runs(dag_id: str):
        dag_config = Variable.get(dag_id, deserialize_json=True)
        dag_config["max_active_runs"] = dag_config.get("max_active_run_lower_limit", 4)
        Variable.set(dag_id, dag_config, serialize_json=True)
        logging.info(f"{dag_id} max_active_runs: down to {dag_config.get('max_active_run_lower_limit', 4)}")

    def check_prometheus():
        cmd = """if [[ $(kubectl get pod -n kube-system prometheus-0 -o json | jq -r '.status.phase') != 'Running' ]]; then
kubectl delete pod -n kube-system prometheus-0
echo "Restarting prometheus"
fi
"""
        process = subprocess.Popen(cmd, shell=True, executable='/bin/bash', stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        out, err = process.communicate()
        logging.info(f"{cmd}\n {out}\n {err}")

    @task(task_id="adjust")
    def adjust(**kwargs):
        total_pending_gpu_tasks = 0
        for gpu_task in DAG_CONFIG.get("gpu_tasks", ["SIMULATION_TESTING_GPU", "LOG_SIM_GPU"]):
            total_pending_gpu_tasks += get_dag_runs(gpu_task)
        logging.info(f"total_pending_gpu_tasks: {total_pending_gpu_tasks}")
        if total_pending_gpu_tasks > DAG_CONFIG.get("max_pending_runs", 10):
            add_taint()
            logging.info(f"{total_pending_gpu_tasks} pending GPU tasks, tainting nodes")
        else:
            remove_taint()
            logging.info(f"{total_pending_gpu_tasks} pending GPU tasks, removing taint")
        sorted_tasks = sorted(AUTO_ADJUST_DAGS.items(), key=lambda x: x[1])
        for i in range(len(sorted_tasks)):
            task_type, _ = sorted_tasks[i]
            pending_count = get_dag_runs(task_type)
            if pending_count > DAG_CONFIG.get("max_pending_runs", 10):
                for lower_priority_task in sorted_tasks[i+1:]:
                    lower_task_type, _ = lower_priority_task
                    decrease_max_active_runs(lower_task_type)
        try:
            check_prometheus()
        except Exception as e:
            print(e)

    adjust()

