import datetime
from airflow.models.dag import DAG
from airflow.decorators import task
import logging
import subprocess
from airflow.models.param import Param

DAG_ID = "create_deployment"


with DAG(
    dag_id=DAG_ID,
    schedule=None,
    start_date=datetime.datetime(2022, 1, 1),
    dagrun_timeout=datetime.timedelta(minutes=60),
    tags=["cluster"],
    max_active_runs=1,
    catchup=False,
    params={
        "deployment_yaml": Param("sil_track.yaml", description="yaml file to apply"),
    }
) as dag:


    def apply_yaml(deployment_yaml):
        cmd = f"""
kubectl apply -f /opt/airflow/plugins/yaml/{deployment_yaml}
"""
        process = subprocess.Popen(cmd, shell=True, executable='/bin/bash', stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        out, err = process.communicate()
        logging.info(f"{cmd}\n {out}\n {err}")

    @task(task_id="adjust")
    def apply(**kwargs):
        input = kwargs["params"]
        apply_yaml(input["deployment_yaml"])

    apply()
