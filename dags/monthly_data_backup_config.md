# Monthly Data Backup DAG 配置说明

## 概述

`monthly_data_backup.py` 是一个每月执行的Airflow DAG，用于将PostgreSQL数据库中的Airflow元数据备份到S3存储中。

## 功能特性

### 📅 调度配置
- **执行频率**: 每月1日凌晨2点执行
- **Cron表达式**: `0 2 1 * *`
- **时区**: 使用Airflow默认时区
- **Catchup**: 禁用（避免历史数据重复备份）

### 📊 备份数据表

#### 按月备份的表（上个月数据）
1. **dag_run** - DAG运行记录
2. **job** - 作业执行记录  
3. **log** - 系统日志记录
4. **task_instance** - 任务实例记录
5. **xcom** - 任务间通信数据

#### 全量备份的表（带版本）
1. **variable** - Airflow变量配置
2. **connection** - 连接配置（不包含密码）

### 🗂️ S3存储结构

```
s3://bucket/prefix/
├── dag_run/
│   └── year=2025/month=01/dag_run_20250201_020000.parquet
├── job/
│   └── year=2025/month=01/job_20250201_020000.parquet
├── log/
│   └── year=2025/month=01/log_20250201_020000.parquet
├── task_instance/
│   └── year=2025/month=01/task_instance_20250201_020000.parquet
├── xcom/
│   └── year=2025/month=01/xcom_20250201_020000.parquet
├── variable/
│   └── version=20250201_020000/variable_20250201_020000.parquet
├── connection/
│   └── version=20250201_020000/connection_20250201_020000.parquet
└── backup_summary/
    └── year=2025/month=01/summary_20250201_020000.json
```

## 🔧 配置方法

### 1. Airflow Variable配置

在Airflow Web UI中创建Variable：`monthly_data_backup`

```json
{
    "postgres_conn_id": "airflow_db",
    "s3_conn_id": "aws_default", 
    "s3_bucket": "your-backup-bucket",
    "s3_prefix": "airflow-data-backup",
    "tags": ["backup", "monthly"]
}
```

### 2. 连接配置

#### PostgreSQL连接 (`airflow_db`)
- **Connection Type**: Postgres
- **Host**: PostgreSQL服务器地址
- **Schema**: airflow数据库名
- **Login**: 数据库用户名
- **Password**: 数据库密码
- **Port**: 5432

#### AWS S3连接 (`aws_default`)
- **Connection Type**: Amazon Web Services
- **Extra**: 
```json
{
    "aws_access_key_id": "your_access_key",
    "aws_secret_access_key": "your_secret_key",
    "region_name": "us-east-1"
}
```

### 3. S3权限配置

确保AWS凭证具有以下权限：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl",
                "s3:GetObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::your-backup-bucket",
                "arn:aws:s3:::your-backup-bucket/*"
            ]
        }
    ]
}
```

## 📈 Trino查询示例

### 创建外部表

```sql
-- 创建dag_run表
CREATE TABLE airflow_backup.dag_run (
    dag_id VARCHAR,
    execution_date TIMESTAMP,
    state VARCHAR,
    run_id VARCHAR,
    external_trigger BOOLEAN,
    conf VARCHAR,
    end_date TIMESTAMP,
    start_date TIMESTAMP,
    run_type VARCHAR,
    last_scheduling_decision TIMESTAMP,
    dag_hash VARCHAR,
    creating_job_id BIGINT,
    queued_at TIMESTAMP,
    data_interval_start TIMESTAMP,
    data_interval_end TIMESTAMP,
    updated_at TIMESTAMP,
    year INTEGER,
    month INTEGER
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/dag_run/',
    format = 'PARQUET',
    partitioned_by = ARRAY['year', 'month']
);

-- 创建variable表（带版本）
CREATE TABLE airflow_backup.variable (
    key VARCHAR,
    val VARCHAR,
    description VARCHAR,
    is_encrypted BOOLEAN,
    backup_version VARCHAR,
    backup_date VARCHAR,
    version VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/variable/',
    format = 'PARQUET',
    partitioned_by = ARRAY['version']
);
```

### 查询示例

```sql
-- 查询上个月的DAG运行统计
SELECT 
    dag_id,
    state,
    COUNT(*) as run_count,
    AVG(EXTRACT(EPOCH FROM (end_date - start_date))) as avg_duration_seconds
FROM airflow_backup.dag_run 
WHERE year = 2025 AND month = 1
GROUP BY dag_id, state
ORDER BY run_count DESC;

-- 查询最新版本的变量配置
SELECT * FROM airflow_backup.variable 
WHERE version = (SELECT MAX(version) FROM airflow_backup.variable);

-- 查询任务失败统计
SELECT 
    dag_id,
    task_id,
    COUNT(*) as failure_count
FROM airflow_backup.task_instance 
WHERE state = 'failed' 
    AND year = 2025 AND month = 1
GROUP BY dag_id, task_id
ORDER BY failure_count DESC;
```

## 🚨 监控和告警

### 1. DAG执行监控
- 检查DAG是否按时执行
- 监控任务执行时间
- 检查备份数据量是否正常

### 2. 数据质量检查
- 验证备份文件是否生成
- 检查Parquet文件完整性
- 对比备份记录数与数据库记录数

### 3. 告警配置
```python
# 在DAG中添加告警
default_args = {
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
}
```

## 🔍 故障排查

### 常见问题

1. **连接失败**
   - 检查PostgreSQL和S3连接配置
   - 验证网络连通性和权限

2. **内存不足**
   - 调整Airflow Worker内存配置
   - 考虑分批处理大表数据

3. **S3上传失败**
   - 检查S3权限配置
   - 验证存储桶是否存在

4. **Parquet格式错误**
   - 检查pandas和pyarrow版本兼容性
   - 验证数据类型转换

### 日志查看
```bash
# 查看DAG执行日志
airflow logs monthly_data_backup backup_dag_run_table 2025-02-01
```

## 📝 维护建议

1. **定期检查备份完整性**
2. **监控S3存储成本**
3. **定期清理旧备份数据**
4. **更新数据库表结构变更**
5. **测试数据恢复流程**
