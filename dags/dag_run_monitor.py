from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import <PERSON><PERSON><PERSON>
from hooks.airflow_hook import AirflowHook
from airflow.decorators import dag
from airflow.sensors.python import PythonSensor
from airflow.models.variable import Variable
from hooks.redis_hook import <PERSON>QRedis<PERSON>ock<PERSON>ook, EQRedisHook
from airflow.models import DagRun
from airflow.utils.state import State, DagRunState
import logging, time, pendulum
from datetime import datetime, timedelta
import copy
from airflow.api.common.mark_tasks import (
    set_dag_run_state_to_failed,
)
from airflow.utils.cli import get_dag
from airflow.utils.session import NEW_SESSION, provide_session
from sqlalchemy.orm import Session
from sqlalchemy import select, func

DAG_ID = "dag_run_monitor"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
ENV = Variable.get("ENV", "dev")
TEST = ENV in ("test", "dev")

default_args = {
    'retries': 2
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime(2025, 1, 1),
    catchup=False,
    tags=DAG_CONFIG.get("tags", ["simulation_testing"]),
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    default_args=default_args
)
def dag_run_monitor():

    def monitor_msg(input, tmp_notify_list, title):
        feishu_cli = FeishuHook()
        feishu_cli.send_msg_card(
            user_email=input.get("create_user_email"),
            user_mobile=input.get("create_user_mobile"),
            title=title,
            notify_list=tmp_notify_list,
        )

    @provide_session
    def get_time_estimation(dag_id:str, batch_id:str, concurrency:int, session: Session = NEW_SESSION):
        dag_run_id_prefix = f"{dag_id}.{batch_id}"
        logging.info(f"dag_runs_time_estimation: dag_id: {dag_id}, dag_run_id_prefix: {dag_run_id_prefix}")
        average_duration: timedelta | None = session.scalar(
            select(func.avg(DagRun.end_date - DagRun.start_date).label('average_duration')).where(
                DagRun.dag_id == dag_id, DagRun.state == DagRunState.SUCCESS
                )
        )
        logging.info(f"average_duration: {average_duration}")
        execution_dates: datetime | None = session.scalars(
            select(DagRun.execution_date).where(
                DagRun.dag_id == dag_id,
                DagRun.run_id.like(f"{dag_run_id_prefix}%"),
                DagRun.state == DagRunState.QUEUED
                ).order_by(DagRun.execution_date.asc())
        ).all()
        if len(execution_dates) > 0:
            first_queued_execution_date = execution_dates[0]
            queued_dag_runs_size = len(execution_dates)
            logging.info(f"first_queued_execution_date: {first_queued_execution_date}, queued_dag_runs_size: {queued_dag_runs_size}")
            ahead_dag_runs_count = session.scalar(
                select(func.count(DagRun.id)).where(
                    DagRun.dag_id == dag_id,
                    DagRun.state == DagRunState.QUEUED,
                    DagRun.execution_date < first_queued_execution_date
                    )
            )
            logging.info(f"ahead_dag_runs_count: {ahead_dag_runs_count}")
            before_finish_dag_runs_count = queued_dag_runs_size + ahead_dag_runs_count
            now = pendulum.now()
            start_time = now + timedelta(
                seconds=average_duration.total_seconds() * ahead_dag_runs_count // concurrency)
            end_time = now + timedelta(
                seconds=average_duration.total_seconds() * before_finish_dag_runs_count // concurrency)
            st = start_time
            et = end_time
        else:
            now = pendulum.now()
            st = now
            et = st
        return {"start_time":st, "end_time":et, "cost":average_duration}

    def update_batch_task_state(batch_id, state):
        backend_cli = StatusHook(f"dms_status_hook_{ENV}")
        res = backend_cli.service_post_request(
            url=DAG_CONFIG.get("manager_uri","/data/managerBatchTaskById"),
            body={"batchTaskId":batch_id,"operator":state}
        )
        if res and res.get("code") == "200":
            return True
        else:
            logging.info("update batch task error")
            return False

    def cut_in_line(dag_id, batch_id):
        airflow_cli = AirflowHook()
        dag_run_id_prefix = f"{dag_id}.{batch_id}"
        url = f"api/v1/dags/{dag_id}/dagRuns/{dag_run_id_prefix}/jumpTheQueue"
        try:
            airflow_cli.post_request(
                url=url,
                data={"next_execution_date":None}
            )
            return True
        except Exception as e:
            logging.error("failed to cut in line")
            return False
        finally:
            update_batch_task_state(batch_id=batch_id, state="hang")

    def format_date(date):
        return date.in_timezone("Asia/Shanghai").isoformat().replace("T", " ")[:16]

    def update_time_estimation(batch_id, stet):
        st = stet.get("start_time")
        et = stet.get("end_time")
        cost = stet.get("cost")
        if st == et:
            et += timedelta(seconds=cost.total_seconds())
        backend_cli = StatusHook(f"dms_status_hook_{ENV}")
        res = backend_cli.service_post_request(
            url=DAG_CONFIG.get("manager_uri","/data/managerBatchTaskById"),
            body={
                "batchTaskId":batch_id,
                "operator":"updateEstimateTime",
                "estimateStartTime": f"{format_date(st)}:00",
                "estimateEndTime": f"{format_date(et)}:00"
            }
        )
        if res and res.get("code") == "200":
            return True
        else:
            logging.info("update batch task error")
            return False

    def send_time_estimation(input, be_cut_in_line=False, hourly_run=False):
        logging.info(input)
        monitor_dag_id = input.get("monitor_dag_id")
        batch_id = input.get("batch_id")
        concurrency = Variable.get(monitor_dag_id, deserialize_json=True).get("max_active_runs")
        stet = get_time_estimation(monitor_dag_id, batch_id, concurrency)
        update_time_estimation(batch_id, stet)
        st = format_date(stet.get("start_time"))
        et = format_date(stet.get("end_time"))
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        note = "该预测时间仅供参考，任务插队、资源调整都会导致运行时间误差和改变，如果您收到了这条通知，可能有人插队或取消了他的任务"
        msg = "您的任务目前已全部在运行中，应该很快运行完毕，请耐心等待结束通知"
        if hourly_run:
            if st != et:
                msg = f"""您任务中未执行的部分预计于北京时间{st}开始执行，预计于北京时间{et}结束执行"""
        else:
            if be_cut_in_line:
                if st != et:
                    msg = f"""您的任务已经被其他任务插队，其中未执行的部分预计于北京时间{st}开始执行，预计于北京时间{et}结束执行"""
            else:
                if st != et:
                    msg = f"""您的任务已经移动到队列前端，预计于北京时间{st}开始执行，预计于北京时间{et}结束执行"""
        tmp_notify_list.extend([
            {"key": "插队情况","value": msg},
            {"key": "预计时间备注","value": note}
            ])
        title=input.get("title", "").replace("通知", "") + "--运行时间预测通知⏲️"
        # monitor_msg(input, tmp_notify_list, title)

    @provide_session
    def find_running_dag_run(dag_id: str, session: Session = NEW_SESSION):
        return session.query(DagRun).filter(
            DagRun.dag_id == dag_id,
            DagRun.state == State.RUNNING
        ).all()

    def notify_all_user(input):
        send_time_estimation(input)
        for dag_run in find_running_dag_run(dag_id=DAG_ID):
            send_time_estimation(input=dag_run.conf, be_cut_in_line=True)

    def send_starting_msg(input):
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        tmp_notify_list.append({
                "key": "状态详情",
                "value": "运行中",
            })
        title=input.get("title", "").replace("通知", "") + "--开始运行通知🚅"
        monitor_msg(input, tmp_notify_list, title)

    def send_failed_msg(input, fail_num):
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        tmp_notify_list.append({
                "key": "状态详情",
                "value": f"您的任务出现了{fail_num}个失败的子任务，请及时查看",
            })
        title=input.get("title", "").replace("通知", "") + "--失败通知❌"
        monitor_msg(input, tmp_notify_list, title)

    def send_cut_in_line_failed_msg(input):
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        tmp_notify_list.append({
                "key": "状态详情",
                "value": f"您的任务未能移动到队列前端，请重试，多次失败请联系霍天翔",
            })
        title=input.get("title", "").replace("通知", "") + "--置顶失败通知❌"
        monitor_msg(input, tmp_notify_list, title)

    def send_finish_msg(input, success_num, fail_num):
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        tmp_notify_list.append({
                "key": "状态详情",
                "value": f"您的任务已完成，出现了{success_num}个成功的和{fail_num}个失败的子任务，请及时查看",
            })
        title=input.get("title", "").replace("通知", "") + "--完成通知👏"
        monitor_msg(input, tmp_notify_list, title)

    def send_break_msg(input):
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        tmp_notify_list.append({
                "key": "状态详情",
                "value": "您的任务因为失败任务超过总任务数的30%，已熔断，请检查相关日志",
            })
        title=input.get("title", "").replace("通知", "") + "--熔断通知💥"
        monitor_msg(input, tmp_notify_list, title)

    def send_delete_msg(input):
        notify_list=input.get("notify_list", [])
        tmp_notify_list = copy.deepcopy(notify_list)
        tmp_notify_list.append({
                "key": "状态详情",
                "value": "您的任务已删除，未运行的任务不再运行，正在运行的任务将被强制停止",
            })
        title=input.get("title", "").replace("通知", "") + "--删除通知🧹"
        monitor_msg(input, tmp_notify_list, title)

    def check_batch_task_status(batch_id):
        backend_cli = StatusHook(f"dms_status_hook_{ENV}")
        res = backend_cli.service_post_request(
            url=DAG_CONFIG.get("status_uri","/data/getBatchTaskStatus"),
            body={"equal":{"batchTaskId":batch_id},"like":{},"limit":1,"page":1}
        )
        if res and res.get("code") == "200" and res.get("data"):
            return res.get("data")
        else:
            logging.info("getBatchTaskStatus error")
            return None

    @provide_session
    def mark_unfinished_dag_runs_failed(batch_id: str, monitor_dag_id: str, session: Session = NEW_SESSION):
        all_queued_dag_runs = session.query(DagRun).filter(
            DagRun.dag_id == monitor_dag_id,
            DagRun.run_id.like(f"{monitor_dag_id}.{batch_id}%"),
            DagRun.state.in_([State.QUEUED, State.RUNNING]),
        ).all()
        dag = get_dag(subdir=None, dag_id=monitor_dag_id, from_db=True)
        for dag_run in all_queued_dag_runs:
            if dag_run.state in (State.QUEUED, State.RUNNING):
                logging.info(f"marking dag run {dag_run.run_id} as failed")
                set_dag_run_state_to_failed(dag=dag, run_id=dag_run.run_id, commit=True)

    @provide_session
    def get_dag_runs_count(batch_id: str, monitor_dag_id: str, session: Session = NEW_SESSION):
        all_count = session.query(DagRun).filter(
            DagRun.dag_id == monitor_dag_id,
            DagRun.run_id.like(f"{monitor_dag_id}.{batch_id}%"),
        ).count()
        failed_count = session.query(DagRun).filter(
            DagRun.dag_id == monitor_dag_id,
            DagRun.state == State.FAILED,
            DagRun.run_id.like(f"{monitor_dag_id}.{batch_id}%"),
        ).count()
        success_count = session.query(DagRun).filter(
            DagRun.dag_id == monitor_dag_id,
            DagRun.state == State.SUCCESS,
            DagRun.run_id.like(f"{monitor_dag_id}.{batch_id}%"),
        ).count()
        queued_count = session.query(DagRun).filter(
            DagRun.dag_id == monitor_dag_id,
            DagRun.state == State.QUEUED,
            DagRun.run_id.like(f"{monitor_dag_id}.{batch_id}%"),
        ).count()
        running_count = session.query(DagRun).filter(
            DagRun.dag_id == monitor_dag_id,
            DagRun.state == State.RUNNING,
            DagRun.run_id.like(f"{monitor_dag_id}.{batch_id}%"),
        ).count()
        return all_count, failed_count, success_count, queued_count, running_count

    def monitor_dag_runs(**kwargs):
        input = kwargs["params"]
        logging.info(input)
        monitor_dag_id = input.get("monitor_dag_id")
        batch_id = input.get("batch_id")
        all_count, failed_count, success_count, queued_count, running_count \
            = get_dag_runs_count(batch_id, monitor_dag_id)
        logging.info(f"{failed_count} failed dag runs found")
        logging.info(f"{success_count} success dag runs found")
        logging.info(f"{all_count} dag runs found")
        batch_task_satus = check_batch_task_status(batch_id)
        locker = EQRedisLockHook()
        lock, _ = locker.acquire_lock(
            unique_id=None,
            lock_key=f"TIME_ESTIMATION_LOCKER.{monitor_dag_id}.{batch_id}",
            lock_expire_time=3600)
        if lock:
            send_time_estimation(input=input, hourly_run=True)
        if batch_task_satus == "Failed":
            mark_unfinished_dag_runs_failed(batch_id=batch_id, monitor_dag_id=monitor_dag_id)
            send_delete_msg(input)
            return True
        elif batch_task_satus == "CutInline":
            lock, uid = None, None
            for _ in range(5):
                lock, uid = locker.acquire_lock(
                    unique_id=None,
                    lock_key=f"CUT_IN_LINE_LOCKER:{monitor_dag_id}",
                    lock_expire_time=120)
                if lock:
                    if cut_in_line(monitor_dag_id, batch_id):
                        notify_all_user(input)
                    else:
                        send_cut_in_line_failed_msg(input)
                    break
                else:
                    time.sleep(20)
            if lock:
                locker.release_lock(unique_id=uid, lock_key=f"CUT_IN_LINE_LOCKER:{monitor_dag_id}")
            return False
        if running_count+failed_count+success_count == 0:
            update_batch_task_state(batch_id=batch_id, state="hang")
        else:
            batch_task_satus = check_batch_task_status(batch_id)
            if batch_task_satus in ("Pending", "Submit", "Waiting"):
                send_starting_msg(input)
                update_batch_task_state(batch_id=batch_id, state="start")
        if failed_count / all_count >= 0.3:
            mark_unfinished_dag_runs_failed(batch_id=batch_id, monitor_dag_id=monitor_dag_id)
            send_break_msg(input)
            update_batch_task_state(batch_id=batch_id, state="failed")
            return True
        if failed_count:
            lock, _ = locker.acquire_lock(
                unique_id=None,
                lock_key=f"FAILED_NOTIFY_LOCKER.{monitor_dag_id}.{batch_id}",
                lock_expire_time=3600)
            if lock:
                send_failed_msg(input, failed_count)
        if all_count == failed_count + success_count:
            send_finish_msg(input, success_count, failed_count)
            update_batch_task_state(batch_id=batch_id, state="finish")
            return True
        return False

    monitor = PythonSensor(
        task_id="monitor",
        python_callable=monitor_dag_runs,
        poke_interval=timedelta(minutes=5),
        mode="reschedule",
        timeout=timedelta(hours=DAG_CONFIG.get("monitor_time_hours", 24*3)),
    )

    monitor

m = dag_run_monitor()
