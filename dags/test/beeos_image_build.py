from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.postgresql_hook import <PERSON>gres<PERSON>ookEq
from hooks.status_hook import <PERSON>Hook
from hooks.redis_hook import EQRedisLockHook
from airflow.sensors.python import PythonSensor
from utils.beeos_util import *
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>Operator, PythonOperator
from custom_operators.ssh_xcom_operator import SSHXComOperator
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException
import datetime, logging
from airflow.models.param import Param
import json

DAG_ID = "BEEOS_IMAGE_BUILD_dev"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = DAG_CONFIG.get("env", "test")
pg_cli = PostgresHookEq(DAG_CONFIG.get("pg_id"))
status_cli = StatusHook(f"image_status_hook_{ENV}")

default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG.get("tags", ["beeos_image_build"]),
    max_active_runs=DAG_CONFIG.get("max_active_runs", 2),
    default_args=default_args,
    params={
        "run_closedloop_build": Param(False, "打开后运行闭环构建"),
        "sil_config": Param("", description="sil_config"),
        "useCustomImage": Param(False, description="useCustomImage"),
        "reBuild": Param(True, description="reBuild"),
        "enableFlowControl": Param(True, description="enableFlowControl"),
        "simTaskType": Param("pdc", description="simTaskType"),
        "dynamicModel": Param("eacon"),
        "vehicleModel": Param("EL100"),
        "algorithmConfig": Param(""),
        "enableTrimGlobalTrajectory": Param(False),
        "taskName": Param("test"),
        "beeos": Param("3.2.3_000"),
        "eq_runnable_common": Param("3.2.3_000"),
        "unity": Param("3.2.3_000"),
        "eq_auto_common": Param("3.2.3_000"),
        "eq_msg": Param("3.2.3_000"),
        "eq_lidar_detection": Param("3.2.3_000"),
        "eq_lidar_inference": Param("3.2.3_000"),
        "eq_multisensor_fusion": Param("3.2.3_000"),
        "eq_radar_perception": Param("3.2.3_000"),
        "eq_semantic_detection": Param("3.2.3_000"),
        "perception_common": Param("3.2.3_000"),
        "eq_prediction": Param("3.2.3_000"),
        "eq_decision_algorithm": Param("3.2.3_000"),
        "eq_matlab": Param("3.2.3_000"),
        "eq_bsw": Param("3.2.3_000"),
        "eq_perception_runnable": Param("3.2.3_000"),
        "eq_prediction_runnable": Param("3.2.3_000"),
        "eq_planning_runnable": Param("3.2.3_000"),
        "eq_control_runnable": Param("3.2.3_000"),
        "module": Param(["perception"]),
        "taskTemplateId": Param("5aa586c50b5943b19749d0bbb5cb3897"),
        "inputDatasetType": Param("LPCAP"),
        "inputDatasetIds": Param(["fcd1ebc732f3c46dc6abdb6891d60369"]),
        "taskCreateUser": "0c6ff2c1581c49289a6d163d79eec93f",
        "fullName": "霍天翔",
        "create_user_email": "<EMAIL>",
        "create_user_mobile": "13691266977",
        "env": "test",
        "simulationENV": "test",
        "taskType": "logsim",
        "tag": ""
    }
)
def beeos_image_build():
    def _decide_branch(**kwargs):
        input = kwargs["params"]
        ti = kwargs["ti"]
        date_id = str(datetime.datetime.now().strftime('%Y%m%d%H%M%S%f'))
        ti.xcom_push(key="date_id", value=date_id)
        logging.info(input)
        if input["run_closedloop_build"]:
            return "closedloop_init"
        else:
            return "sil_track_init"

    decide_branch = BranchPythonOperator(
        task_id="decide_branch",
        python_callable=_decide_branch
    )

    # 分两种分支，分别处理closedloop和sil_track
    # sil_track

    def check_param(input):
        params = input.get("sil_config")
        try:
            if isinstance(params, str):
                params = json.loads(params)
            config = ImageConfig.model_validate(params)
            tid = add_image_task(pg_cli, config)
            params["task_id"] = tid
            return tid, params
        except Exception as e:
            logging.error(e)
            return None, None

    def build_basic_info(input, success):
        sil_config = input.get("sil_config")
        tmp_task_name = ""
        if sil_config and isinstance(sil_config, dict):
            tmp_task_name = f"sil_task_{sil_config.get('task_id')}_{sil_config.get('creator')}"
        if input.get("taskName") != "":
            tmp_task_name = input.get("taskName")
        return [
            {
                "key": "任务名称",
                "value": tmp_task_name,
            },
            {
                "key": "任务类型",
                "value": "仿真镜像构建",
            },
            {
                "key": "任务状态",
                "value": "成功✅" if success else "失败❌",
            }
        ]

    def failed_notify(rid, **kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        create_user_email = input.get("create_user_email", "")
        create_user_mobile = input.get("create_user_mobile", "")
        failed_reason = ti.xcom_pull(key=f"failed_reason_{rid}")
        notify_list = build_basic_info(input, False)
        build_info = ti.xcom_pull(key="build_info")
        logging.info(failed_reason)
        if failed_reason == "pull code error":
            notify_list.extend([{
                "key": "失败原因",
                "value": "代码拉取失败"
            },
            {
                "key": "操作建议",
                "value": "目前平台已重试4次均无法拉取代码，请检查网络和代码仓库是否正常。建议等一段时间后再次尝试。"
            }
            ])
        else:
            notify_list.extend([{
                "key": "失败原因",
                "value": "编译失败"
            },
            {
                "key": "操作建议",
                "value": "请检查代码和编译日志中可能包含的错误信息。（如果你在这条通知中未收到日志，请联系霍天翔）"
            }
            ])
        notify_list.append({
            "key": "提示",
            "value": "下游任务已停止运行"
        })
        if build_info:
            for k, v in build_info.items():
                notify_list.append({
                    "key": k,
                    "value": v
                })
        notify_cli = FeishuHook()
        notify_cli.send_msg_card(
            user_email=create_user_email,
            user_mobile=create_user_mobile,
            title="仿真镜像构建任务通知",
            notify_list=notify_list

        )

    def success_notify(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        create_user_email = input.get("create_user_email", "")
        create_user_mobile = input.get("create_user_mobile", "")
        build_info = ti.xcom_pull(key="build_info")
        notify_list = build_basic_info(input, True)
        for k, v in build_info.items():
            notify_list.append({
                "key": k,
                "value": v
            })
        notify_cli = FeishuHook()
        notify_cli.send_msg_card(
            user_email=create_user_email,
            user_mobile=create_user_mobile,
            title="仿真镜像构建任务通知",
            notify_list=notify_list

        )

    @task(task_id="sil_track_init")
    def sil_track_init(**kwargs):
        input = kwargs["params"]
        ti = kwargs["ti"]
        tid, sil_dict = check_param(input)
        date_id = ti.xcom_pull(key="date_id")
        if tid:
            image_type = sil_dict.get("image_type")
            pull_code_command = f"echo '{json.dumps(sil_dict)}' > /tmp/sil_config_{tid}_{date_id}.json && "
            build_command = f"/usr/local/bin/eq_builder build -f /tmp/sil_config_{tid}_{date_id}.json;"
            if image_type=="alg":
                pull_code_command += f"/usr/local/bin/eq_builder pull -f /tmp/sil_config_{tid}_{date_id}.json"
            elif image_type=="update":
                pull_code_command += "echo 'skip pull code'"
                build_command = f"/usr/local/bin/eq_builder update -f /tmp/sil_config_{tid}_{date_id}.json"
            else:
                pull_code_command += "echo 'skip pull code'"
            c_num = DAG_CONFIG.get("build_cache_image_num", 3)
            clear_command = f"""/usr/local/bin/eq_builder clear -f /tmp/sil_config_{tid}_{date_id}.json;
docker images | grep registry | grep -v base | grep -v eq-sil-oasis | tail -n +{c_num} | awk """+"""'{print $3}' | xargs -r docker rmi;
docker ps -a | grep hours | grep Exited | awk '{print $1}' | xargs docker rm -v;
docker images | grep none | grep hours | awk '{print $3}' | xargs -r docker rmi;
docker images | grep localbuild | grep hours | awk '{print $3}'"""+f""" | xargs -r docker rmi;
cd /root/workspace/codes/sil-track/repo;
rm -rf beeos_{tid}; rm -rf oasis_{tid};
find /root/workspace/codes/sil-track/repo -maxdepth 1 -type d -mmin +120 -exec rm -rf """+"""{} +;
"""
            update_image_task(pg_cli, tid, ImageBuildStatus.prepare.value)
            ti.xcom_push(key="tid", value=tid)
            ti.xcom_push(key="date_id", value=date_id)
            ti.xcom_push(key="pull_code_command", value=pull_code_command)
            ti.xcom_push(key="build_command", value=build_command)
            ti.xcom_push(key="clear_command", value=clear_command)
            ti.xcom_push(key="failed_reason_sil_track", value=None)
            ti.xcom_push(key="sil_dict", value=sil_dict)
        else:
            update_image_task(pg_cli, tid, ImageBuildStatus.failed.value, "输入参数解析错误")
            ti.xcom_push(key="failed_reason_sil_track", value="param error")
            raise AirflowException("param error")

    sil_git_clone = SSHXComOperator(
        task_id="sil_git_clone",
        ssh_conn_id=DAG_CONFIG.get("ssh_id", "build_image_srv_dev"),
        command="{{ ti.xcom_pull(key='pull_code_command') }}",
        do_xcom_push=True,
        conn_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        cmd_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        retries=3
    )

    def _sil_check_code_prepared(**kwargs):
        ti = kwargs["ti"]
        tid = ti.xcom_pull(key="tid")
        sil_dict = ti.xcom_pull(key="sil_dict")
        clone_res_code = ti.xcom_pull(task_ids="sil_git_clone", key="ssh_exit")
        clone_res = ti.xcom_pull(task_ids="sil_git_clone", key="return_value")
        pull_log = f"http://***********:8049/task_{tid}.log"
        info = {
            "拉取代码日志": f"[{pull_log}]({pull_log})"
        }
        docker_name = sil_dict.get("name")
        info["镜像名称"] = docker_name
        ti.xcom_push(key="build_info", value=info)
        if isinstance(clone_res, dict) and isinstance(clone_res_code, int):
            if clone_res_code == 0:
                update_image_task(pg_cli, tid, ImageBuildStatus.building.value)
                ti.xcom_push(key="failed_reason_sil_track", value=None)
                return "sil_get_resource_lock"
            else:
                update_image_task(pg_cli, tid, ImageBuildStatus.failed.value, "代码拉取失败")
                ti.xcom_push(key="failed_reason_sil_track", value="pull code error")
                return "sil_pull_failed_notify"
        else:
            update_image_task(pg_cli, tid, ImageBuildStatus.failed.value, "代码拉取失败")
            ti.xcom_push(key="failed_reason_sil_track", value="pull code error")
            return "sil_pull_failed_notify"

    sil_check_code_prepared = BranchPythonOperator(
        task_id="sil_check_code_prepared",
        trigger_rule="none_skipped",
        python_callable=_sil_check_code_prepared
    )

    sil_image_build = SSHXComOperator(
        task_id="sil_image_build",
        ssh_conn_id=DAG_CONFIG.get("ssh_id", "build_image_srv_dev"),
        command="{{ ti.xcom_pull(key='build_command') }}",
        do_xcom_push=True,
        conn_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        cmd_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        retries=0
    )

    @task(task_id="update_status_after_build_finished", trigger_rule="none_skipped")
    def update_status_after_build_finished(**kwargs):
        ti = kwargs["ti"]
        tid = ti.xcom_pull(key="tid")
        sil_dict = ti.xcom_pull(key="sil_dict")
        build_res_code = ti.xcom_pull(task_ids="sil_image_build", key="ssh_exit")
        build_res = ti.xcom_pull(task_ids="sil_image_build", key="return_value")
        build_log = f"http://***********:8049/build_iamge_{tid}.log"
        pull_log = f"http://***********:8049/task_{tid}.log"
        info = {
            "编译日志": f"[{build_log}]({build_log})",
            "拉取代码日志": f"[{pull_log}]({pull_log})"
        }
        docker_name = sil_dict.get("name")
        info["镜像名称"] = docker_name
        logging.info(f"build info: {info}")
        ti.xcom_push(key="build_info", value=info)
        if isinstance(build_res, dict):
            build_res = build_res.get("return")
        logging.info(f"build result: {build_res_code}")
        if build_res_code == 0:
            ti.xcom_push(key="failed_reason_sil_track", value=None)
            update_image_task(pg_cli, tid, ImageBuildStatus.success.value)
        else:
            ti.xcom_push(key="failed_reason_sil_track", value="build error")
            update_image_task(pg_cli, tid, ImageBuildStatus.failed.value, "编译失败")
            raise AirflowException("编译失败")

    sil_build_success_notify = PythonOperator(
        task_id="sil_build_success_notify",
        python_callable=success_notify,
    )

    sil_pull_failed_notify = PythonOperator(
        task_id="sil_pull_failed_notify",
        python_callable=failed_notify,
        op_kwargs={"rid": "sil_code"}
    )

    sil_build_failed_notify = PythonOperator(
        task_id="sil_build_failed_notify",
        python_callable=failed_notify,
        op_kwargs={"rid": "sil_build"},
        trigger_rule="one_failed"
    )

    sil_clear = SSHXComOperator(
        task_id="sil_clear",
        ssh_conn_id=DAG_CONFIG.get("ssh_id", "build_image_srv_dev"),
        command="{{ ti.xcom_pull(key='clear_command') }}",
        conn_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        cmd_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        trigger_rule="all_done"
    )

    # closedloop

    @task(task_id="closedloop_init")
    def closedloop_init(**kwargs):
        input = kwargs["params"]
        ti = kwargs["ti"]
        date_id = ti.xcom_pull(key="date_id")
        try:
            ti.xcom_push(key="pull_code_command", value=pull_code_command(input, date_id))
            ti.xcom_push(key="build_command", value=build_command(input, date_id))
            ti.xcom_push(key="failed_reason_closedloop", value=None)
        except Exception as e:
            ti.xcom_push(key="failed_reason_closedloop", value="param error")
            raise AirflowException(f"param error: {e}")

    closedloop_git_clone = SSHXComOperator(
        task_id="closedloop_git_clone",
        ssh_conn_id=DAG_CONFIG.get("ssh_id", "build_image_srv_dev"),
        command="{{ ti.xcom_pull(key='pull_code_command') }}",
        do_xcom_push=True,
        conn_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        cmd_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        retries=3
    )

    def _check_code_prepared(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        docker_name = f"registry.eqfleetcmder.com/eq-sil/test_sil:data_close_loop_{input.get('tag')}"
        clone_res_code = ti.xcom_pull(task_ids="closedloop_git_clone", key="ssh_exit")
        clone_res = ti.xcom_pull(task_ids="closedloop_git_clone", key="return_value")
        if isinstance(clone_res, dict) and isinstance(clone_res_code, int):
            if clone_res_code == 0:
                ti.xcom_push(key="failed_reason_closedloop_code", value=None)
                return "closedloop_get_resource_lock"
            else:
                clone_res = clone_res.get("return")
                ti.xcom_push(key="failed_reason_closedloop_code", value="pull code error")
                status_cli.update_image_task(docker_name, "FAIL", clone_res[-200:])
                return "pull_failed_notify"
        else:
            ti.xcom_push(key="failed_reason_closedloop_code", value="pull code error")
            status_cli.update_image_task(docker_name, "FAIL", "pull code error")
            return "pull_failed_notify"

    check_code_prepared = BranchPythonOperator(
        task_id="check_code_prepared",
        trigger_rule="none_skipped",
        python_callable=_check_code_prepared
    )

    closedloop_image_build = SSHXComOperator(
        task_id="closedloop_image_build",
        ssh_conn_id=DAG_CONFIG.get("ssh_id", "build_image_srv_dev"),
        command="{{ ti.xcom_pull(key='build_command') }}",
        conn_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        cmd_timeout=DAG_CONFIG.get("ssh_timeout", 3600),
        do_xcom_push=True
    )

    @task(task_id="check_build_finished", trigger_rule="none_skipped")
    def check_build_finished(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        build_res_code = ti.xcom_pull(task_ids="closedloop_image_build", key="ssh_exit")
        build_res = ti.xcom_pull(task_ids="closedloop_image_build", key="return_value")
        info = get_info_from_log(build_res.get("return"))
        logging.info(f"build info: {info}")
        docker_name = f"registry.eqfleetcmder.com/eq-sil/test_sil:data_close_loop_{input.get('tag')}"
        info["镜像名称"] = docker_name
        ti.xcom_push(key="build_info", value=info)
        if isinstance(build_res, dict):
            build_res = build_res.get("return")
        logging.info(f"build result: {build_res_code}")
        if build_res_code == 0:
            ti.xcom_push(key="failed_reason_closedloop_build", value=None)
            status_cli.update_image_task(docker_name, "SUCCESS", build_res[-200:])
        else:
            ti.xcom_push(key="failed_reason_closedloop_build", value="build error")
            status_cli.update_image_task(docker_name, "FAIL", build_res[-200:])
            raise AirflowException("编译失败")

    build_success_notify = PythonOperator(
        task_id="build_success_notify",
        python_callable=success_notify,
    )

    pull_failed_notify = PythonOperator(
        task_id="pull_failed_notify",
        python_callable=failed_notify,
        op_kwargs={"rid": "closedloop_code"}
    )

    build_failed_notify = PythonOperator(
        task_id="build_failed_notify",
        python_callable=failed_notify,
        op_kwargs={"rid": "closedloop_build"},
        trigger_rule="one_failed"
    )

    def get_build_resource_lock(**kwargs):
        ti = kwargs["ti"]
        locker = EQRedisLockHook()
        for resource in DAG_CONFIG.get("build_resource", ["build_resource_1", "build_resource_2", "build_resource_3"]):
            res, _ = locker.acquire_lock(resource, resource, 7200)
            if res:
                ti.xcom_push(key="resource_lock", value=resource)
                return True
        return False

    sil_get_resource_lock = PythonSensor(
        task_id="sil_get_resource_lock",
        python_callable=get_build_resource_lock,
        poke_interval=datetime.timedelta(minutes=2),
        mode="reschedule",
        timeout=datetime.timedelta(hours=24),
    )

    closedloop_get_resource_lock = PythonSensor(
        task_id="closedloop_get_resource_lock",
        python_callable=get_build_resource_lock,
        poke_interval=datetime.timedelta(minutes=2),
        mode="reschedule",
        timeout=datetime.timedelta(hours=24),
    )

    def release_build_resource_lock(**kwargs):
        ti = kwargs["ti"]
        locker = EQRedisLockHook()
        resource_id = ti.xcom_pull(key="resource_lock")
        locker.release_lock(resource_id, resource_id)

    sil_release_resource_lock = PythonOperator(
        task_id="sil_release_resource_lock",
        python_callable=release_build_resource_lock,
        trigger_rule="none_skipped",
        op_kwargs={"release_resource": True}
    )

    closedloop_release_resource_lock = PythonOperator(
        task_id="closedloop_release_resource_lock",
        python_callable=release_build_resource_lock,
        trigger_rule="none_skipped",
        op_kwargs={"release_resource": True}
    )


    decide_branch >> sil_track_init() >> sil_git_clone >> sil_check_code_prepared >> sil_pull_failed_notify
    sil_check_code_prepared >> sil_get_resource_lock >> sil_image_build >> sil_release_resource_lock >> \
    update_status_after_build_finished() >> [sil_build_failed_notify, sil_build_success_notify] >> sil_clear

    decide_branch >> closedloop_init() >> closedloop_git_clone >> check_code_prepared >> pull_failed_notify
    check_code_prepared >> closedloop_get_resource_lock >> closedloop_image_build >> closedloop_release_resource_lock \
    >> check_build_finished() >> [build_failed_notify, build_success_notify]

beeos_image_build()
