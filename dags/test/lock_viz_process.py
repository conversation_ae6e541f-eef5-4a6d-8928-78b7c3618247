# -*- coding: utf-8 -*-

from custom_operators.pod_operator import <PERSON><PERSON><PERSON><PERSON><PERSON>perator
from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import StatusHook
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException
import datetime
import logging

DAG_ID = "LOCK_VIZ_PROCESS_dev"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = DAG_CONFIG.get("env", "test")
TEST = ENV in ("test", "dev")

default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    dagrun_timeout=datetime.timedelta(hours=DAG_CONFIG.get("dagrun_timeout", 1)),
    tags=DAG_CONFIG["tags"],
    default_args=default_args
)
def lock_viz_process():
    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(input)
        pod_params = input.get("ori_params").get("data").get("params")
        argo_template = input.get("ori_params").get("data").get("argoTemplate")
        image = pod_params.get("image_name") + ":" + pod_params.get("image_tag")
        data_info_key = pod_params.get("data_info_key")
        trace_pcap = pod_params.get("trace_pcap")
        resource = {
            "resource_requests": {
                "cpu": argo_template.get("cpuRequestSize"),
                "memory": f'{argo_template.get("memoryRequestSize")}Mi'
            },
            "resource_limits": {
                "cpu": argo_template.get("cpuLimitSize"),
                "memory": f'{argo_template.get("memoryLimitSize")}Mi'
            }
        }
        ti.xcom_push(key="image", value=image)
        ti.xcom_push(key="workflow_name", value=pod_params.get("workflow_name"))
        ti.xcom_push(key="envs", value={"DATA_INFO_KEY": data_info_key, "TRACE_PCAP": trace_pcap})
        ti.xcom_push(key="image_pull_policy", value=argo_template.get("imagePullPolicy"))
        ti.xcom_push(key="resource", value=resource)

    pod_task = EqPodOperator(
        task_id="lock_viz_process_pod",
        name="{{ ti.xcom_pull(key='workflow_name') }}",
        namespace=DAG_CONFIG.get("namespace"),
        image="{{ ti.xcom_pull(key='image') }}",
        image_pull_policy="{{ ti.xcom_pull(key='image_pull_policy') }}",
        envs_json="{{ ti.xcom_pull(key='envs') }}",
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        command=r'/entrypoint.sh; status=$?; echo "{\"status\": $status}" > /airflow/xcom/return.json; exit $status',
        do_xcom_push=True,
        mount_argo_pps_config=True,
        test_run=TEST,
        on_finish_action='keep_pod',
        retries=0,
        retry_delay=datetime.timedelta(seconds=300),
     )

    @task(trigger_rule="all_done")
    def update_status(**kwargs):
        ti = kwargs["ti"]
        status = ti.xcom_pull(task_ids="lock_viz_process_pod")
        workflow_name = ti.xcom_pull(key="workflow_name")
        if isinstance(status, dict) and status.get("status")==0:
            status = "true"
        else:
            status = "false"
        logging.info(f"{status}, {workflow_name}")
        status_hook_id = DAG_CONFIG.get("status_hook_id") + f"_{ENV}"
        if StatusHook(status_hook_id).update_status(status, workflow_name):
            ti.xcom_push(key="update_result", value="success")
            return
        else:
            ti.xcom_push(key="update_result", value="failed")
            raise AirflowException("update status failed")

    def build_basic_info(input, success, failed_reason):
        failed_reason_msg = ""
        if "update_status_failed" in failed_reason:
            failed_reason_msg += "我们已经进行了多次重试，但向后端更新状态失败。\n"
        if "pod_failed" in failed_reason:
            failed_reason_msg += "我们已经进行了多次重试，但pod执行依然失败。"
        return [
            {
                "key": "任务名称",
                "value": input.get("ori_params", {}).get("taskDO", {}).get("taskName"),
            },
            {
                "key": "任务类型",
                "value": "锁存数据可视化任务",
            },
            {
                "key": "任务状态",
                "value": "成功✅" if success else "失败❌",
            },
            {
                "key": "失败原因",
                "value": failed_reason_msg
            }
        ]

    @task(task_id="failed_notify", trigger_rule="one_failed")
    def _failed_notify(**kwargs):
        ti = kwargs["ti"]
        status = ti.xcom_pull(task_ids="lock_viz_process_pod")
        update_result = ti.xcom_pull(task_ids="update_status")
        failed_reason = []
        if not (isinstance(status, dict) and status.get("status")==0):
            failed_reason.append("pod_failed")
        if update_result == "failed":
            failed_reason.append("update_status_failed")
        input = kwargs["params"]
        logging.info(f"email: {input.get('email')}, phone: {input.get('phone')}")
        logging.info(input)
        feishu_cli = FeishuHook()
        basic_info = build_basic_info(input, False, failed_reason)
        feishu_cli.send_msg_card(
            title="锁存数据可视化任务通知",
            notify_list=basic_info,
            user_email=input.get("email"),
            user_mobile=input.get("phone")
        )


    failed_notify = _failed_notify()

    init() >> pod_task >> update_status() >> failed_notify
    pod_task >> failed_notify

lock_viz_process()
