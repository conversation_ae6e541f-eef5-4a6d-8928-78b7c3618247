from custom_operators.pod_operator import <PERSON>q<PERSON>odOperator
from airflow.sensors.python import PythonSensor
from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import StatusHook
from hooks.tos_hook import TosHook
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.models import TaskInstance
from airflow.utils.state import State
import datetime, logging
import json
from kubernetes.client import models as k8s
from custom_operators.eq_job_operator import get_node_lock, release_node_lock

DAG_ID = "SIM_DATA_GEN_TASK_dev"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = Variable.get("ENV", "dev")
TEST = ENV in ("test", "dev")
tos_cli = TosHook(DAG_CONFIG.get("tos_hook_id", "tos-gpu-data"))
default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    tags=DAG_CONFIG.get("tags", ["data_gen"]),
    default_args=default_args
)
def sim_data_gen_task():
    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(input)
        task_id = input.get("taskId")
        folder_name = input.get("folderName")
        if input.get("storagePath").startswith("/gpu-data/simGeneData"):
            input["storagePath"] = f"/gpu-data/simGeneData/{folder_name}"
        else:
            ti.xcom_push(key="error", value="invalid_input")
            raise Exception("storagePath is invalid")
        input["dataSource"] = DAG_CONFIG.get("data_source", [])
        clip_num = input.get("clipNum", 10)
        input["clipNum"] = clip_num
        run_id = kwargs["run_id"]
        ti.xcom_push(key="run_id", value=run_id)
        input_json = json.dumps(input)
        logging.info(f"uploading {input_json}")
        tos_cli.load_string(
            string_data=input_json,
            key=f"simGeneData/{folder_name}/auto_gen_config",
            bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
            replace=True
        )
        progress_path = f"simGeneData/{folder_name}/{folder_name}"
        locks = []
        while len(locks) == 0:
            for _ in range(DAG_CONFIG.get("max_concurrency", 5)):
                lock_id = get_node_lock(time_out=3600*24)
                if lock_id:
                    locks.append(lock_id)
        pod_num = len(locks)
        ti.xcom_push(key="locks", value=locks)
        commands = []
        for index in range(pod_num):
            command = f"""cp /gpu-data/simGeneData/{folder_name}/auto_gen_config /app/ue/EQ/Content/auto_gen_config &&
            {DAG_CONFIG.get("main_cmd", "python3 /app/Run.py")} --pod_index {index} --pod_num {pod_num};
            sleep {DAG_CONFIG.get("debug_time", 3600)};
            """
            command += r'echo "{\"status\": $?}" > /airflow/xcom/return.json'
            commands.append(command)
        resource = {
            "resource_requests": {
                "cpu": DAG_CONFIG.get("cpu_request", 6),
                "memory": f'{DAG_CONFIG.get("memory_request", 8000)}Mi',
                "nvidia.com/gpu": "1"
            },
            "resource_limits": {
                "nvidia.com/gpu": "1"
            }
        }
        ti.xcom_push(key="resource", value=resource)
        ti.xcom_push(key="progress_path", value=progress_path)
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        http_body = {
            "task_type": "SIM_DATA_GEN_TASK",
            "task_id": task_id,
            "task_status": 2
        }
        url = DAG_CONFIG.get("status_hook_url", "/data/callbackTaskStatus")
        if status_cli.interface_post_request(url=url, body=http_body):
            logging.info(f"{status_hook_id} post request success")
            return commands
        else:
            logging.error(f"{status_hook_id} post request failed")
            return []

    commands = init()

    pod_task = EqPodOperator.partial(
        task_id="data_gen_pod",
        name="{{ ti.xcom_pull(key='run_id') }}",
        namespace=DAG_CONFIG.get("namespace", "simulation"),
        image=DAG_CONFIG.get("image", "registry.eqfleetcmder.com/eq-sim/auto_gen:v1.1"),
        image_pull_policy=DAG_CONFIG.get("image_pull_policy", "Always"),
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        do_xcom_push=True,
        pvc_with_path={
            "data-gen-pvc": "/gpu-data"
        },
        labels={"hami.io/webhook": "ignore"},
        annotations={
            "vci.vke.volcengine.com/desired-system-storage": "120",
            "vci.vke.volcengine.com/auto-imc-disk-size": "60",
            "vci.vke.volcengine.com/enable-auto-create-imc": "true",
            "vci.vke.volcengine.com/gpu-driver-version": "tesla-535.161.07",
            "vci.vke.volcengine.com/preferred-instance-family": "vci.gni3",
            "vke.volcengine.com/burst-to-vci": "enforce"
        },
        container_security_context=k8s.V1SecurityContext(run_as_user=1001),
        test_run=TEST,
        retries=2,
        execution_timeout=datetime.timedelta(hours=24*7),
        startup_timeout_seconds=3600,
    ).expand(command=commands)

    def update_progress(**kwargs):
        input = kwargs["params"]
        task_id = input.get("taskId")
        ti = kwargs["ti"]
        dag = kwargs['dag']
        run_id = kwargs['dag_run'].run_id
        locks = ti.xcom_pull(key="locks")
        total_running_pod = len(locks)
        for index in range(len(locks)):
            mapped_ti = TaskInstance(
                task=dag.get_task('data_gen_pod'),
                execution_date=kwargs['execution_date'],
                run_id=run_id,
                map_index=index
            )
            mapped_ti.refresh_from_db()
            if mapped_ti.state != State.RUNNING:
                total_running_pod -= 1
        progress_path = ti.xcom_pull(key="progress_path")
        progress_key = f"{progress_path}/progress.json"
        harm_progress_key = f"{progress_path}/harm_progress.json"
        avg_progress = 0
        avg_remain_time = 86400
        if tos_cli.head_object(
            bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
            key=progress_key
        ) and tos_cli.head_object(
            bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
            key=harm_progress_key
        ):
            progress = json.loads(tos_cli.read_key(
                bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
                key=progress_key
            ))
            harm_progress = json.loads(tos_cli.read_key(
                bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
                key=harm_progress_key
            ))
            avg_progress = int(progress.get("progress")*0.7 + harm_progress.get("progress")*0.3)
            avg_remain_time = int(progress.get("remain_time")*0.7 + harm_progress.get("remain_time")*0.3)
            logging.info(f"progress: {progress}, harm_progress: {harm_progress}")
            logging.info(f"avg_progress: {avg_progress}, avg_remain_time: {avg_remain_time}")
        elif tos_cli.head_object(
            bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
            key=progress_key
        ):
            progress = json.loads(tos_cli.read_key(
                bucket_name=DAG_CONFIG.get("tos_bucket", "gpu-data"),
                key=progress_key
            ))
            avg_progress = int(progress.get("progress")*0.7)
            avg_remain_time = int(progress.get("remain_time")*0.7)
            logging.info(f"progress: {progress}")
            logging.info(f"avg_progress: {avg_progress}, avg_remain_time: {avg_remain_time}")
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        http_body = {
            "task_type": "SIM_DATA_GEN_TASK",
            "task_id": task_id,
            "task_status": 2,
            "progress": avg_progress,
            "remain_time": avg_remain_time
        }
        url = DAG_CONFIG.get("status_hook_url", "/data/callbackTaskStatus")
        if status_cli.interface_post_request(url=url, body=http_body):
            logging.info(f"{status_hook_id} post request success")
        else:
            ti.xcom_push(key="error", value="update_status_failed")
            logging.error(f"{status_hook_id} post request failed")
        if avg_progress > 98 or total_running_pod == 0:
            return True
        else:
            return False

    progress_monitor = PythonSensor(
        task_id="monitor",
        python_callable=update_progress,
        poke_interval=datetime.timedelta(minutes=5),
        mode="reschedule",
        timeout=datetime.timedelta(hours=DAG_CONFIG.get("monitor_time_hours", 24*7)),
    )

    @task(trigger_rule="all_done")
    def update_status(**kwargs):
        ti = kwargs["ti"]
        status = ti.xcom_pull(task_ids="data_gen_pod")
        locks = ti.xcom_pull(key="locks")
        for lock in locks:
            release_node_lock(lock)
        logging.info(f"status: {status}")
        input = kwargs["params"]
        task_id = input.get("taskId")
        total_status = 3
        if isinstance(status, list):
            for item in status:
                if item.get("status")==0 and total_status==3:
                    total_status = 3
                else:
                    total_status = 5
        logging.info(f"{task_id}: {total_status}")
        ti.xcom_push(key="total_status", value=total_status)
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        http_body = {
            "task_type": "SIM_DATA_GEN_TASK",
            "task_id": task_id,
            "task_status": total_status,
            "progress": 100,
            "remain_time": 0
        }
        url = DAG_CONFIG.get("status_hook_url", "/data/callbackTaskStatus")
        if status_cli.interface_post_request(url=url, body=http_body):
            logging.info(f"{status_hook_id} post request success")
        else:
            ti.xcom_push(key="error", value="update_status_failed")
            logging.error(f"{status_hook_id} post request failed")

    def build_basic_info(task_name, success, failed_reason, output_path=""):
        failed_reason_msg = ""
        if "invalid_input" in failed_reason:
            failed_reason_msg = "您输入的存储位置有误，请检查后重新输入或使用默认值。"
        if "update_status_failed" in failed_reason:
            failed_reason_msg += "我们已经进行了多次重试，但向后端更新状态失败。"
        if "pod_failed" in failed_reason:
            failed_reason_msg += "我们已经进行了多次重试，但pod执行依然失败。"
        res = [
            {
                "key": "任务名称",
                "value": task_name,
            },
            {
                "key": "任务类型",
                "value": "仿真数据生成任务",
            },
            {
                "key": "任务状态",
                "value": "成功✅" if success else "失败❌",
            }
        ]
        if failed_reason_msg:
            res.append({
                "key": "失败原因",
                "value": failed_reason_msg
            })
        else:
            res.append({
                "key": "结果路径",
                "value": output_path
            })
        return res

    @task(task_id="failed_notify", trigger_rule="all_done")
    def _failed_notify(**kwargs):
        ti = kwargs["ti"]
        total_status = ti.xcom_pull(task_ids="total_status")
        update_result = ti.xcom_pull(key="error")
        failed_reason = []
        if total_status == 5:
            failed_reason.append("pod_failed")
        if update_result:
            failed_reason.append(update_result)
        input = kwargs["params"]
        task_name = input.get("taskName")
        folder_name = input.get("folderName")
        output_path = f"/gpu-data/simGeneData/{folder_name}"
        logging.info(f"email: {input.get('email')}, phone: {input.get('phone')}")
        feishu_cli = FeishuHook()
        if failed_reason:
            basic_info = build_basic_info(task_name, False, failed_reason)
            feishu_cli.send_msg_card(
                title="仿真数据生成任务通知",
                notify_list=basic_info,
                user_email=input.get("email"),
                user_mobile=input.get("phone")
            )
        else:
            basic_info = build_basic_info(task_name, True, failed_reason, output_path)
            feishu_cli.send_msg_card(
                title="仿真数据生成任务通知",
                notify_list=basic_info,
                user_email=input.get("email"),
                user_mobile=input.get("phone")
            )

    failed_notify = _failed_notify()

    commands >> [pod_task, progress_monitor] >> update_status() >> failed_notify
    pod_task >> failed_notify

sim_data_gen_task()
