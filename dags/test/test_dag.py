import datetime
from airflow.models.dag import DAG
from airflow.decorators import task
from airflow.models.variable import Variable
import logging
import time

DAG_ID = "test_dag"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)

with DAG(
    dag_id=DAG_ID,
    schedule=None,
    start_date=datetime.datetime(2022, 1, 1),
    dagrun_timeout=datetime.timedelta(minutes=60),
    tags=["cluster"],
    max_active_runs=DAG_CONFIG.get("max_active_runs", 102),
    catchup=False
) as dag:
    
    @task()
    def test_task(**kwargs):
        context = kwargs
        logging.info(f"{context['dag'].dag_id}")
        logging.info(f"{context['ti'].run_id}")
        time.sleep(120)

    test_task()