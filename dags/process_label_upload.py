# -*- coding: utf-8 -*-

from custom_operators.pod_operator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import <PERSON><PERSON><PERSON>
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException

import datetime
import logging
import json


DAG_ID = "PROCESS_LABEL_UPLOAD"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = DAG_CONFIG.get("env", "test")
TEST = ENV in ("test", "dev")

default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG["tags"],
    default_args=default_args
)
def process_label_upload():
    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(input)
        logging.info(type(input))

        task_running_context = input.get("task_running_context")
        airflow_params = task_running_context.get("airflowParams")
        airflow_params = json.loads(airflow_params)
        logging.info(airflow_params)
        logging.info(type(airflow_params))

        run_id = kwargs["run_id"]
        task_id =  airflow_params.get("task_id")
        tar_path = airflow_params.get("tar_path")
        resource = DAG_CONFIG.get("resource")

        ti.xcom_push(key="run_id", value=run_id.lower().replace("_", "-").replace(".", "-"))
        ti.xcom_push(key='tar_path', value=tar_path) # tos://bucket/path/xxx.tar.gz
        ti.xcom_push(key="resource", value=resource)
        ti.xcom_push(key="task_id", value=task_id)

        # 回调状态更新
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        update_task_status = status_cli.update_task_status(
            taskId=task_id,
            taskDetailId="",
            status="Running",
            info="",
            errMsg=""
        )
        if update_task_status:
            logging.info(f"{status_hook_id} post request success")
        else:
            logging.error(f"{status_hook_id} post request failed")


    label_upload_task = EqPodOperator(
        task_id="process_label_upload_pod",
        name="{{ ti.xcom_pull(key='run_id') }}",
        namespace=DAG_CONFIG.get("namespace", "argo"),
        image=DAG_CONFIG.get("image"),
        image_pull_policy=DAG_CONFIG.get("image_pull_policy"),
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        command="/opt/run.py {{ ti.xcom_pull(key='tar_path') }};" + \
                r' status=$?; echo "{\"status\": $status}" > /airflow/xcom/return.json; exit $status',
        do_xcom_push=True,
        mount_argo_pps_config=True,
        test_run=TEST,
        on_finish_action='keep_pod',
        tolerations=[{
            "key": "node",
            "operator": "Equal",
            "value": "gpu",
            "effect": "NoSchedule"
        }],
        retries=0,
        retry_delay=datetime.timedelta(seconds=300),
        execution_timeout=datetime.timedelta(hours=2)
     )

    @task(trigger_rule="all_done")
    def update_status(**kwargs):
        ti = kwargs["ti"]
        task_id = ti.xcom_pull(key="task_id")
        status = ti.xcom_pull(task_ids="process_label_upload_pod")
        if isinstance(status, dict) and status.get("status")==0:
            status = "Succeeded"
        else:
            status = "Failed"
        logging.info(f"{status}")
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        update_task_status = status_cli.update_task_status(
            taskId=task_id,
            taskDetailId="",
            status=status,
            info="",
            errMsg=""
        )
        if update_task_status:
            logging.info(f"{status_hook_id} post request success")
        else:
            ti.xcom_push(key="update_result", value="failed")
            logging.error(f"{status_hook_id} post request failed")


    @task(task_id="failed_notify", trigger_rule="one_failed", retries=0)
    def _failed_notify(**kwargs):
        ti = kwargs["ti"]
        status = ti.xcom_pull(task_ids="process_label_upload_pod")
        update_result = ti.xcom_pull(key="update_result")
        failed_reason = []
        if not (isinstance(status, dict) and status.get("status")==0):
            failed_reason.append("pod_failed")
        if update_result == "failed":
            failed_reason.append(update_result)
        input = kwargs["params"]
        logging.info(f"email: {input.get('email')}, phone: {input.get('phone')}")
        logging.info(input)
        logging.info(f"failed_reason: {failed_reason}")
        # 强制使 DAG 失败
        raise AirflowException("任务失败，已发送通知")


    failed_notify = _failed_notify()
    init() >> label_upload_task  >>  update_status() >> failed_notify
    label_upload_task >> failed_notify


process_label_upload()
