from airflow.decorators import dag, task
from airflow.sensors.python import PythonSensor
from airflow.models.variable import Variable
from datetime import datetime, timedelta
from hooks.tos_hook import TosHook
from hooks.status_hook import StatusHook
import logging, json, time
from airflow.api.common.trigger_dag import trigger_dag
import os
# 指定后端环境
ENV = Variable.get("ENV", "dev")
TEST = ENV in ("test", "dev")

DAG_ID = "init_dag"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
tos_cli = TosHook(DAG_CONFIG.get("tos_hook_id", "tos-gpu-data"))
default_args = {
    'retries': 2
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime(2025, 1, 1),
    catchup=False,
    tags=DAG_CONFIG.get("tags", ["simulation_testing"]),
    max_active_runs=1,
    default_args=default_args
)
def init_dag():

    def build_basic_info(task_name, task_type, batch_id):
        prefix = DAG_CONFIG.get("task_frontend_prefix")
        url = f"{prefix}?id={batch_id}"
        info = [
            {
                "key": "任务名称",
                "value": task_name,
            },
            {
                "key": "任务类型",
                "value": task_type,
            },
            {
                "key": "任务页面",
                "value": f"[{url}]({url})",
            }
        ]
        return info

    def trigger_manager_dag(input, tmp_file_name):
        monitor_dag_id = DAG_CONFIG.get("monitor_dag_id", "monitor_dag") # 监控dag
        batch_id = input.get("task_info_id")
        task_type = input.get("task_type")
        user_email = input.get("user_email", "")
        user_mobile = input.get("user_mobile", "")
        with open(tmp_file_name, "r") as f:
            for line in f.readlines():
                line = json.loads(line)
                task_name = line.get("taskName", "")
                if task_type == "LOG_SIM" and check_use_gpu(line):
                    task_type = f"{task_type}_GPU"
                break
        notify_list = build_basic_info(task_name, task_type, batch_id)
        conf = {
            "user_email": user_email,
            "user_mobile": user_mobile,
            "notify_list": notify_list,
            "title": "任务通知",
            "batch_id": batch_id,
            "task_type": task_type # 被监控dag
        }
        try:
            logging.info(f"conf: {conf}")
            trigger_dag(dag_id=monitor_dag_id,
                        run_id=f"{monitor_dag_id}.{batch_id}",
                        conf=conf,
                        replace_microseconds=False)
        except Exception as e:
            logging.error(f"trigger_dag error: {e}")

    def check_use_gpu(task_conf):
        return task_conf.get("containerMap", {}).get("alg-server", {}).get("key", {}).get("gpuRequestSize") > 0

    def trigger_downstream_dag(task_type, task_info_id, tos_bucket_name, batch_task_tos_key_list):
        success_cnt = 0
        all_cnt = 0
        for tos_key in batch_task_tos_key_list:
            try:
                # 下载文件
                tmp_file_name = tos_cli.download_file(key=tos_key, bucket_name=tos_bucket_name)

                # 读取jsonl文件，每一行作为一个任务配置
                with open(tmp_file_name, "r") as f:
                    for line in f.readlines():
                        line = line.strip()
                        if not line:  # 跳过空行
                            continue
                        try:
                            # jsonl的每一行是发送到下游dag的参数
                            task_conf = json.loads(line)
                            all_cnt += 1
                            # 获取task_type作为下游dag的dag_id
                            downstream_dag_id = task_type
                            if task_type == "LOG_SIM" and check_use_gpu(task_conf):
                                downstream_dag_id = f"{downstream_dag_id}_GPU"
                            batch_id = task_info_id
                            task_detail_id = task_conf.get("id")
                            run_id = f"{downstream_dag_id}.{batch_id}.{task_detail_id}.{str(int(time.time()*1000))}"

                            # 触发下游DAG
                            try:
                                logging.info(f"开始触发DAG: {downstream_dag_id}")
                                logging.info(f"conf: {task_conf}")
                                trigger_dag(dag_id=downstream_dag_id,
                                           run_id=run_id,
                                           conf=task_conf,
                                           replace_microseconds=False)
                                logging.info(f"Successfully triggered DAG {downstream_dag_id} with run_id {run_id}")
                                success_cnt += 1
                            except Exception as e:
                                logging.error(f"Failed to trigger DAG {downstream_dag_id}: {e}")

                        except json.JSONDecodeError as e:
                            logging.error(f"Failed to parse JSON line: {line}, error: {e}")
                            continue

                # 清理临时文件
                os.remove(tmp_file_name)

            except Exception as e:
                logging.error(f"Error processing file {tos_key}: {e}")
                continue
        return success_cnt, all_cnt

    def create_batch_tasks(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(f"input: {input}")
        task_info_id = input.get("task_info_id")
        task_type = input.get("task_type")
        tos_bucket_name = input.get("tos_bucket_name")
        batch_task_tos_key_list = input.get("batch_task_tos_key_list")

        # 检查batch_task_tos_key_list中的所有文件是否都已上传
        if not batch_task_tos_key_list:
            logging.error("batch_task_tos_key_list is empty")
            ti.xcom_push(key="downsteam_msg", value="batch_task_tos_key_list is empty")
            raise Exception("batch_task_tos_key_list is empty")

        # 检查所有文件是否存在
        missing_files = []
        for tos_key in batch_task_tos_key_list:
            try:
                # 使用check_for_key方法检查文件是否存在
                if not tos_cli.check_for_key(key=tos_key, bucket_name=tos_bucket_name):
                    missing_files.append(tos_key)
            except Exception as e:
                logging.error(f"Error checking file {tos_key}: {e}")
                missing_files.append(tos_key)

        if missing_files:
            logging.info(f"Missing files: {missing_files}")
            ti.xcom_push(key="downsteam_msg", value=f"Missing files: {missing_files}")
            return False

        logging.info("All files in batch_task_tos_key_list are available")

        status_hook_id = DAG_CONFIG.get("status_hook_id") + f"_{ENV}"
        status_hook = StatusHook(status_hook_id)
        # 回调后端，创建任务详情，避免tos文件上传失败
        status_hook.service_post_request("/task/updateDetailInfo", input)
        time.sleep(5) # 等待后端创建任务详情

        # 遍历每个jsonl文件，解析并触发下游DAG
        success_cnt, all_cnt = trigger_downstream_dag(task_type, task_info_id, tos_bucket_name, batch_task_tos_key_list)
        if all_cnt > 0 and success_cnt/all_cnt > 0.8:
            logging.info(f"Successfully triggered {success_cnt} DAGs")
        else:
            ti.xcom_push(key="downsteam_msg", value=f"Failed to trigger {all_cnt - success_cnt} DAGs")
            raise Exception(f"Failed to trigger {all_cnt - success_cnt} DAGs")

        # 触发监控DAG（保持原有逻辑）
        if batch_task_tos_key_list:
            # 使用第一个文件来触发监控DAG
            first_file_key = batch_task_tos_key_list[0]
            try:
                tmp_file_name = tos_cli.download_file(key=first_file_key, bucket_name=tos_bucket_name)
                trigger_manager_dag(input, tmp_file_name)
                os.remove(tmp_file_name)
            except Exception as e:
                ti.xcom_push(key="downsteam_msg", value=f"Error triggering manager DAG: {e}")
                logging.error(f"Error triggering manager DAG: {e}")
        ti.xcom_push(key="downsteam_msg", value="success")
        return True

    monitor = PythonSensor(
        task_id="monitor",
        python_callable=create_batch_tasks,
        poke_interval=timedelta(minutes=2),
        mode="reschedule",
        timeout=timedelta(minutes=7),
    )

    @task(task_id="init_dag_notify", trigger_rule="all_done")
    def init_dag_notify(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        downsteam_msg = ti.xcom_pull(task_ids="monitor", key="downsteam_msg")
        status_hook_id = DAG_CONFIG.get("status_hook_id") + f"_{ENV}"
        status_hook = StatusHook(status_hook_id)
        retry_cnt = 0
        if downsteam_msg == "success":
            # 成功通知
            while status_hook.service_post_request(
                "/task/updateTaskInfo",
                {"taskInfoId": input.get("task_info_id"), "taskState": "Running"}).get("code") != "200" and retry_cnt < 5:
                time.sleep(5)
                retry_cnt += 1
        else:
            # 失败通知
            while status_hook.service_post_request(
                "/task/updateTaskInfo",
                {"taskInfoId": input.get("task_info_id"), "taskState": "Failed"}).get("code") != "200" and retry_cnt < 5:
                time.sleep(5)
                retry_cnt += 1


    monitor >> init_dag_notify()

m = init_dag()
