from airflow.decorators import dag, task
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.models.variable import Variable
from datetime import datetime, timedelta
import logging
import json
from typing import Dict

try:
    import pandas as pd
except ImportError:
    pd = None

# DAG配置
DAG_ID = "monthly_data_backup"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True, default_var={
    "postgres_conn_id": "airflow_db",
    "s3_conn_id": "aws_default",
    "s3_bucket": "airflow-backup-bucket",
    "s3_prefix": "airflow-data-backup",
    "tags": ["backup", "monthly"]
})

default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': datetime(2025, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5)
}

@dag(
    dag_id=DAG_ID,
    default_args=default_args,
    description='Monthly backup of Airflow PostgreSQL data to S3',
    schedule_interval='0 2 1 * *',  # 每月1日凌晨2点执行
    catchup=False,
    tags=DAG_CONFIG.get("tags", ["backup", "monthly"]),
    max_active_runs=1
)
def monthly_data_backup():

    @task
    def get_backup_date_range(**context):
        """获取上个月的日期范围"""
        execution_date = context['execution_date']

        # 计算上个月的开始和结束日期
        first_day_current_month = execution_date.replace(day=1)
        last_day_previous_month = first_day_current_month - timedelta(days=1)
        first_day_previous_month = last_day_previous_month.replace(day=1)

        date_range = {
            'start_date': first_day_previous_month.strftime('%Y-%m-%d'),
            'end_date': last_day_previous_month.strftime('%Y-%m-%d'),
            'year_month': first_day_previous_month.strftime('%Y-%m'),
            'backup_timestamp': execution_date.strftime('%Y%m%d_%H%M%S')
        }

        logging.info(f"Backup date range: {date_range}")
        return date_range

    @task
    def backup_dag_run_table(date_range: Dict[str, str]):
        """备份dag_run表数据"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        # 查询上个月的dag_run数据
        sql = """
        SELECT
            *
        FROM dag_run
        WHERE execution_date >= %s
        AND execution_date < %s + INTERVAL '1 day'
        ORDER BY execution_date
        """

        if pd is None:
            raise ImportError("pandas is required for this DAG. Please install it: pip install pandas")

        df = postgres_hook.get_pandas_df(
            sql,
            parameters=[date_range['start_date'], date_range['end_date']]
        )

        if not df.empty:
            # 转换为Parquet格式（Trino友好）
            s3_key = f"{DAG_CONFIG['s3_prefix']}/dag_run/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/dag_run_{date_range['backup_timestamp']}.parquet"

            # 上传到S3
            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} dag_run records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No dag_run data found for the specified date range")

        return len(df) if not df.empty else 0

    @task
    def backup_job_table(date_range: Dict[str, str]):
        """备份job表数据"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        sql = """
        SELECT
            *
        FROM job
        WHERE start_date >= %s
        AND start_date < %s + INTERVAL '1 day'
        ORDER BY start_date
        """

        df = postgres_hook.get_pandas_df(
            sql,
            parameters=[date_range['start_date'], date_range['end_date']]
        )

        if not df.empty:
            s3_key = f"{DAG_CONFIG['s3_prefix']}/job/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/job_{date_range['backup_timestamp']}.parquet"

            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} job records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No job data found for the specified date range")

        return len(df) if not df.empty else 0

    @task
    def backup_log_table(date_range: Dict[str, str]):
        """备份log表数据"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        sql = """
        SELECT
            *
        FROM log
        WHERE dttm >= %s
        AND dttm < %s + INTERVAL '1 day'
        ORDER BY dttm
        """

        df = postgres_hook.get_pandas_df(
            sql,
            parameters=[date_range['start_date'], date_range['end_date']]
        )

        if not df.empty:
            s3_key = f"{DAG_CONFIG['s3_prefix']}/log/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/log_{date_range['backup_timestamp']}.parquet"

            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} log records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No log data found for the specified date range")

        return len(df) if not df.empty else 0

    @task
    def backup_task_instance_table(date_range: Dict[str, str]):
        """备份task_instance表数据"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        sql = """
        SELECT
            *
        FROM task_instance
        WHERE start_date >= %s
        AND start_date < %s + INTERVAL '1 day'
        ORDER BY start_date
        """

        df = postgres_hook.get_pandas_df(
            sql,
            parameters=[date_range['start_date'], date_range['end_date']]
        )

        if not df.empty:
            s3_key = f"{DAG_CONFIG['s3_prefix']}/task_instance/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/task_instance_{date_range['backup_timestamp']}.parquet"

            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} task_instance records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No task_instance data found for the specified date range")

        return len(df) if not df.empty else 0

    @task
    def backup_xcom_table(date_range: Dict[str, str]):
        """备份xcom表数据"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        sql = """
        SELECT
            *
        FROM xcom
        WHERE timestamp >= %s
        AND timestamp < %s + INTERVAL '1 day'
        ORDER BY timestamp
        """

        df = postgres_hook.get_pandas_df(
            sql,
            parameters=[date_range['start_date'], date_range['end_date']]
        )

        if not df.empty:
            s3_key = f"{DAG_CONFIG['s3_prefix']}/xcom/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/xcom_{date_range['backup_timestamp']}.parquet"

            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} xcom records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No xcom data found for the specified date range")

        return len(df) if not df.empty else 0

    @task
    def backup_variable_table(date_range: Dict[str, str]):
        """全量备份variable表数据（带版本）"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        sql = """
        SELECT
            *
        FROM variable
        ORDER BY key
        """

        df = postgres_hook.get_pandas_df(sql)

        if not df.empty:

            s3_key = f"{DAG_CONFIG['s3_prefix']}/variable/version={date_range['backup_timestamp']}/variable_{date_range['backup_timestamp']}.parquet"

            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} variable records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No variable data found")

        return len(df) if not df.empty else 0

    @task
    def backup_connection_table(date_range: Dict[str, str]):
        """全量备份connection表数据（带版本）"""
        postgres_hook = PostgresHook(postgres_conn_id=DAG_CONFIG["postgres_conn_id"])
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        sql = """
        SELECT
            conn_id,
            conn_type,
            description,
            host,
            schema,
            login,
            password,
            port,
            extra,
            is_encrypted,
            is_extra_encrypted
        FROM connection
        ORDER BY conn_id
        """

        df = postgres_hook.get_pandas_df(sql)

        if not df.empty:

            s3_key = f"{DAG_CONFIG['s3_prefix']}/connection/version={date_range['backup_timestamp']}/connection_{date_range['backup_timestamp']}.parquet"

            parquet_buffer = df.to_parquet(index=False)
            s3_hook.load_bytes(
                bytes_data=parquet_buffer,
                key=s3_key,
                bucket_name=DAG_CONFIG["s3_bucket"],
                replace=True
            )

            logging.info(f"Backed up {len(df)} connection records to s3://{DAG_CONFIG['s3_bucket']}/{s3_key}")
        else:
            logging.info("No connection data found")

        return len(df) if not df.empty else 0

    @task
    def generate_backup_summary(
        date_range: Dict[str, str],
        dag_run_count: int,
        job_count: int,
        log_count: int,
        task_instance_count: int,
        xcom_count: int,
        variable_count: int,
        connection_count: int
    ):
        """生成备份摘要报告"""
        s3_hook = S3Hook(aws_conn_id=DAG_CONFIG["s3_conn_id"])

        summary = {
            "backup_date": date_range['year_month'],
            "backup_timestamp": date_range['backup_timestamp'],
            "date_range": {
                "start_date": date_range['start_date'],
                "end_date": date_range['end_date']
            },
            "backup_counts": {
                "dag_run": dag_run_count,
                "job": job_count,
                "log": log_count,
                "task_instance": task_instance_count,
                "xcom": xcom_count,
                "variable": variable_count,
                "connection": connection_count
            },
            "total_records": sum([
                dag_run_count, job_count, log_count,
                task_instance_count, xcom_count, variable_count, connection_count
            ]),
            "s3_location": f"s3://{DAG_CONFIG['s3_bucket']}/{DAG_CONFIG['s3_prefix']}/",
            "backup_status": "completed"
        }

        # 保存摘要到S3
        summary_key = f"{DAG_CONFIG['s3_prefix']}/backup_summary/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/summary_{date_range['backup_timestamp']}.json"

        s3_hook.load_string(
            string_data=json.dumps(summary, indent=2, default=str),
            key=summary_key,
            bucket_name=DAG_CONFIG["s3_bucket"],
            replace=True
        )

        logging.info(f"Backup completed successfully!")
        logging.info(f"Summary: {json.dumps(summary, indent=2, default=str)}")

        return summary

    # 定义任务流程
    date_range = get_backup_date_range()

    # 并行执行所有备份任务
    dag_run_count = backup_dag_run_table(date_range)
    job_count = backup_job_table(date_range)
    log_count = backup_log_table(date_range)
    task_instance_count = backup_task_instance_table(date_range)
    xcom_count = backup_xcom_table(date_range)
    variable_count = backup_variable_table(date_range)
    connection_count = backup_connection_table(date_range)

    # 生成摘要报告
    backup_summary = generate_backup_summary(
        date_range,
        dag_run_count,
        job_count,
        log_count,
        task_instance_count,
        xcom_count,
        variable_count,
        connection_count
    )

    # 使用summary变量避免未使用警告
    _ = backup_summary

# 实例化DAG
dag_instance = monthly_data_backup()
