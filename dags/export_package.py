# -*- coding: utf-8 -*-

from custom_operators.pod_operator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
# from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import <PERSON>Hook
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException

import datetime
import logging

DAG_ID = "NUSCENES_LABEL_EXPORT_TASK"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = Variable.get("ENV", "test")
TEST = ENV in ("test", "dev")

default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    dagrun_timeout=datetime.timedelta(minutes=DAG_CONFIG.get("dagrun_timeout", 60*60*2)),
    tags=DAG_CONFIG["tags"],
    default_args=default_args
)
def nuscene_label_package_process():
    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        logging.info(input)

        run_id = kwargs["run_id"]
        task_id =  input.get("task_id")
        running_config = input.get("config_path")
        resource = DAG_CONFIG.get("resource")

        ti.xcom_push(key='running_config', value=running_config) # tos://bucket/path/input.json
        ti.xcom_push(key="run_id", value=run_id)
        ti.xcom_push(key="resource", value=resource)

        # 回调状态更新
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        http_body = {
            "task_type": "NUSCENES_LABEL_EXPORT_TASK",
            "task_id": task_id,
            "task_status": "Running"
        }
        url = DAG_CONFIG.get("status_hook_url", "/data/callbackTaskStatus")
        if status_cli.interface_post_request(url=url, body=http_body):
            logging.info(f"{status_hook_id} post request success")
        else:
            logging.error(f"{status_hook_id} post request failed")


    label_package_task = EqPodOperator(
        task_id="nuscenes_label_package_pod",
        name="{{ ti.xcom_pull(key='run_id') }}",
        namespace=DAG_CONFIG.get("namespace", "argo"),
        image=DAG_CONFIG.get("image"),
        image_pull_policy=DAG_CONFIG.get("image_pull_policy"),
        resource_json='{{ ti.xcom_pull(key="resource") }}',
        command="/opt/run.py --cp={{ ti.xcom_pull(key='running_config') }};" + \
                r' status=$?; echo "{\"status\": $status}" > /airflow/xcom/return.json; exit $status',
        do_xcom_push=True,
        mount_argo_pps_config=True,
        test_run=TEST,
        on_finish_action='keep_pod',
        tolerations=[{
            "key": "node",
            "operator": "Equal",
            "value": "gpu",
            "effect": "NoSchedule"
        }],
        retries=0,
        retry_delay=datetime.timedelta(seconds=300),
        execution_timeout=datetime.timedelta(hours=2)
     )

    @task(trigger_rule="all_done")
    def update_status(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        task_id =  input.get("task_id")
        status = ti.xcom_pull(task_ids="nuscenes_label_package_pod")
        if isinstance(status, dict) and status.get("status")==0:
            status = "Succeeded"
        else:
            status = "Failed"
        logging.info(f"{status}")
        status_hook_id = DAG_CONFIG.get("status_hook_id", "interface_status_hook") + f"_{ENV}"
        status_cli = StatusHook(status_hook_id)
        http_body = {
            "task_type": "NUSCENES_LABEL_EXPORT_TASK",
            "task_id": task_id,
            "task_status": status
        }
        url = DAG_CONFIG.get("status_hook_url", "/data/callbackTaskStatus")
        if status_cli.interface_post_request(url=url, body=http_body):
            logging.info(f"{status_hook_id} post request success")
        else:
            ti.xcom_push(key="error", value="update_status_failed")
            logging.error(f"{status_hook_id} post request failed")


    @task(task_id="failed_notify", trigger_rule="one_failed", retries=0)
    def _failed_notify(**kwargs):
        ti = kwargs["ti"]
        status = ti.xcom_pull(task_ids="nuscenes_label_package_pod")
        update_result = ti.xcom_pull(task_ids="error")
        failed_reason = []
        if not (isinstance(status, dict) and status.get("status")==0):
            failed_reason.append("pod_failed")
        if update_result == "failed":
            failed_reason.append(update_result)
        input = kwargs["params"]
        logging.info(f"email: {input.get('email')}, phone: {input.get('phone')}")
        logging.info(input)
        # 强制使 DAG 失败
        raise AirflowException("任务失败，已发送通知")


    failed_notify = _failed_notify()
    init() >> label_package_task  >>  update_status() >> failed_notify
    label_package_task >> failed_notify

nuscene_label_package_process()
