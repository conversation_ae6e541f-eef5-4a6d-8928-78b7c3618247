import datetime
from airflow.models.dag import DAG
from airflow.decorators import task
from airflow.operators.python import PythonOperator, ShortCircuitOperator
from airflow.models.variable import Variable
import logging
from hooks.postgresql_hook import PostgresHookEq
from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.prometheus_metrics import node_statistics

DAG_ID = "cluster_monitor"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
pg_conn = PostgresHookEq(postgres_conn_id=DAG_CONFIG.get("postgres_conn_id"))
feishu_conn = FeishuHook()

with DAG(
    dag_id=DAG_ID,
    schedule="*/5 * * * *",
    start_date=datetime.datetime(2022, 1, 1),
    dagrun_timeout=datetime.timedelta(minutes=60),
    tags=["cluster"],
    max_active_runs=DAG_CONFIG.get("max_active_runs", 1),
    catchup=False
) as dag:

    def create_insert_sql(
        status_this_time: dict,
        table_name: str=DAG_CONFIG.get("table_name")):
        insert_sql = f"""INSERT INTO {table_name}
        (node, cpu_requests, cpu_limits, memory_requests, memory_limits,
        cpu_usage, memory_usage, allocatable_cpu, allocatable_memory, allocatable_pod,
        allocatable_ephemeral_storage, capacity_cpu, capacity_memory, capacity_pod,
        capacity_ephemeral_storage, images_size, gpu_core_used, gpu_core_total, gpu_memory_used,
        gpu_memory_total, gpu_task_num)
        VALUES """
        for node, val in status_this_time.items():
            insert_sql += f"""('{node}', {val['cpu_requests']}, {val['cpu_limits']},
            {val['memory_requests']}, {val['memory_limits']}, {val['cpu_usage']},
            {val['memory_usage']}, {val['allocatable_cpu']}, {val['allocatable_memory']},
            {val['allocatable_pod']}, {val['allocatable_ephemeral_storage']},
            {val['capacity_cpu']}, {val['capacity_memory']}, {val['capacity_pod']},
            {val['capacity_ephemeral_storage']}, {val['images_size']}, {val['gpu_core_used']},
            {val['gpu_core_total']}, {val['gpu_memory_used']}, {val['gpu_memory_total']}, {val['gpu_task_num']}),"""
        insert_sql = f"{insert_sql[:-1]};"
        logging.info(insert_sql)
        return insert_sql

    def into_db():
        status_this_time = node_statistics()
        insert_sql = create_insert_sql(status_this_time)
        pg_conn.insert(insert_sql=insert_sql)

    run_this_last = PythonOperator(
        task_id="check_usage",
        python_callable=into_db,
    )

    def check_time():
        now = datetime.datetime.now()
        if 0 < now.minute < 10:
            return True
        else:
            return False

    do_report = ShortCircuitOperator(
        task_id="do_report",
        python_callable=check_time,
    )

    @task()
    def report_to_feishu():
        query_sql = """select
            avg(node_st.cpu_usage_rate) as avg_cpu_usage_rate,
            avg(node_st.memory_usage_rate) as avg_memory_usage_rate
        from
            (
            select
                (cs.cpu_usage / cs.capacity_cpu ) as cpu_usage_rate,
                (cs.memory_usage / cs.capacity_memory) as memory_usage_rate
            from
                eq_dms_test.dt_k8s_cluster_statistics cs
            where
                cs.create_time >= (NOW() at TIME zone 'UTC' - interval '1 hour')) as node_st;
        """
        result = pg_conn.get_records(query_sql)
        report_text = \
        f"过去一小时集群平均CPU使用率: {(result[0][0]*100):.2f}%,集群平均内存使用率: {(result[0][1]*100):.2f}%"
        query_sql = """select
            avg(node_st.cpu_usage_rate) as avg_cpu_usage_rate,
            avg(node_st.memory_usage_rate) as avg_memory_usage_rate
        from
            (
            select
                (cs.cpu_usage / cs.capacity_cpu ) as cpu_usage_rate,
                (cs.memory_usage / cs.capacity_memory) as memory_usage_rate
            from
                eq_dms_test.dt_k8s_cluster_statistics cs
            where
                cs.create_time >= (NOW() at TIME zone 'UTC' - interval '1 hour')
                and node in ('10.30.0.109', '10.30.0.187', '10.30.8.186')
            ) as node_st;
        """
        result = pg_conn.get_records(query_sql)
        report_text += \
        f"\n\n过去一小时内GPU节点平均CPU使用率: {(result[0][0]*100):.2f}%, 平均内存使用率: {(result[0][1]*100):.2f}%"
        query_sql = """select
            avg(node_st.cpu_usage_rate) as avg_cpu_usage_rate,
            avg(node_st.memory_usage_rate) as avg_memory_usage_rate
        from
            (
            select
                (cs.cpu_usage / cs.capacity_cpu ) as cpu_usage_rate,
                (cs.memory_usage / cs.capacity_memory) as memory_usage_rate
            from
                eq_dms_test.dt_k8s_cluster_statistics cs
            where
                cs.create_time >= (NOW() at TIME zone 'UTC' - interval '1 hour')
                and node in ('10.30.36.193', '10.30.42.14', '10.30.44.192')
            ) as node_st;
        """
        result = pg_conn.get_records(query_sql)
        report_text += \
        f"\n\n过去一小时内CPU节点平均CPU使用率: {(result[0][0]*100):.2f}%, 平均内存使用率: {(result[0][1]*100):.2f}%"
        logging.info(report_text)
        feishu_conn.send_group_msg(msg=report_text)


run_this_last >> do_report >> report_to_feishu()
