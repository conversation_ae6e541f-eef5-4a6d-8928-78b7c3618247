#!/usr/bin/env python3
"""
测试monthly_data_backup DAG的语法和基本功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加dags目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dag_import():
    """测试DAG导入"""
    try:
        from monthly_data_backup import dag_instance
        print("✅ DAG导入成功")
        print(f"DAG ID: {dag_instance.dag_id}")
        print(f"调度间隔: {dag_instance.schedule_interval}")
        print(f"开始日期: {dag_instance.start_date}")
        print(f"标签: {dag_instance.tags}")
        return True
    except Exception as e:
        print(f"❌ DAG导入失败: {e}")
        return False

def test_dag_structure():
    """测试DAG结构"""
    try:
        from monthly_data_backup import dag_instance
        
        # 检查任务数量
        tasks = dag_instance.tasks
        print(f"\n📋 DAG任务列表 ({len(tasks)}个任务):")
        
        expected_tasks = [
            'get_backup_date_range',
            'backup_dag_run_table',
            'backup_job_table', 
            'backup_log_table',
            'backup_task_instance_table',
            'backup_xcom_table',
            'backup_variable_table',
            'backup_connection_table',
            'generate_backup_summary'
        ]
        
        actual_tasks = [task.task_id for task in tasks]
        
        for expected_task in expected_tasks:
            if expected_task in actual_tasks:
                print(f"  ✅ {expected_task}")
            else:
                print(f"  ❌ {expected_task} (缺失)")
        
        # 检查额外的任务
        extra_tasks = set(actual_tasks) - set(expected_tasks)
        if extra_tasks:
            print(f"\n🔍 额外任务: {extra_tasks}")
        
        return len(actual_tasks) >= len(expected_tasks)
        
    except Exception as e:
        print(f"❌ DAG结构检查失败: {e}")
        return False

def test_dag_configuration():
    """测试DAG配置"""
    try:
        from monthly_data_backup import DAG_CONFIG, DAG_ID
        
        print(f"\n⚙️ DAG配置:")
        print(f"  DAG ID: {DAG_ID}")
        print(f"  配置: {DAG_CONFIG}")
        
        # 检查必需的配置项
        required_configs = [
            'postgres_conn_id',
            's3_conn_id', 
            's3_bucket',
            's3_prefix'
        ]
        
        for config in required_configs:
            if config in DAG_CONFIG:
                print(f"  ✅ {config}: {DAG_CONFIG[config]}")
            else:
                print(f"  ❌ {config}: 缺失")
        
        return all(config in DAG_CONFIG for config in required_configs)
        
    except Exception as e:
        print(f"❌ DAG配置检查失败: {e}")
        return False

def test_date_calculation():
    """测试日期计算逻辑"""
    try:
        # 模拟执行日期
        execution_date = datetime(2025, 2, 1)  # 2月1日执行
        
        # 计算上个月的日期范围
        first_day_current_month = execution_date.replace(day=1)
        last_day_previous_month = first_day_current_month - timedelta(days=1)
        first_day_previous_month = last_day_previous_month.replace(day=1)
        
        date_range = {
            'start_date': first_day_previous_month.strftime('%Y-%m-%d'),
            'end_date': last_day_previous_month.strftime('%Y-%m-%d'),
            'year_month': first_day_previous_month.strftime('%Y-%m'),
            'backup_timestamp': execution_date.strftime('%Y%m%d_%H%M%S')
        }
        
        print(f"\n📅 日期计算测试:")
        print(f"  执行日期: {execution_date}")
        print(f"  备份日期范围: {date_range['start_date']} 到 {date_range['end_date']}")
        print(f"  年月: {date_range['year_month']}")
        print(f"  备份时间戳: {date_range['backup_timestamp']}")
        
        # 验证日期逻辑
        assert date_range['start_date'] == '2025-01-01'
        assert date_range['end_date'] == '2025-01-31'
        assert date_range['year_month'] == '2025-01'
        
        print("  ✅ 日期计算正确")
        return True
        
    except Exception as e:
        print(f"❌ 日期计算测试失败: {e}")
        return False

def test_s3_path_generation():
    """测试S3路径生成"""
    try:
        from monthly_data_backup import DAG_CONFIG
        
        date_range = {
            'year_month': '2025-01',
            'backup_timestamp': '20250201_020000'
        }
        
        # 测试各种S3路径
        test_paths = {
            'dag_run': f"{DAG_CONFIG['s3_prefix']}/dag_run/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/dag_run_{date_range['backup_timestamp']}.parquet",
            'variable': f"{DAG_CONFIG['s3_prefix']}/variable/version={date_range['backup_timestamp']}/variable_{date_range['backup_timestamp']}.parquet",
            'summary': f"{DAG_CONFIG['s3_prefix']}/backup_summary/year={date_range['year_month'][:4]}/month={date_range['year_month'][5:]}/summary_{date_range['backup_timestamp']}.json"
        }
        
        print(f"\n🗂️ S3路径生成测试:")
        for path_type, path in test_paths.items():
            print(f"  {path_type}: {path}")
        
        # 验证路径格式
        assert 'year=2025' in test_paths['dag_run']
        assert 'month=01' in test_paths['dag_run']
        assert 'version=20250201_020000' in test_paths['variable']
        
        print("  ✅ S3路径生成正确")
        return True
        
    except Exception as e:
        print(f"❌ S3路径生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Monthly Data Backup DAG 测试")
    print("=" * 50)
    
    tests = [
        ("DAG导入", test_dag_import),
        ("DAG结构", test_dag_structure),
        ("DAG配置", test_dag_configuration),
        ("日期计算", test_date_calculation),
        ("S3路径生成", test_s3_path_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过! DAG准备就绪。")
        return True
    else:
        print("⚠️ 有测试失败，请检查DAG配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
