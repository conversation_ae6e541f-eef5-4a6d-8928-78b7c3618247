from custom_operators.pod_operator import MultiContainerPodOperator
from hooks.feishu_hook import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hooks.status_hook import <PERSON><PERSON>ook
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.exceptions import AirflowException
import datetime, logging, json

DAG_ID = "LOG_SIM"
DAG_CONFIG = Variable.get(DAG_ID, deserialize_json=True)
# 指定后端环境
ENV = Variable.get("ENV", "dev")
TEST = ENV in ("test", "dev")

default_args = {
    'retries': 1
}

@dag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime.datetime(2025, 1, 1),
    catchup=False,
    max_active_runs=DAG_CONFIG.get("max_active_runs", 5),
    tags=DAG_CONFIG["tags"],
    default_args=default_args
)
def log_sim():

    @task(task_id="init")
    def init(**kwargs):
        ti = kwargs["ti"]
        input = kwargs["params"]
        run_id = kwargs["run_id"]
        _, info_id, detail_id, _ = run_id.split(".")
        task_type = input.get("taskType").lower().replace("_", "-")
        ti.xcom_push(key="pod_name", value=f"{task_type}-{info_id}-{detail_id}")
        logging.info(input)
        ti.xcom_push(key="context_json", value=json.dumps(input))
        status_hook_id = DAG_CONFIG.get("status_hook_id") + f"_{ENV}"
        status_hook = StatusHook(status_hook_id)
        status_hook.update_task_status(info_id, detail_id, "Running", "", "")


    sim_pod = MultiContainerPodOperator(
        task_id="sim_pod",
        name="{{ ti.xcom_pull(key='pod_name') }}",
        namespace="simulation",
        base_container_name="sim-server",
        context_json="{{ ti.xcom_pull(key='context_json') }}",
        do_xcom_push=True
    )

    @task(task_id="update_status", trigger_rule="all_done")
    def update_status(**kwargs):
        ti = kwargs["ti"]
        run_id = kwargs["run_id"]
        _, info_id, detail_id, _ = run_id.split(".")
        input = kwargs["params"]
        logging.info(input)
        status = ti.xcom_pull(task_ids="sim_pod")
        if isinstance(status, dict) and status.get("retry_status")==False:
            status = "Succeeded"
        else:
            status = "Failed"
        logging.info(f"{status}")
        status_hook_id = DAG_CONFIG.get("status_hook_id") + f"_{ENV}"
        status_hook = StatusHook(status_hook_id)
        status_hook.update_task_status(info_id, detail_id, status, "", "")

    init() >> sim_pod >> update_status()

log_sim()
