FROM ghcr.io/apache/airflow/v2-10-test/prod/python3.12:latest

RUN pip install apache-airflow-providers-cncf-kubernetes==10.4.0 \
    pydantic==2.10.6 \
    pycryptodome==3.22.0 \
    boto3==1.24.63 \
    tos==2.8.3

USER root
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    vim \
    less \
    curl \
    wget \
    jq \
    git \
    unzip \
    zip \
    procps \
    && \
    rm -rf /var/lib/apt/lists/*

COPY kubectl /usr/local/bin/kubectl
RUN chmod +x /usr/local/bin/kubectl
USER airflow
