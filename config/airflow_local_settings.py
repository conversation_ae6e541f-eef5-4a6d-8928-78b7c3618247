from kubernetes.client import models as k8s
from airflow.providers.cncf.kubernetes.utils.xcom_sidecar import PodDefaults

PodDefaults.SIDECAR_CONTAINER = k8s.V1Container(
    name=PodDefaults.SIDECAR_CONTAINER_NAME,
    command=['sh', '-c', PodDefaults.XCOM_CMD],
    image='registry.eqfleetcmder.com/eq/airflow-sidecar-alpine:latest',
    volume_mounts=[PodDefaults.VOLUME_MOUNT],
    resources=k8s.V1ResourceRequirements(
        requests={
            "cpu": "10m",
            "memory":"32Mi"
        },
        limits={
            "cpu": "0.25",
            "memory":"32Mi"
        }
    ),
    image_pull_policy='IfNotPresent'
)
