apiVersion: v1
kind: Service
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"addonmanager.kubernetes.io/mode":"Reconcile","kubernetes.io/cluster-service":"true","kubernetes.io/name":"Prometheus"},"name":"prometheus","namespace":"kube-system"},"spec":{"ports":[{"name":"http","port":9090,"protocol":"TCP","targetPort":9090},{"name":"thanos-sidecar","port":19090,"protocol":"TCP","targetPort":19090}],"selector":{"app":"prometheus"},"type":"NodePort"}}
  creationTimestamp: "2025-03-21T07:29:40Z"
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    kubernetes.io/cluster-service: "true"
    kubernetes.io/name: Prometheus
  name: prometheus
  namespace: kube-system
  resourceVersion: "449645871"
  uid: bd12f525-335c-45f3-b12d-95038030c97b
spec:
  clusterIP: **************
  clusterIPs:
  - **************
  externalTrafficPolicy: Cluster
  ports:
  - name: http
    nodePort: 32548
    port: 9090
    protocol: TCP
    targetPort: 9090
  - name: thanos-sidecar
    nodePort: 31204
    port: 19090
    protocol: TCP
    targetPort: 19090
  selector:
    app: prometheus
  sessionAffinity: None
  type: NodePort
status:
  loadBalancer: {}
