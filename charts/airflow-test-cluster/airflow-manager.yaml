apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-manager
  namespace: airflow
  labels:
    app: airflow_manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow_manager
  template:
    metadata:
      name: airflow-manager
      labels:
        app: airflow_manager
    spec:
      securityContext:
        runAsUser: 0
      containers:
      - name: base
        image: registry.eqfleetcmder.com/eq-sim/airflow:larger
        imagePullPolicy: Always
        command:
          - sh
          - -c
          - sleep inf;
        volumeMounts:
          - name: pvc-tos
            mountPath: /opt/airflow
          # - name: pvc-tos
          #   mountPath: /usr/lib/ssl/openssl.cnf
          #   subPath: configs-sync/openssl.cnf
          # - name: pvc-tos
          #   mountPath: /root/.kube/config
          #   subPath: config/kube-config
        env:
          - name: CELERY_QUEUE
            value: deafult
          - name: AIRFLOW__CORE__DAGS_FOLDER
            value: /opt/airflow/dags
          - name: AIRFLOW__CORE__PLUGINS_FOLDER
            value: /opt/airflow/plugins
          - name: AIRFLOW__CORE__DEFAULT_TIMEZONE
            value: "cst"
          - name: TZ
            value: "Asia/Shanghai"
          - name: AIRFLOW__CELERY__OPERATION_TIMEOUT
            value: "8.0"
      - name: airflow-manager
        image: registry.eqfleetcmder.com/eq/airflow:2.10.6
        imagePullPolicy: Always
        command:
          - sh
          - -c
          - sleep inf;
        volumeMounts:
          - name: pvc-tos
            mountPath: /opt/airflow
          # - name: pvc-tos
          #   mountPath: /usr/lib/ssl/openssl.cnf
          #   subPath: configs-sync/openssl.cnf
          # - name: pvc-tos
          #   mountPath: /root/.kube/config
          #   subPath: config/kube-config
        env:
          - name: CELERY_QUEUE
            value: deafult
          - name: AIRFLOW__CORE__DAGS_FOLDER
            value: /opt/airflow/dags
          - name: AIRFLOW__CORE__PLUGINS_FOLDER
            value: /opt/airflow/plugins
          - name: AIRFLOW__CORE__DEFAULT_TIMEZONE
            value: "cst"
          - name: TZ
            value: "Asia/Shanghai"
          - name: AIRFLOW__CELERY__OPERATION_TIMEOUT
            value: "8.0"
      restartPolicy: Always
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
        - name: pvc-tos
          persistentVolumeClaim:
            claimName: airflow-pvc
