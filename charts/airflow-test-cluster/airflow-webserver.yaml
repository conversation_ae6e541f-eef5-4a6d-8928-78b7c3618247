apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-webserver
  namespace: airflow
  labels:
    app: airflow
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1        # 更新时最多不可用 Pod 数
      maxSurge: 1              # 更新时最多超出的 Pod 数
  selector:
    matchLabels:
      app: airflow_webserver
  template:
    metadata:
      name: airflow-webserver
      labels:
        app: airflow_webserver
    spec:
      containers:
        - name: airflow-webserver
          image: registry.eqfleetcmder.com/eq/airflow:2.10.6
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          livenessProbe:
            initialDelaySeconds: 60
            timeoutSeconds: 20
            failureThreshold: 5
            periodSeconds: 60
            exec:
              command:
                - /bin/bash
                - -c
                - curl -f http://localhost:8080/health
          args: ["webserver"]
          volumeMounts:
            - name: pvc-tos
              mountPath: /opt/airflow/dags
              subPath: dags
            - name: pvc-tos
              mountPath: /opt/airflow/plugins
              subPath: plugins
            - name: pvc-tos
              mountPath: /opt/airflow/logs
              subPath: logs
            - name: pvc-tos
              mountPath: /opt/airflow/config
              subPath: config
            - name: pvc-tos
              mountPath: /opt/airflow/.env
              subPath: .env
            - name: pvc-tos
              mountPath: /opt/airflow/airflow.cfg
              subPath: airflow.cfg
            - name: pvc-tos
              mountPath: /home/<USER>/.kube/config
              subPath: config/kube.cfg
          resources:
            limits:
              cpu: 8
              memory: "16Gi"
            requests:
              cpu: "500m"
              memory: "2Gi"
          env:
            - name: CELERY_QUEUE
              value: deafult
            - name: AIRFLOW__CORE__DAGS_FOLDER
              value: /opt/airflow/dags
            - name: AIRFLOW__CORE__PLUGINS_FOLDER
              value: /opt/airflow/plugins
            - name: AIRFLOW__CORE__DEFAULT_TIMEZONE
              value: "cst"
            - name: TZ
              value: "Asia/Shanghai"
            - name: AIRFLOW__CELERY__OPERATION_TIMEOUT
              value: "8.0"
      restartPolicy: Always
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
        - name: pvc-tos
          persistentVolumeClaim:
            claimName: airflow-pvc

---

apiVersion: v1
kind: Service
metadata:
  name: airflow-webserver
  namespace: airflow
spec:
  selector:
    app: airflow_webserver
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
      nodePort: 31113  # 指定 NodePort，范围通常是 30000-32767
  type: NodePort

