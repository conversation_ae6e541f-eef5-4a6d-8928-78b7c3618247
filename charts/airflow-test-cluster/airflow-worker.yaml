apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: airflow-worker
  namespace: airflow
  labels:
    app: airflow
spec:
  serviceName: airflow-worker
  replicas: 4
  selector:
    matchLabels:
      app: airflow_worker
  template:
    metadata:
      name: airflow-worker
      labels:
        app: airflow_worker
    spec:
      containers:
        - name: airflow-worker
          image: registry.eqfleetcmder.com/eq/airflow:2.10.6
          imagePullPolicy: Always
          ports:
            - containerPort: 8793
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 60
            timeoutSeconds: 20
            failureThreshold: 5
            exec:
              command:
                - sh
                - -c
                - CONNECTION_CHECK_MAX_COUNT=0 exec /entrypoint python -m celery --app airflow.providers.celery.executors.celery_executor.app inspect ping -d celery@$(hostname)
          args: ["airflow", "celery", "worker"] #, "-q", "$(CELERY_QUEUE)"]
          volumeMounts:
            - name: pvc-tos
              mountPath: /opt/airflow/dags
              subPath: dags
            - name: pvc-tos
              mountPath: /opt/airflow/plugins
              subPath: plugins
            - name: pvc-tos
              mountPath: /opt/airflow/logs
              subPath: logs
            - name: pvc-tos
              mountPath: /opt/airflow/config
              subPath: config
            - name: pvc-tos
              mountPath: /opt/airflow/.env
              subPath: .env
            - name: pvc-tos
              mountPath: /opt/airflow/airflow.cfg
              subPath: airflow.cfg
            - name: pvc-tos
              mountPath: /home/<USER>/.kube/config
              subPath: config/kube.cfg
          resources:
            limits:
              memory: "16Gi"
              ephemeral-storage: 20Gi
            requests:
              memory: "8Gi"
              ephemeral-storage: 20Gi
          env:
            - name: CELERY_QUEUE
              value: deafult
            - name: AIRFLOW__CORE__DAGS_FOLDER
              value: /opt/airflow/dags
            - name: AIRFLOW__CORE__PLUGINS_FOLDER
              value: /opt/airflow/plugins
            - name: AIRFLOW__CORE__DEFAULT_TIMEZONE
              value: "cst"
            - name: KUBE_CONFIG_ENV_VAR
              value: /home/<USER>/.kube/config
            - name: TZ
              value: "Asia/Shanghai"
            - name: AIRFLOW__CELERY__OPERATION_TIMEOUT
              value: "8.0"
      restartPolicy: Always
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
        - name: pvc-tos
          persistentVolumeClaim:
            claimName: airflow-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: airflow-worker  # 服务名称
  namespace: airflow    # 命名空间
spec:
  selector:
    app: airflow_worker  # 必须与 StatefulSet 的标签匹配
  ports:
    - name: worker-port  # 端口名称
      protocol: TCP
      port: 8793         # Service 暴露的端口
      targetPort: 8793   # Pod 上的目标端口
  clusterIP: None        # 设置为 None 创建 Headless Service
