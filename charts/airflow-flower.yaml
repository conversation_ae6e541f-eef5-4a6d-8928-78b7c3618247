apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-flower
  namespace: airflow
  labels:
    app: airflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow_flower
  template:
    metadata:
      name: airflow-flower
      labels:
        app: airflow_flower
    spec:
      containers:
        - name: airflow-flower
          image: registry.eqfleetcmder.com/eq/airflow:2.10.6
          imagePullPolicy: Always
          ports:
            - containerPort: 5555
          args: ["celery", "flower"]
          volumeMounts:
            - name: pvc-tos
              mountPath: /opt/airflow/dags
              subPath: dags
            - name: pvc-tos
              mountPath: /opt/airflow/plugins
              subPath: plugins
            - name: pvc-tos
              mountPath: /opt/airflow/logs
              subPath: logs
            - name: pvc-tos
              mountPath: /opt/airflow/config
              subPath: config
            - name: pvc-tos
              mountPath: /opt/airflow/.env
              subPath: .env
            - name: pvc-tos
              mountPath: /opt/airflow/airflow.cfg
              subPath: airflow.cfg
            - name: pvc-tos
              mountPath: /home/<USER>/.kube/config
              subPath: config/kube.cfg
          resources:
            requests:
              cpu: 0.01
              memory: "256Mi"
            limits:
              cpu: 1
              memory: "512Mi"
          env:
            - name: CELERY_QUEUE
              value: deafult
            - name: AIRFLOW__CORE__DAGS_FOLDER
              value: /opt/airflow/dags
            - name: AIRFLOW__CORE__PLUGINS_FOLDER
              value: /opt/airflow/plugins
            - name: AIRFLOW__CORE__DEFAULT_TIMEZONE
              value: "cst"
            - name: TZ
              value: "Asia/Shanghai"
            - name: AIRFLOW__CELERY__OPERATION_TIMEOUT
              value: "8.0"
      restartPolicy: Always
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
        - name: pvc-tos
          persistentVolumeClaim:
            claimName: airflow-pvc

---

apiVersion: v1
kind: Service
metadata:
  name: airflow-flower
  namespace: airflow
spec:
  selector:
    app: airflow_flower
  ports:
    - protocol: TCP
      port: 5555
      targetPort: 5555
      nodePort: 31114  # 指定 NodePort，范围通常是 30000-32767
  type: NodePort

