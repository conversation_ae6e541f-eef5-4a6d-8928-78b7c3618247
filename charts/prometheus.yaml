apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"apps/v1","kind":"StatefulSet","metadata":{"annotations":{},"labels":{"addonmanager.kubernetes.io/mode":"Reconcile","app":"prometheus","kubernetes.io/cluster-service":"true"},"name":"prometheus","namespace":"kube-system"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"prometheus"}},"serviceName":"prometheus","template":{"metadata":{"annotations":{"prometheus.io/path":"/metrics","prometheus.io/port":"9090","prometheus.io/scrape":"true"},"labels":{"app":"prometheus","config-version":"2"}},"spec":{"containers":[{"args":["--volume-dir=/etc/config","--webhook-url=http://localhost:9090/-/reload"],"image":"eqr-cn-beijing.cr.volces.com/eq/configmap-reload:v0.1","imagePullPolicy":"IfNotPresent","name":"prometheus-server-configmap-reload","resources":{"limits":{"cpu":"100m","memory":"100Mi"},"requests":{"cpu":"50m","memory":"50Mi"}},"volumeMounts":[{"mountPath":"/etc/config","name":"config-volume","readOnly":true},{"mountPath":"/data","name":"promdata"}]},{"args":["--config.file=/etc/config/prometheus.yml","--storage.tsdb.path=/data","--storage.tsdb.retention.time=30m","--storage.tsdb.min-block-duration=2h","--storage.tsdb.max-block-duration=2h","--web.enable-lifecycle"],"image":"eqr-cn-beijing.cr.volces.com/eq/prometheus:v2.14.0","imagePullPolicy":"IfNotPresent","name":"prometheus-server","ports":[{"containerPort":9090}],"resources":{"limits":{"cpu":"2000m","memory":"5000Mi"},"requests":{"cpu":"200m","memory":"1000Mi"}},"volumeMounts":[{"mountPath":"/etc/config","name":"config-volume","readOnly":true},{"mountPath":"/data","name":"promdata"}]},{"args":["sidecar","--tsdb.path=/data","--prometheus.url=http://localhost:9090","--http-address=0.0.0.0:19191","--grpc-address=0.0.0.0:19090","--objstore.config=type: s3\nconfig:\n  endpoint: tos-s3-cn-beijing.volces.com\n  bucket: eq-thanos\n  access_key: AKLTY2RkZWM0MjkwYWI0NDM4NmFjMTNlODNmYTE2M2Y3MzQ\n  secret_key: TmpOa01EZzRaVEV3TjJaaU5EWTVNbUUzWkRsaVlUY3hOalV3Tm1ZME5XSQ==\n  bucket_lookup_type: virtual-hosted\n"],"image":"eqr-cn-beijing.cr.volces.com/eq/thanos:v0.34.1","imagePullPolicy":"IfNotPresent","name":"thanos-sidecar","resources":{"limits":{"cpu":"1000m","memory":"1000Mi"},"requests":{"cpu":"200m","memory":"1000Mi"}},"securityContext":{"runAsUser":65534},"volumeMounts":[{"mountPath":"/data","name":"promdata"}]}],"imagePullSecrets":[{"name":"docker-registry-secret"}],"initContainers":[{"command":["chown","-R","65534:65534","/data"],"image":"eqr-cn-beijing.cr.volces.com/eq/busybox:latest","imagePullPolicy":"IfNotPresent","name":"init-file","resources":{"limits":{"cpu":"1000m","memory":"1Gi"},"requests":{"cpu":"250m","memory":"0.5Gi"}},"securityContext":{"runAsUser":0},"volumeMounts":[{"mountPath":"/data","name":"promdata"}]}],"priorityClassName":"system-cluster-critical","serviceAccountName":"prometheus","terminationGracePeriodSeconds":300,"tolerations":[{"effect":"NoSchedule","key":"nodetype","operator":"Exists"}],"volumes":[{"configMap":{"name":"prometheus-config"},"name":"config-volume"},{"hostPath":{"path":"/mnt/data/appcache/prometheus","type":"DirectoryOrCreate"},"name":"promdata"}]}}}}
  creationTimestamp: "2025-03-21T07:28:52Z"
  generation: 1
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: prometheus
    kubernetes.io/cluster-service: "true"
  name: prometheus
  namespace: kube-system
  resourceVersion: "*********"
  uid: a21904dc-9ca8-41f8-b059-2fa62a30892a
spec:
  podManagementPolicy: OrderedReady
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: prometheus
  serviceName: prometheus
  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "9090"
        prometheus.io/scrape: "true"
      labels:
        app: prometheus
        config-version: "2"
    spec:
      containers:
      - args:
        - --volume-dir=/etc/config
        - --webhook-url=http://localhost:9090/-/reload
        image: eqr-cn-beijing.cr.volces.com/eq/configmap-reload:v0.1
        imagePullPolicy: IfNotPresent
        name: prometheus-server-configmap-reload
        resources:
          limits:
            cpu: 100m
            memory: 100Mi
          requests:
            cpu: 50m
            memory: 50Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config
          name: config-volume
          readOnly: true
        - mountPath: /data
          name: promdata
      - args:
        - --config.file=/etc/config/prometheus.yml
        - --storage.tsdb.path=/data
        - --storage.tsdb.retention.time=30m
        - --storage.tsdb.min-block-duration=2h
        - --storage.tsdb.max-block-duration=2h
        - --web.enable-lifecycle
        image: eqr-cn-beijing.cr.volces.com/eq/prometheus:v2.14.0
        imagePullPolicy: IfNotPresent
        name: prometheus-server
        ports:
        - containerPort: 9090
          protocol: TCP
        resources:
          limits:
            cpu: "2"
            memory: 5000Mi
          requests:
            cpu: 200m
            memory: 1000Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config
          name: config-volume
          readOnly: true
        - mountPath: /data
          name: promdata
      - args:
        - sidecar
        - --tsdb.path=/data
        - --prometheus.url=http://localhost:9090
        - --http-address=0.0.0.0:19191
        - --grpc-address=0.0.0.0:19090
        - |
          --objstore.config=type: s3
          config:
            endpoint: tos-s3-cn-beijing.volces.com
            bucket: eq-thanos
            access_key: AKLTY2RkZWM0MjkwYWI0NDM4NmFjMTNlODNmYTE2M2Y3MzQ
            secret_key: TmpOa01EZzRaVEV3TjJaaU5EWTVNbUUzWkRsaVlUY3hOalV3Tm1ZME5XSQ==
            bucket_lookup_type: virtual-hosted
        image: eqr-cn-beijing.cr.volces.com/eq/thanos:v0.34.1
        imagePullPolicy: IfNotPresent
        name: thanos-sidecar
        resources:
          limits:
            cpu: "1"
            memory: 1000Mi
          requests:
            cpu: 200m
            memory: 1000Mi
        securityContext:
          runAsUser: 65534
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data
          name: promdata
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: docker-registry-secret
      initContainers:
      - command:
        - chown
        - -R
        - 65534:65534
        - /data
        image: eqr-cn-beijing.cr.volces.com/eq/busybox:latest
        imagePullPolicy: IfNotPresent
        name: init-file
        resources:
          limits:
            cpu: "1"
            memory: 1Gi
          requests:
            cpu: 250m
            memory: 512Mi
        securityContext:
          runAsUser: 0
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data
          name: promdata
      priorityClassName: system-cluster-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: prometheus
      serviceAccountName: prometheus
      terminationGracePeriodSeconds: 300
      tolerations:
      - effect: NoSchedule
        key: nodetype
        operator: Exists
      volumes:
      - configMap:
          defaultMode: 420
          name: prometheus-config
        name: config-volume
      - hostPath:
          path: /mnt/data/appcache/prometheus
          type: DirectoryOrCreate
        name: promdata
  updateStrategy:
    rollingUpdate:
      partition: 0
    type: RollingUpdate


