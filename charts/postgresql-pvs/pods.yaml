apiVersion: v1
kind: Pod
metadata:
  name: n1
  namespace: airflow
spec:
  securityContext:
    runAsUser: 0
  containers:
    - command:
        - sh
        - -c
        - sleep inf
      image: registry.eqfleetcmder.com/eq/airflow:busybox
      imagePullPolicy: Always
      name: base
      volumeMounts:
      - name: data-gen
        mountPath: /data
  imagePullSecrets:
    - name: docker-registry-secret
  volumes:
    - name: data-gen
      persistentVolumeClaim:
        claimName: node1-pvc
  tolerations:
    - key: node
      operator: Equal
      value: gpu
      effect: NoSchedule

# ---

# apiVersion: v1
# kind: Pod
# metadata:
#   name: n2
#   namespace: airflow
# spec:
#   securityContext:
#     runAsUser: 0
#   containers:
#     - command:
#         - sh
#         - -c
#         - sleep inf
#       image: registry.eqfleetcmder.com/eq/airflow:busybox
#       imagePullPolicy: Always
#       name: base
#       volumeMounts:
#       - name: data-gen
#         mountPath: /data
#   imagePullSecrets:
#     - name: docker-registry-secret
#   volumes:
#     - name: data-gen
#       persistentVolumeClaim:
#         claimName: node2-pvc
#   tolerations:
#     - key: node
#       operator: Equal
#       value: gpu
#       effect: NoSchedule

# ---

# apiVersion: v1
# kind: Pod
# metadata:
#   name: n3
#   namespace: airflow
# spec:
#   securityContext:
#     runAsUser: 0
#   containers:
#     - command:
#         - sh
#         - -c
#         - sleep inf
#       image: registry.eqfleetcmder.com/eq/airflow:busybox
#       imagePullPolicy: Always
#       name: base
#       volumeMounts:
#       - name: data-gen
#         mountPath: /data
#   imagePullSecrets:
#     - name: docker-registry-secret
#   volumes:
#     - name: data-gen
#       persistentVolumeClaim:
#         claimName: node3-pvc
#   tolerations:
#     - key: node
#       operator: Equal
#       value: gpu
#       effect: NoSchedule
