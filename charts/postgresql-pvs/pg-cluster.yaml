apiVersion: "acid.zalan.do/v1"  
kind: postgresql  
metadata:  
  name: local-storage-cluster
  namespace: airflow
spec:
  teamId: "acid"  
  numberOfInstances: 3  
  volume:  
    size: 30Gi  
    storageClass: "local-storage"  
    selector:  
      matchLabels:  
        storage-type: local-ssd  
        node-type: database  
  nodeAffinity:  
    requiredDuringSchedulingIgnoredDuringExecution:  
      nodeSelectorTerms:  
      - matchExpressions:  
        - key: storage-type  
          operator: In  
          values:  
          - local-ssd  
  postgresql:  
    version: "13"  
  users:  
    postgres:  
    - superuser  
    - createdb  
  databases:  
    myapp: postgres