apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: node1-pvc  # PVC 名称。
  namespace: airflow
spec:
  storageClassName: local-storage
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi  # 声明的存储使用量。
  volumeMode: Filesystem # 挂载对象存储的格式，本示例填写 Filesystem，表示文件系统挂载。
  volumeName: local-pv-node1  # 绑定到该 PVC 的 PV 名称。


# ---
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: node2-pvc  # PVC 名称。
#   namespace: airflow
# spec:
#   storageClassName: local-storage
#   accessModes:
#   - ReadWriteOnce
#   resources:
#     requests:
#       storage: 30Gi  # 声明的存储使用量。
#   volumeMode: Filesystem # 挂载对象存储的格式，本示例填写 Filesystem，表示文件系统挂载。
#   volumeName: local-pv-node2  # 绑定到该 PVC 的 PV 名称。


# ---
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: node3-pvc  # PVC 名称。
#   namespace: airflow
# spec:
#   storageClassName: local-storage
#   accessModes:
#   - ReadWriteOnce
#   resources:
#     requests:
#       storage: 30Gi  # 声明的存储使用量。
#   volumeMode: Filesystem # 挂载对象存储的格式，本示例填写 Filesystem，表示文件系统挂载。
#   volumeName: local-pv-node3  # 绑定到该 PVC 的 PV 名称。
