# 节点1的PV
apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-pv-node1
  labels:
    node-type: database
    storage-type: local-ssd
spec:
  capacity:
    storage: 50Gi
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: /mnt
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - ***********  # 替换为实际节点名

# ---
# # 节点2的PV
# apiVersion: v1
# kind: PersistentVolume
# metadata:
#   name: local-pv-node2
#   labels:
#     node-type: database
#     storage-type: local-ssd
# spec:
#   capacity:
#     storage: 30Gi
#   accessModes:
#   - ReadWriteOnce
#   persistentVolumeReclaimPolicy: Retain
#   storageClassName: local-storage
#   local:
#     path: /mnt/pg-data
#   nodeAffinity:
#     required:
#       nodeSelectorTerms:
#       - matchExpressions:
#         - key: kubernetes.io/hostname
#           operator: In
#           values:
#           - ***********  # 替换为实际节点名

# ---
# # 节点3的PV
# apiVersion: v1
# kind: PersistentVolume
# metadata:
#   name: local-pv-node3
#   labels:
#     node-type: database
#     storage-type: local-ssd
# spec:
#   capacity:
#     storage: 30Gi
#   accessModes:
#   - ReadWriteOnce
#   persistentVolumeReclaimPolicy: Retain
#   storageClassName: local-storage
#   local:
#     path: /mnt/pg-data
#   nodeAffinity:
#     required:
#       nodeSelectorTerms:
#       - matchExpressions:
#         - key: kubernetes.io/hostname
#           operator: In
#           values:
#           - ***********  # 替换为实际节点名
