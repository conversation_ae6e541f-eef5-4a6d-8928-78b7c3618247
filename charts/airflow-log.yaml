apiVersion: v1
kind: ConfigMap
metadata:
  name: airflow-nginx-lua-scripts
  namespace: airflow
data:
  file_browser.lua: |
    local M = {}

    -- 安全路径检查函数
    local function is_safe_path(base_path, target_path)
        -- 规范化路径
        local function normalize_path(path)
            local parts = {}
            for part in string.gmatch(path, "[^/]+") do
                if part == ".." then
                    table.remove(parts)
                elseif part ~= "." and part ~= "" then
                    table.insert(parts, part)
                end
            end
            return "/" .. table.concat(parts, "/")
        end

        local norm_base = normalize_path(base_path)
        local norm_target = normalize_path(target_path)

        return string.sub(norm_target, 1, string.len(norm_base)) == norm_base
    end

    -- 获取文件大小的可读格式
    local function format_size(size)
        if size < 1024 then
            return size .. " B"
        elseif size < 1024 * 1024 then
            return string.format("%.1f KB", size / 1024)
        elseif size < 1024 * 1024 * 1024 then
            return string.format("%.1f MB", size / (1024 * 1024))
        else
            return string.format("%.1f GB", size / (1024 * 1024 * 1024))
        end
    end

    -- 获取文件修改时间
    local function format_time(timestamp)
        return os.date("%Y-%m-%d %H:%M:%S", timestamp)
    end

    -- 获取目录内容
    function M.get_directory_contents(dir_path, page, per_page)
        page = page or 1
        per_page = per_page or 50

        -- 安全检查
        if not is_safe_path("/opt/bitnami/openresty/nginx/html/browse", dir_path) then
            return nil, "Invalid path"
        end

        local handle = io.popen("find '" .. dir_path .. "' -maxdepth 1 -type f -o -type d | sort")
        if not handle then
            return nil, "Cannot access directory"
        end

        local items = {}
        local total_count = 0

        for line in handle:lines() do
            if line ~= dir_path then  -- 排除当前目录
                total_count = total_count + 1
                local name = string.match(line, "([^/]+)$")
                local attr = io.popen("stat -c '%s %Y %F' '" .. line .. "'"):read("*a")

                if attr then
                    local size, mtime, ftype = string.match(attr, "(%d+) (%d+) (.+)")
                    local is_dir = string.find(ftype, "directory") ~= nil

                    table.insert(items, {
                        name = name,
                        path = line,
                        size = tonumber(size) or 0,
                        mtime = tonumber(mtime) or 0,
                        is_directory = is_dir
                    })
                end
            end
        end
        handle:close()

        -- 分页
        local start_idx = (page - 1) * per_page + 1
        local end_idx = math.min(start_idx + per_page - 1, #items)
        local page_items = {}

        for i = start_idx, end_idx do
            if items[i] then
                table.insert(page_items, items[i])
            end
        end

        return {
            items = page_items,
            total = #items,
            page = page,
            per_page = per_page,
            total_pages = math.ceil(#items / per_page)
        }
    end

    -- 生成HTML页面
    function M.generate_html(data, current_path, base_url)
        local html = [[
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Airflow Log Browser</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .header { border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
            .breadcrumb { background: #e9ecef; padding: 10px; border-radius: 4px; margin-bottom: 20px; }
            .breadcrumb a { color: #007bff; text-decoration: none; }
            .breadcrumb a:hover { text-decoration: underline; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f8f9fa; font-weight: bold; }
            tr:hover { background-color: #f8f9fa; }
            .file-icon { width: 20px; margin-right: 8px; }
            .directory { color: #007bff; font-weight: bold; }
            .file { color: #333; }
            .pagination { margin-top: 20px; text-align: center; }
            .pagination a { display: inline-block; padding: 8px 12px; margin: 0 4px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            .pagination a:hover { background: #0056b3; }
            .pagination .current { background: #6c757d; }
            .stats { background: #e7f3ff; padding: 10px; border-radius: 4px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🗂️ Airflow Log Browser</h1>
            </div>

            <div class="breadcrumb">
                <strong>📍 Current Path:</strong> ]] .. current_path .. [[
            </div>

            <div class="stats">
                <strong>📊 Total Items:</strong> ]] .. data.total .. [[ |
                <strong>📄 Page:</strong> ]] .. data.page .. [[ of ]] .. data.total_pages .. [[ |
                <strong>📦 Items per page:</strong> ]] .. data.per_page .. [[
            </div>

            <table>
                <thead>
                    <tr>
                        <th>📁 Name</th>
                        <th>📏 Size</th>
                        <th>🕒 Modified</th>
                        <th>🔧 Actions</th>
                    </tr>
                </thead>
                <tbody>
        ]]

        -- 添加返回上级目录链接
        if current_path ~= "/opt/bitnami/openresty/nginx/html/browse" then
            local parent_path = string.match(current_path, "(.+)/[^/]+$") or "/opt/bitnami/openresty/nginx/html/browse"
            local relative_parent = string.gsub(parent_path, "/opt/bitnami/openresty/nginx/html/browse", "")
            if relative_parent == "" then relative_parent = "/" end

            html = html .. [[
                    <tr>
                        <td><a href="]] .. base_url .. relative_parent .. [[" class="directory">📁 ..</a></td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
            ]]
        end

        -- 添加文件和目录
        for _, item in ipairs(data.items) do
            local relative_path = string.gsub(item.path, "/opt/bitnami/openresty/nginx/html/browse", "")
            if relative_path == "" then relative_path = "/" end

            local icon = item.is_directory and "📁" or "📄"
            local class = item.is_directory and "directory" or "file"
            local size = item.is_directory and "-" or format_size(item.size)
            local mtime = format_time(item.mtime)

            html = html .. [[
                    <tr>
                        <td><a href="]] .. base_url .. relative_path .. [[" class="]] .. class .. [[">]] .. icon .. [[ ]] .. item.name .. [[</a></td>
                        <td>]] .. size .. [[</td>
                        <td>]] .. mtime .. [[</td>
                        <td>
            ]]

            if not item.is_directory then
                html = html .. [[<a href="]] .. base_url .. relative_path .. [[" download>⬇️ Download</a>]]
            end

            html = html .. [[
                        </td>
                    </tr>
            ]]
        end

        html = html .. [[
                </tbody>
            </table>
        ]]

        -- 分页导航
        if data.total_pages > 1 then
            html = html .. [[<div class="pagination">]]

            for i = 1, data.total_pages do
                local page_url = base_url .. current_path .. "?page=" .. i
                local class = (i == data.page) and "current" or ""
                html = html .. [[<a href="]] .. page_url .. [[" class="]] .. class .. [[">]] .. i .. [[</a>]]
            end

            html = html .. [[</div>]]
        end

        html = html .. [[
        </div>
    </body>
    </html>
        ]]

        return html
    end

    return M

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: airflow-nginx-logserver-conf
  namespace: airflow
data:
  default.conf: |
    server {
      listen 8080;

      # 启用 Lua 模块
      lua_package_path '/opt/bitnami/openresty/nginx/conf/conf.d/lib/?.lua;;';
      lua_shared_dict dir_cache 10m;

      location / {
        root /opt/bitnami/openresty/nginx/html;

        # 处理目录浏览请求
        location ~ ^/browse(/.+)? {
          default_type text/html;
          content_by_lua_block {
            local template = require "resty.template"
            local paginated_dir = require "lib.paginated_dir"

            -- 获取请求路径
            local path = ngx.var[1] or "/"
            local base_dir = "/opt/bitnami/openresty/nginx/html/browse"
            local full_path = base_dir .. path

            -- 获取分页参数
            local page = tonumber(ngx.var.arg_page) or 1
            local per_page = tonumber(ngx.var.arg_per_page) or 20

            -- 获取分页数据
            local data, err = paginated_dir.paginate(full_path, page, per_page)
            if not data then
              ngx.status = 404
              ngx.say("Error: " .. (err or "unknown error"))
              return
            end

            -- 父目录链接
            local parent_path = ""
            if path ~= "/" then
              parent_path = path:match("^(.*)/[^/]+/?$") or "/"
            end

            -- 渲染模板
            template.render([[
            <!DOCTYPE html>
            <html>
            <head>
              <title>浏览: {{path}}</title>
              <style>
                .file-list { margin: 20px 0; }
                .file-item { padding: 8px; border-bottom: 1px solid #eee; }
                .file-item:hover { background: #f5f5f5; }
                .dir { font-weight: bold; color: #2196F3; }
                .pagination { margin: 20px 0; }
                .pagination a, .pagination span {
                  display: inline-block;
                  padding: 5px 10px;
                  margin: 0 2px;
                  border: 1px solid #ddd;
                }
                .pagination a { text-decoration: none; }
                .pagination a:hover { background: #eee; }
                .current { background: #2196F3; color: white; }
                .breadcrumb { margin-bottom: 20px; }
              </style>
            </head>
            <body>
              <div class="container">
                <h1>浏览: {{path}}</h1>

                <div class="breadcrumb">
                  <a href="/browse/">根目录</a>
                  {% local parts = {} %}
                  {% for part in path:gmatch("[^/]+") do %}
                    {% table.insert(parts, part) %}
                    {% local partial = "/" .. table.concat(parts, "/") %}
                    &raquo; <a href="/browse{{partial}}">{{part}}</a>
                  {% end %}
                </div>

                {% if parent_path ~= "" then %}
                  <div class="parent-dir">
                    <a href="/browse{{parent_path}}">返回上级目录</a>
                  </div>
                {% end %}

                <div class="file-list">
                  {% for _, item in ipairs(items) do %}
                    <div class="file-item {% if item.is_dir then %}dir{% end %}">
                      {% if item.is_dir then %}
                        <a href="/browse{{path}}{{item.name}}">{{item.name}}</a>
                      {% else %}
                        {{item.name}}
                        <span style="float:right">
                          {{item.size}} bytes -
                          {{os.date("%Y-%m-%d %H:%M", item.mtime)}}
                        </span>
                      {% end %}
                    </div>
                  {% end %}
                </div>

                {% if pagination.total_pages > 1 then %}
                  <div class="pagination">
                    {% if pagination.page > 1 then %}
                      <a href="/browse{{path}}?page=1">&laquo; 首页</a>
                      <a href="/browse{{path}}?page={{pagination.page-1}}">上一页</a>
                    {% end %}

                    {% for p = 1, pagination.total_pages do %}
                      {% if p == pagination.page then %}
                        <span class="current">{{p}}</span>
                      {% else %}
                        <a href="/browse{{path}}?page={{p}}">{{p}}</a>
                      {% end %}
                    {% end %}

                    {% if pagination.page < pagination.total_pages then %}
                      <a href="/browse{{path}}?page={{pagination.page+1}}">下一页</a>
                      <a href="/browse{{path}}?page={{pagination.total_pages}}">末页 &raquo;</a>
                    {% end %}
                  </div>
                {% end %}
              </div>
            </body>
            </html>
            ]], {
              path = path,
              items = data.items,
              pagination = {
                page = data.page,
                per_page = data.per_page,
                total_pages = data.total_pages,
                total = data.total
              },
              parent_path = parent_path
            })
          }
        }

        # 处理静态文件请求
        try_files $uri $uri/ =404;
      }
    }

  # Lua 模块文件
  paginated_dir.lua: |
    local lfs = require "lfs"
    local resty_md5 = require "resty.md5"
    local str = require "resty.string"

    local _M = {}

    -- 安全路径检查
    local function is_safe_path(root, path)
        local normalized = ngx.re.gsub(path, "/\\.\\./", "/")
        return string.sub(normalized, 1, #root) == root
    end

    -- 获取目录内容（带缓存）
    local function get_dir_entries(dir_path)
        local md5 = resty_md5:new()
        md5:update(dir_path)
        local cache_key = "dir:" .. str.to_hex(md5:final())

        local cached = ngx.shared.dir_cache:get(cache_key)
        if cached then
            return cached
        end

        local entries = {dirs = {}, files = {}}

        for entry in lfs.dir(dir_path) do
            if entry ~= "." and entry ~= ".." then
                local full_path = dir_path .. "/" .. entry
                local attr = lfs.attributes(full_path)

                if attr.mode == "directory" then
                    table.insert(entries.dirs, {
                        name = entry,
                        path = full_path,
                        mtime = attr.modification,
                        size = attr.size
                    })
                else
                    table.insert(entries.files, {
                        name = entry,
                        path = full_path,
                        mtime = attr.modification,
                        size = attr.size
                    })
                end
            end
        end

        -- 按名称排序
        table.sort(entries.dirs, function(a, b) return a.name < b.name end)
        table.sort(entries.files, function(a, b) return a.name < b.name end)

        -- 缓存10分钟
        ngx.shared.dir_cache:set(cache_key, entries, 600)
        return entries
    end

    -- 分页函数
    function _M.paginate(dir_path, page, per_page)
        page = tonumber(page) or 1
        per_page = tonumber(per_page) or 20

        if not is_safe_path("/opt/bitnami/openresty/nginx/html/browse", dir_path) then
            return nil, "Invalid path"
        end

        local entries, err = get_dir_entries(dir_path)
        if not entries then
            return nil, err
        end

        local all_items = {}

        -- 先添加目录
        for _, dir in ipairs(entries.dirs) do
            table.insert(all_items, {
                name = dir.name .. "/",
                is_dir = true,
                mtime = dir.mtime,
                size = dir.size
            })
        end

        -- 再添加文件
        for _, file in ipairs(entries.files) do
            table.insert(all_items, {
                name = file.name,
                is_dir = false,
                mtime = file.mtime,
                size = file.size
            })
        end

        -- 分页计算
        local total = #all_items
        local total_pages = math.ceil(total / per_page)
        local start_idx = (page - 1) * per_page + 1
        local end_idx = math.min(page * per_page, total)

        -- 当前页数据
        local page_items = {}
        for i = start_idx, end_idx do
            table.insert(page_items, all_items[i])
        end

        return {
            items = page_items,
            page = page,
            per_page = per_page,
            total = total,
            total_pages = total_pages,
            current_path = dir_path
        }
    end

    return _M

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-log
  namespace: airflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-log
  template:
    metadata:
      labels:
        app: airflow-log
    spec:
      containers:
      - name: openresty
        image: registry.eqfleetcmder.com/eq/openresty:bitnami  # 使用bitnami/openresty镜像
        ports:
        - containerPort: 8080  # bitnami镜像默认使用8080端口
        env:
        - name: OPENRESTY_ENABLE_LUA_PATH
          value: "yes"
        - name: OPENRESTY_LUA_PATH
          value: "/opt/bitnami/openresty/nginx/conf/conf.d/lib/?.lua;;"  # 设置Lua模块搜索路径
        volumeMounts:
        - name: pvc-tos
          mountPath: /opt/bitnami/openresty/nginx/html/browse
          subPath: logs
        - name: nginx-conf
          mountPath: /opt/bitnami/openresty/nginx/conf/conf.d  # bitnami的配置目录
        - name: lua-scripts
          mountPath: /opt/bitnami/openresty/nginx/conf/conf.d/lib
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "256Mi"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - |
              ls /opt/bitnami/openresty/nginx/html >/dev/null 2>&1 &&
              touch /opt/bitnami/openresty/nginx/html/.alive 2>/dev/null &&
              rm /opt/bitnami/openresty/nginx/html/.alive 2>/dev/null
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /browse/
            port: 8080  # 使用bitnami的默认端口
          initialDelaySeconds: 5
          periodSeconds: 10
      # 移除initContainers，因为bitnami镜像已包含所需依赖
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
      - name: pvc-tos
        persistentVolumeClaim:
          claimName: airflow-pvc
      - name: nginx-conf
        configMap:
          name: airflow-nginx-logserver-conf
      - name: lua-scripts
        configMap:
          name: airflow-nginx-lua-scripts

---

apiVersion: v1
kind: Service
metadata:
  name: airflow-log-svc
  namespace: airflow
spec:
  selector:
    app: airflow-log
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
      nodePort: 31117  # 指定 NodePort，范围通常是 30000-32767
  type: NodePort
