apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: airflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret
      containers:
      - name: redis
        image: registry.eqfleetcmder.com/eq/redis:7.2-bookworm
        args: ["--requirepass", "$(REDIS_PASSWORD)"]
        env:
        - name: REDIS_PASSWORD
          value: airflow_123456
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"

---

apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: airflow
spec:
  selector:
    app: redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
      nodePort: 31111  # 指定 NodePort，范围通常是 30000-32767
  type: NodePort
