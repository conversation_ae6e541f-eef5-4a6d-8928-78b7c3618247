apiVersion: v1
data:
  .dockerconfigjson: ewoJImF1dGhzIjogewoJCSJjci1jbi1iZWlqaW5nLnZvbGNlcy5jb20iOiB7CgkJCSJhdXRoIjogImVXbHJiMjVuZW1ocGFtbGhRREl4TURBeE1qUXhNamM2UlhFeU1ESXlJVUFqIgoJCX0sCgkJInJlZ2lzdHJ5LmVxZmxlZXRjbWRlci5jb20iOiB7CgkJCSJhdXRoIjogIlpYRTZRbXRpTVRaWVRIZEVTR1phWm5CeCIKCQl9Cgl9Cn0K
kind: Secret
metadata:
  creationTimestamp: "2023-04-20T03:48:26Z"
  name: docker-registry-secret
  namespace: airflow
  resourceVersion: "10523213"
  uid: c4b4900e-004d-44f3-8f5a-2e59274fd4e0
type: kubernetes.io/dockerconfigjson
