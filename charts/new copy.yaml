apiVersion: apps/v1
kind: Deployment
metadata:
  name: openresty-file-browser
  namespace: airflow
  labels:
    app: openresty-file-browser
spec:
  replicas: 1
  selector:
    matchLabels:
      app: openresty-file-browser
  template:
    metadata:
      labels:
        app: openresty-file-browser
    spec:
      containers:
      - name: openresty
        image: registry.eqfleetcmder.com/eq/openresty:1.25.3.1-0-alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: data-volume
          mountPath: /usr/local/openresty/nginx/html/data
          readOnly: true
          subPath: logs  # 挂载PVC中的logs子目录
        - name: config-volume
          mountPath: /usr/local/openresty/nginx/conf/nginx.conf
          subPath: nginx.conf
        - name: config-volume
          mountPath: /usr/local/openresty/nginx/conf/lua/file-browser.lua
          subPath: file-browser.lua
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: airflow-pvc  # 替换为你的PVC名称
      - name: config-volume
        configMap:
          name: openresty-config
          items:
          - key: nginx.conf
            path: nginx.conf
          - key: file-browser.lua
            path: file-browser.lua
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: openresty-config
  namespace: airflow
data:
  nginx.conf: |
    worker_processes  1;
    events {
      worker_connections  1024;
    }
    http {
      include       mime.types;
      default_type  application/octet-stream;
      sendfile        on;
      keepalive_timeout  65;
      lua_package_path "/usr/local/openresty/lualib/?.lua;;";
      server {
        listen       80;
        server_name  localhost;
        location / {
          root   html;
          index  index.html index.htm;
        }
        location ~ ^/browse(/.*)?$ {
          set $path $1;
          content_by_lua_file conf/lua/file-browser.lua;
        }

        # 处理文件下载和查看请求
        location /data/ {
          alias /usr/local/openresty/nginx/html/data/;

          # 添加适当的MIME类型
          location ~* \.(log|txt)$ {
            add_header Content-Type text/plain;
            add_header Content-Disposition 'inline';
          }

          # 支持目录浏览
          autoindex on;
          autoindex_exact_size off;
          autoindex_localtime on;
        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
          root   html;
        }
      }
    }
  file-browser.lua: |
    -- 安全路径检查函数
    local function is_safe_path(base_path, target_path)
        local function normalize_path(path)
            local parts = {}
            for part in string.gmatch(path, "[^/]+") do
                if part == ".." then
                    table.remove(parts)
                elseif part ~= "." and part ~= "" then
                    table.insert(parts, part)
                end
            end
            return "/" .. table.concat(parts, "/")
        end

        local norm_base = normalize_path(base_path)
        local norm_target = normalize_path(target_path)

        return string.sub(norm_target, 1, string.len(norm_base)) == norm_base
    end

    -- 获取文件信息
    local function get_file_info(filepath)
        local handle = io.popen('stat -c "%s %Y %F" "' .. filepath .. '" 2>/dev/null')
        if not handle then return nil end

        local result = handle:read("*a")
        handle:close()

        if result and result ~= "" then
            local size, mtime, ftype = string.match(result, "(%d+) (%d+) (.+)")
            return {
                size = tonumber(size) or 0,
                mtime = tonumber(mtime) or 0,
                is_directory = string.find(ftype, "directory") ~= nil
            }
        end
        return nil
    end

    -- 格式化文件大小
    local function format_size(size)
        if size < 1024 then
            return size .. " B"
        elseif size < 1024 * 1024 then
            return string.format("%.1f KB", size / 1024)
        elseif size < 1024 * 1024 * 1024 then
            return string.format("%.1f MB", size / (1024 * 1024))
        else
            return string.format("%.1f GB", size / (1024 * 1024 * 1024))
        end
    end

    -- 格式化时间
    local function format_time(timestamp)
        return os.date("%Y-%m-%d %H:%M:%S", timestamp)
    end

    -- 获取目录内容
    local function get_directory_contents(dir_path, page, per_page)
        page = page or 1
        per_page = per_page or 20

        -- 安全检查
        local base_dir = "/usr/local/openresty/nginx/html/data"
        if not is_safe_path(base_dir, dir_path) then
            return nil, "Invalid path"
        end

        -- 检查目录是否存在
        local handle = io.popen('test -d "' .. dir_path .. '" && echo "exists"')
        local exists = handle:read("*a")
        handle:close()

        if not exists or exists:match("exists") == nil then
            return nil, "Directory not found"
        end

        -- 获取目录内容
        local cmd = 'find "' .. dir_path .. '" -maxdepth 1 -mindepth 1 | sort'
        handle = io.popen(cmd)
        if not handle then
            return nil, "Cannot access directory"
        end

        local items = {}
        for line in handle:lines() do
            local name = string.match(line, "([^/]+)$")
            local info = get_file_info(line)

            if info then
                table.insert(items, {
                    name = name,
                    path = line,
                    size = info.size,
                    mtime = info.mtime,
                    is_directory = info.is_directory
                })
            end
        end
        handle:close()

        -- 排序：目录在前，文件在后
        table.sort(items, function(a, b)
            if a.is_directory and not b.is_directory then
                return true
            elseif not a.is_directory and b.is_directory then
                return false
            else
                return a.name < b.name
            end
        end)

        -- 分页
        local total = #items
        local total_pages = math.ceil(total / per_page)
        page = math.min(page, total_pages)
        local start_idx = (page - 1) * per_page + 1
        local end_idx = math.min(page * per_page, total)

        local page_items = {}
        for i = start_idx, end_idx do
            if items[i] then
                table.insert(page_items, items[i])
            end
        end

        return page_items, total, total_pages, page
    end

    local function html_escape(str)
        return str:gsub("[&<>\"']", {
            ["&"] = "&amp;",
            ["<"] = "&lt;",
            [">"] = "&gt;",
            ["\""] = "&quot;",
            ["'"] = "&#39;"
        })
    end

    -- 主逻辑
    ngx.header.content_type = "text/html"

    local base_dir = "/usr/local/openresty/nginx/html/data"
    local request_path = ngx.var.path or ""
    local current_dir = base_dir .. request_path

    local page = tonumber(ngx.var.arg_page) or 1
    local per_page = tonumber(ngx.var.arg_per_page) or 20

    local items, total, total_pages, current_page = get_directory_contents(current_dir, page, per_page)

    if not items then
        ngx.status = 404
        ngx.say("Directory not found or access denied")
        return
    end

    -- 生成面包屑导航
    local function generate_breadcrumb(path)
        local breadcrumb = '<a href="/browse/">[Home]</a>'
        if path and path ~= "" and path ~= "/" then
            local parts = {}
            for part in string.gmatch(path, "[^/]+") do
                table.insert(parts, part)
            end

            local current_path = ""
            for i, part in ipairs(parts) do
                current_path = current_path .. "/" .. part
                breadcrumb = breadcrumb .. ' / <a href="/browse' .. current_path .. '">' .. html_escape(part) .. '</a>'
            end
        end
        return breadcrumb
    end

    ngx.say([[
    <!DOCTYPE html>
    <html>
    <head>
        <title>File Browser - Recursive Directory Tree</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .header { border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
            .breadcrumb { background: #e9ecef; padding: 10px; border-radius: 4px; margin-bottom: 20px; }
            .breadcrumb a { color: #007bff; text-decoration: none; margin: 0 5px; }
            .breadcrumb a:hover { text-decoration: underline; }
            .stats { background: #e7f3ff; padding: 10px; border-radius: 4px; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f8f9fa; font-weight: bold; }
            tr:hover { background-color: #f8f9fa; }
            .directory { color: #007bff; font-weight: bold; }
            .file { color: #333; }
            .pagination { margin-top: 20px; text-align: center; }
            .pagination a { display: inline-block; padding: 8px 12px; margin: 0 4px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            .pagination a:hover { background: #0056b3; }
            .pagination .active { background: #6c757d; }
            .icon {
                margin-right: 8px;
                font-family: monospace;
                font-weight: bold;
                padding: 2px 4px;
                border-radius: 3px;
                font-size: 0.8em;
            }
            .directory .icon { background: #e3f2fd; color: #1976d2; }
            .file .icon { background: #f3e5f5; color: #7b1fa2; }
            .breadcrumb a {
                background: #f8f9fa;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>[File Browser] - Recursive Directory Tree</h1>
            </div>

            <div class="breadcrumb">
                <strong>Current Path:</strong> ]] .. generate_breadcrumb(request_path) .. [[
            </div>

            <div class="stats">
                <strong>Total Items:</strong> ]] .. total .. [[ |
                <strong>Page:</strong> ]] .. current_page .. [[ of ]] .. total_pages .. [[ |
                <strong>Items per page:</strong> ]] .. per_page .. [[
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Size</th>
                        <th>Modified</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    ]])

    -- 添加返回上级目录链接
    if request_path and request_path ~= "" and request_path ~= "/" then
        local parent_path = string.match(request_path, "(.+)/[^/]+$") or ""
        ngx.say([[
                    <tr>
                        <td><a href="/browse]] .. parent_path .. [[" class="directory"><span class="icon">[DIR]</span> ..</a></td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
        ]])
    end

    -- 显示目录和文件
    for _, item in ipairs(items) do
        local relative_path = string.gsub(item.path, "/usr/local/openresty/nginx/html/data", "")
        if relative_path == "" then relative_path = "/" end

        local icon = item.is_directory and "[DIR]" or "[FILE]"
        local class = item.is_directory and "directory" or "file"
        local size = item.is_directory and "-" or format_size(item.size)
        local mtime = format_time(item.mtime)
        local prefix = item.is_directory and "browse" or "data"

        ngx.say([[
                    <tr>
                        <td><a href="/]] ..prefix.. [[]] .. html_escape(relative_path) .. [[" class="]] .. class .. [["><span class="icon">]] .. icon .. [[</span> ]] .. html_escape(item.name) .. [[</a></td>
                        <td>]] .. size .. [[</td>
                        <td>]] .. mtime .. [[</td>
                        <td>
        ]])

        if not item.is_directory then
            ngx.say([[<a href="/data]] .. html_escape(relative_path) .. [[" target="_blank">[View]</a> | <a href="/data]] .. html_escape(relative_path) .. [[" download>[Download]</a>]])
        else
            ngx.say([[<a href="/browse]] .. html_escape(relative_path) .. [[">[Open]</a>]])
        end

        ngx.say([[
                        </td>
                    </tr>
        ]])
    end

    ngx.say("</table>")

    ngx.say("                </tbody>")
    ngx.say("            </table>")

    -- 分页导航
    if total_pages > 1 then
        ngx.say([[
            <div class="pagination">
        ]])

        local base_url = "/browse" .. request_path .. "?"

        if current_page > 1 then
            ngx.say('<a href="' .. base_url .. 'page=1&per_page=' .. per_page .. '">[First]</a>')
            ngx.say('<a href="' .. base_url .. 'page=' .. (current_page - 1) .. '&per_page=' .. per_page .. '">[Prev]</a>')
        end

        -- 显示页码
        local start_page = math.max(1, current_page - 3)
        local end_page = math.min(total_pages, current_page + 3)

        for i = start_page, end_page do
            if i == current_page then
                ngx.say('<a class="active" href="' .. base_url .. 'page=' .. i .. '&per_page=' .. per_page .. '">' .. i .. '</a>')
            else
                ngx.say('<a href="' .. base_url .. 'page=' .. i .. '&per_page=' .. per_page .. '">' .. i .. '</a>')
            end
        end

        if current_page < total_pages then
            ngx.say('<a href="' .. base_url .. 'page=' .. (current_page + 1) .. '&per_page=' .. per_page .. '">[Next]</a>')
            ngx.say('<a href="' .. base_url .. 'page=' .. total_pages .. '&per_page=' .. per_page .. '">[Last]</a>')
        end

        ngx.say([[
            </div>
        ]])
    end

    ngx.say([[
        </div>
    </body>
    </html>
    ]])

---

apiVersion: v1
kind: Service
metadata:
  name: openresty-file-browser-svc
  namespace: airflow
spec:
  selector:
    app: openresty-file-browser
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      nodePort: 31118  # 指定 NodePort，范围通常是 30000-32767
  type: NodePort
