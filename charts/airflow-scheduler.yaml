apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-scheduler
  namespace: airflow
  labels:
    app: airflow
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1        # 更新时最多不可用 Pod 数
      maxSurge: 1              # 更新时最多超出的 Pod 数
  selector:
    matchLabels:
      app: airflow_scheduler
  template:
    metadata:
      name: airflow-scheduler
      labels:
        app: airflow_scheduler
    spec:
      containers:
        - name: airflow-scheduler
          image: registry.eqfleetcmder.com/eq/airflow:2.10.6
          imagePullPolicy: Always
          args: ["scheduler"]
          livenessProbe:
            initialDelaySeconds: 60
            timeoutSeconds: 20
            failureThreshold: 5
            periodSeconds: 60
            exec:
              command:
                - /bin/bash
                - -c
                - CONNECTION_CHECK_MAX_COUNT=0 AIRFLOW__LOGGING__LOGGING_LEVEL=ERROR exec /entrypoint airflow jobs check --job-type SchedulerJob --local
          volumeMounts:
            - name: pvc-tos
              mountPath: /opt/airflow/dags
              subPath: dags
            - name: pvc-tos
              mountPath: /opt/airflow/plugins
              subPath: plugins
            - name: pvc-tos
              mountPath: /opt/airflow/logs
              subPath: logs
            - name: pvc-tos
              mountPath: /opt/airflow/config
              subPath: config
            - name: pvc-tos
              mountPath: /opt/airflow/.env
              subPath: .env
            - name: pvc-tos
              mountPath: /opt/airflow/airflow.cfg
              subPath: airflow.cfg
            - name: pvc-tos
              mountPath: /home/<USER>/.kube/config
              subPath: config/kube.cfg
          resources:
            limits:
              cpu: 8
              memory: "16Gi"
            requests:
              cpu: "50m"
              memory: "1Gi"
          env:
            - name: CELERY_QUEUE
              value: deafult
            - name: AIRFLOW__CORE__DAGS_FOLDER
              value: /opt/airflow/dags
            - name: AIRFLOW__CORE__PLUGINS_FOLDER
              value: /opt/airflow/plugins
            - name: AIRFLOW__CORE__DEFAULT_TIMEZONE
              value: "cst"
            - name: TZ
              value: "Asia/Shanghai"
            - name: AIRFLOW__CELERY__OPERATION_TIMEOUT
              value: "8.0"
      restartPolicy: Always
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret
      volumes:
        - name: pvc-tos
          persistentVolumeClaim:
            claimName: airflow-pvc
