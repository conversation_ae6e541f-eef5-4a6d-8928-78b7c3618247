apiVersion: v1
data:
  closedloop.yml: "tos:\n  accessKey: AKLTYjYwNjRiZWVmNGI5NGY0YmE4MjgyNTAwYTE0NDhkMzg\n
    \ secretKey: TjJJNFl6TXpPVEF3TjJObU5EWTJOemt3TlRjMk1tRmpNVEJsTURZM01ETQ==\n  endpoint:
    tos-cn-beijing.ivolces.com\n  region: cn-beijing\n  bucketName: eq-data-closedloop\n\npackage:\n
    \ localTempPath: /data/workspace\n  \npg:\n  host: postgres0344848617f8.rds-pg.ivolces.com\n
    \ # host: *************\n  port: 5432\n  user: lbs\n  password: 1q2w3e4r!\n  dbname:
    lbs\n\ndms:\n  url: https://interface-dmp.eqfleetcmder.com/data/argoCallback\n\n
    \ \n  "
kind: ConfigMap
metadata:
  annotations:
    apps.vke.volcengine.com/updated: "2023-05-18T09:53:26Z"
  creationTimestamp: "2023-05-18T09:23:56Z"
  name: argo-pps-config-dev
  namespace: simulation
  resourceVersion: "219966407"
  uid: 2d4642ed-0c89-4e14-aea2-a9348b47ebce
