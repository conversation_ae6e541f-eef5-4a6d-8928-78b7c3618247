# Trino配置参数
trino:
  image:
    repository: trinodb/trino
    tag: "435"
    pullPolicy: IfNotPresent
  
  # 资源配置
  resources:
    requests:
      memory: "4Gi"
      cpu: "1000m"
    limits:
      memory: "8Gi"
      cpu: "2000m"
  
  # JVM配置
  jvm:
    maxHeapSize: "8G"
    gcType: "G1GC"
    heapRegionSize: "32M"
  
  # Trino配置
  config:
    coordinator: true
    includeCoordinator: true
    httpPort: 8080
    queryMaxMemory: "4GB"
    queryMaxMemoryPerNode: "1GB"
    queryMaxTotalMemoryPerNode: "2GB"
    discoveryUri: "http://localhost:8080"
  
  # 健康检查
  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
  
  readinessProbe:
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5

# PostgreSQL连接配置
postgresql:
  enabled: true
  host: "postgresql-service"
  port: 5432
  database: "airflow"
  username: "airflow"
  password: "airflow123"
  # 连接池配置
  connectionPool:
    maxSize: 20
    minSize: 5
  # SSL配置
  ssl:
    enabled: false
    mode: "require"

# S3配置
s3:
  enabled: true
  # AWS配置
  aws:
    region: "us-east-1"
    endpoint: "s3.amazonaws.com"
    accessKeyId: "YOUR_AWS_ACCESS_KEY"
    secretAccessKey: "YOUR_AWS_SECRET_KEY"
  
  # S3存储桶配置
  bucket:
    name: "your-backup-bucket"
    prefix: "airflow-data-backup"
  
  # Hive配置
  hive:
    metastore: "file"
    catalogDir: "/tmp/hive"
    nonManagedTableWrites: true
    ssl: true
    pathStyleAccess: false
    maxConnections: 500
    maxErrorRetries: 10
    connectTimeout: "5s"

# 服务配置
service:
  type: ClusterIP
  port: 8080
  
  # NodePort配置（如果type为NodePort）
  nodePort:
    enabled: true
    port: 30080

# Ingress配置
ingress:
  enabled: false
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
  hosts:
    - host: trino.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# 存储配置
storage:
  # 数据目录
  dataDir: "/data/trino"
  # 存储类型
  storageClass: ""
  # 存储大小
  size: "10Gi"
  # 访问模式
  accessMode: "ReadWriteOnce"

# 安全配置
security:
  # 认证配置
  authentication:
    enabled: false
    type: "PASSWORD"
  
  # HTTPS配置
  https:
    enabled: false
    port: 8443
    keystore: ""
    keystorePassword: ""
  
  # 网络策略
  networkPolicy:
    enabled: false
    ingress: []
    egress: []

# 监控配置
monitoring:
  # JMX监控
  jmx:
    enabled: true
    port: 9080
  
  # Prometheus监控
  prometheus:
    enabled: false
    port: 9090
    path: "/metrics"
  
  # 日志配置
  logging:
    level: "INFO"
    rootLevel: "INFO"
    categories:
      "io.trino": "INFO"
      "io.trino.server.security": "DEBUG"

# 高可用配置（单点部署时不需要）
highAvailability:
  enabled: false
  replicas: 1
  
  # 工作节点配置
  workers:
    enabled: false
    replicas: 0
    resources:
      requests:
        memory: "2Gi"
        cpu: "500m"
      limits:
        memory: "4Gi"
        cpu: "1000m"

# 初始化配置
initialization:
  # 是否自动创建表
  createTables: true
  
  # 初始化脚本
  scripts:
    - name: "create-schemas"
      content: |
        CREATE SCHEMA IF NOT EXISTS s3.airflow_backup;
    
    - name: "create-views"
      content: |
        -- 创建组合视图的SQL将在这里
        
# 备份配置
backup:
  # S3存储结构
  structure:
    dagRun: "dag_run/year={year}/month={month}/"
    job: "job/year={year}/month={month}/"
    log: "log/year={year}/month={month}/"
    taskInstance: "task_instance/year={year}/month={month}/"
    xcom: "xcom/year={year}/month={month}/"
    variable: "variable/version={version}/"
    connection: "connection/version={version}/"
    summary: "backup_summary/year={year}/month={month}/"
  
  # 数据保留策略
  retention:
    # 热数据保留天数
    hotDataDays: 30
    # 冷数据保留月数
    coldDataMonths: 12
    # 配置数据保留版本数
    configVersions: 10

# 性能优化配置
performance:
  # 查询优化
  query:
    maxConcurrentQueries: 100
    maxQueuedQueries: 1000
    maxQueryLength: "1MB"
    maxStageCount: 100
  
  # 任务配置
  task:
    maxWorkerThreads: 200
    minDrivers: 2
    maxDriversPerTask: 3
  
  # 内存配置
  memory:
    heapHeadroomPerNode: "1GB"
    reservedSystemMemory: "1GB"
    
  # 网络配置
  network:
    maxRequestsPerDestination: 16
    maxConnectionsPerServer: 200
    requestTimeout: "2m"

# 开发和调试配置
development:
  # 调试模式
  debug: false
  
  # 详细日志
  verboseLogging: false
  
  # 性能分析
  profiling: false
  
  # 查询历史保留
  queryHistoryRetention: "7d"

# 自定义配置文件
customConfig:
  # 额外的配置属性
  additionalProperties: {}
  
  # 自定义catalog配置
  catalogs: {}
  
  # 自定义连接器配置
  connectors: {}
