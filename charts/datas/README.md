# Trino部署配置

## 概述

这个配置用于在Kubernetes中部署单点Trino实例，用于查询Airflow的PostgreSQL热数据和S3冷数据。

## 🚀 部署步骤

### 1. 修改配置

#### 更新Secret中的凭证
编辑 `trino-deployment.yaml` 中的Secret部分：

```bash
# 生成Base64编码的凭证
echo -n "your_aws_access_key" | base64
echo -n "your_aws_secret_key" | base64
echo -n "your_postgres_password" | base64
```

#### 更新S3配置
修改 `s3.properties` 中的S3配置：
- `hive.s3.endpoint`: S3端点
- `hive.s3.region`: S3区域
- 在 `trino-init-tables.sql` 中更新S3存储桶名称

#### 更新PostgreSQL配置
修改 `postgresql.properties` 中的数据库连接信息：
- `connection-url`: PostgreSQL连接URL
- `connection-user`: 数据库用户名
- `connection-password`: 数据库密码

### 2. 部署到Kubernetes

```bash
# 应用配置
kubectl apply -f trino-deployment.yaml

# 检查部署状态
kubectl get pods -l app=trino
kubectl get services -l app=trino

# 查看日志
kubectl logs -l app=trino -f
```

### 3. 访问Trino

#### 通过NodePort访问
```bash
# 获取节点IP
kubectl get nodes -o wide

# 访问Trino Web UI
http://<node-ip>:30080
```

#### 通过Ingress访问（需要配置Ingress Controller）
```bash
# 添加hosts记录
echo "<ingress-ip> trino.local" >> /etc/hosts

# 访问
http://trino.local
```

#### 通过端口转发访问
```bash
kubectl port-forward service/trino-service 8080:8080
# 访问 http://localhost:8080
```

## 📊 初始化表结构

### 1. 连接到Trino

使用Trino CLI连接：
```bash
# 下载Trino CLI
curl -o trino https://repo1.maven.org/maven2/io/trino/trino-cli/435/trino-cli-435-executable.jar
chmod +x trino

# 连接到Trino
./trino --server http://localhost:8080 --catalog s3 --schema airflow_backup
```

### 2. 执行初始化脚本

```sql
-- 在Trino CLI中执行
SOURCE 'trino-init-tables.sql';
```

或者逐个执行SQL语句创建表和视图。

## 🔍 查询示例

### 查询热数据（PostgreSQL）
```sql
-- 查询最近30天的DAG运行情况
SELECT 
    dag_id,
    state,
    COUNT(*) as run_count
FROM postgresql.public.dag_run
WHERE execution_date >= current_date - interval '30' day
GROUP BY dag_id, state
ORDER BY run_count DESC;
```

### 查询冷数据（S3）
```sql
-- 查询历史DAG运行统计
SELECT 
    dag_id,
    state,
    COUNT(*) as run_count,
    year,
    month
FROM s3.airflow_backup.dag_run
WHERE year = 2025 AND month = 1
GROUP BY dag_id, state, year, month
ORDER BY run_count DESC;
```

### 联合查询热数据和冷数据
```sql
-- 使用组合视图查询最近3个月的数据
SELECT 
    data_source,
    dag_id,
    state,
    COUNT(*) as run_count
FROM s3.airflow_backup.dag_run_combined
WHERE execution_date >= current_date - interval '3' month
GROUP BY data_source, dag_id, state
ORDER BY run_count DESC;
```

### 查询任务失败分析
```sql
-- 任务失败统计
SELECT 
    dag_id,
    task_id,
    COUNT(*) as failure_count,
    AVG(duration) as avg_duration
FROM s3.airflow_backup.task_instance_combined
WHERE state = 'failed' 
    AND start_date >= current_date - interval '1' month
GROUP BY dag_id, task_id
HAVING COUNT(*) > 5
ORDER BY failure_count DESC;
```

### 查询配置变更历史
```sql
-- 查看变量配置的历史版本
SELECT 
    key,
    val,
    backup_date,
    backup_version
FROM s3.airflow_backup.variable
WHERE key = 'your_variable_key'
ORDER BY backup_version DESC;
```

## 🔧 配置说明

### Trino配置文件

#### config.properties
- `coordinator=true`: 启用协调器模式
- `node-scheduler.include-coordinator=true`: 协调器节点也参与查询执行
- `query.max-memory=4GB`: 查询最大内存限制

#### catalog配置

##### postgresql.properties
- 连接到Airflow的PostgreSQL数据库
- 用于查询热数据（最近30天）

##### s3.properties
- 使用Hive connector连接S3
- 支持Parquet格式文件
- 按年月分区存储

### 资源配置

```yaml
resources:
  requests:
    memory: "4Gi"
    cpu: "1000m"
  limits:
    memory: "8Gi"
    cpu: "2000m"
```

根据实际负载调整资源配置。

## 🚨 监控和维护

### 健康检查
```bash
# 检查Trino状态
curl http://localhost:8080/v1/info

# 检查集群状态
curl http://localhost:8080/v1/cluster
```

### 查询监控
```sql
-- 查看正在运行的查询
SELECT * FROM system.runtime.queries;

-- 查看查询历史
SELECT * FROM system.runtime.completed_queries
ORDER BY created DESC
LIMIT 10;
```

### 日志查看
```bash
# 查看Pod日志
kubectl logs -l app=trino --tail=100

# 实时查看日志
kubectl logs -l app=trino -f
```

## 🔒 安全配置

### 1. 网络安全
- 使用ClusterIP限制内部访问
- 配置Ingress时启用TLS
- 限制NodePort的访问范围

### 2. 认证授权
```yaml
# 在config.properties中添加认证配置
http-server.authentication.type=PASSWORD
http-server.https.enabled=true
http-server.https.port=8443
```

### 3. 数据加密
- S3数据传输加密
- PostgreSQL连接加密
- 敏感配置使用Secret存储

## 📈 性能优化

### 1. 内存配置
根据查询复杂度调整内存设置：
```properties
query.max-memory=8GB
query.max-memory-per-node=2GB
query.max-total-memory-per-node=4GB
```

### 2. 并发配置
```properties
query.max-concurrent-queries=100
task.max-worker-threads=200
```

### 3. S3优化
```properties
hive.s3.max-connections=500
hive.s3.max-error-retries=10
hive.s3.connect-timeout=5s
```

## 🛠️ 故障排查

### 常见问题

1. **连接PostgreSQL失败**
   - 检查网络连通性
   - 验证数据库凭证
   - 确认防火墙设置

2. **S3访问失败**
   - 检查AWS凭证
   - 验证S3权限
   - 确认存储桶存在

3. **内存不足**
   - 调整JVM堆内存
   - 优化查询语句
   - 增加节点资源

4. **查询超时**
   - 增加查询超时时间
   - 优化表分区
   - 使用查询优化技巧
