apiVersion: v1
kind: ConfigMap
metadata:
  name: trino-config
  namespace: default
data:
  config.properties: |
    coordinator=true
    node-scheduler.include-coordinator=true
    http-server.http.port=8080
    query.max-memory=4GB
    query.max-memory-per-node=1GB
    query.max-total-memory-per-node=2GB
    discovery-server.enabled=true
    discovery.uri=http://localhost:8080
  
  jvm.config: |
    -server
    -Xmx8G
    -XX:+UseG1GC
    -XX:G1HeapRegionSize=32M
    -XX:+UseGCOverheadLimit
    -XX:+ExplicitGCInvokesConcurrent
    -XX:+HeapDumpOnOutOfMemoryError
    -XX:+ExitOnOutOfMemoryError
    -Djdk.attach.allowAttachSelf=true
  
  log.properties: |
    io.trino=INFO
  
  node.properties: |
    node.environment=production
    node.id=ffffffff-ffff-ffff-ffff-ffffffffffff
    node.data-dir=/data/trino

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: trino-catalog-config
  namespace: default
data:
  postgresql.properties: |
    connector.name=postgresql
    connection-url=*************************************************
    connection-user=airflow
    connection-password=airflow123
    case-insensitive-name-matching=true
  
  s3.properties: |
    connector.name=hive-hadoop2
    hive.metastore=file
    hive.metastore.catalog.dir=/tmp/hive
    hive.non-managed-table-writes-enabled=true
    hive.s3.endpoint=s3.amazonaws.com
    hive.s3.region=us-east-1
    hive.s3.aws-access-key=${ENV:AWS_ACCESS_KEY_ID}
    hive.s3.aws-secret-key=${ENV:AWS_SECRET_ACCESS_KEY}
    hive.s3.ssl.enabled=true
    hive.s3.path-style-access=false
    hive.allow-drop-table=true
    hive.allow-rename-table=true
    hive.allow-add-column=true
    hive.allow-drop-column=true
    hive.allow-rename-column=true
  
  memory.properties: |
    connector.name=memory
    memory.max-data-per-node=128MB

---
apiVersion: v1
kind: Secret
metadata:
  name: trino-secrets
  namespace: default
type: Opaque
data:
  # Base64 encoded values - replace with your actual credentials
  aws-access-key-id: WU9VUl9BV1NfQUNDRVNTX0tFWQ==  # YOUR_AWS_ACCESS_KEY
  aws-secret-access-key: WU9VUl9BV1NfU0VDUkVUX0tFWQ==  # YOUR_AWS_SECRET_KEY
  postgres-password: YWlyZmxvdzEyMw==  # airflow123

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trino
  namespace: default
  labels:
    app: trino
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trino
  template:
    metadata:
      labels:
        app: trino
    spec:
      containers:
      - name: trino
        image: trinodb/trino:435
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: trino-secrets
              key: aws-access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: trino-secrets
              key: aws-secret-access-key
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "2000m"
        volumeMounts:
        - name: trino-config
          mountPath: /etc/trino
        - name: trino-catalog
          mountPath: /etc/trino/catalog
        - name: trino-data
          mountPath: /data/trino
        livenessProbe:
          httpGet:
            path: /v1/info
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /v1/info
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: trino-config
        configMap:
          name: trino-config
      - name: trino-catalog
        configMap:
          name: trino-catalog-config
      - name: trino-data
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: trino-service
  namespace: default
  labels:
    app: trino
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: trino

---
apiVersion: v1
kind: Service
metadata:
  name: trino-nodeport
  namespace: default
  labels:
    app: trino
spec:
  type: NodePort
  ports:
  - port: 8080
    targetPort: 8080
    nodePort: 30080
    protocol: TCP
    name: http
  selector:
    app: trino

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trino-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: trino.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: trino-service
            port:
              number: 8080
