-- Trino示例查询 - Airflow数据分析
-- 连接方式: ./trino --server http://localhost:8080

-- ============================================
-- 1. 基础数据探索
-- ============================================

-- 查看可用的catalog和schema
SHOW CATALOGS;
SHOW SCHEMAS FROM postgresql;
SHOW SCHEMAS FROM s3;

-- 查看表结构
DESCRIBE postgresql.public.dag_run;
DESCRIBE s3.airflow_backup.dag_run;

-- 查看表数据量
SELECT COUNT(*) as hot_dag_runs FROM postgresql.public.dag_run;
SELECT COUNT(*) as cold_dag_runs FROM s3.airflow_backup.dag_run;

-- ============================================
-- 2. DAG运行分析
-- ============================================

-- 最近30天DAG运行成功率
SELECT 
    dag_id,
    COUNT(*) as total_runs,
    SUM(CASE WHEN state = 'success' THEN 1 ELSE 0 END) as success_runs,
    ROUND(
        SUM(CASE WHEN state = 'success' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 
        2
    ) as success_rate
FROM postgresql.public.dag_run
WHERE execution_date >= current_date - interval '30' day
GROUP BY dag_id
HAVING COUNT(*) > 5
ORDER BY success_rate DESC;

-- 历史数据中DAG运行趋势（按月统计）
SELECT 
    dag_id,
    year,
    month,
    COUNT(*) as run_count,
    SUM(CASE WHEN state = 'success' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN state = 'failed' THEN 1 ELSE 0 END) as failed_count
FROM s3.airflow_backup.dag_run
WHERE year = 2025
GROUP BY dag_id, year, month
ORDER BY dag_id, year, month;

-- 联合查询：完整的DAG运行历史
WITH combined_runs AS (
    SELECT 
        'hot' as data_source,
        dag_id,
        execution_date,
        state,
        EXTRACT(EPOCH FROM (end_date - start_date)) as duration_seconds
    FROM postgresql.public.dag_run
    WHERE execution_date >= current_date - interval '30' day
    
    UNION ALL
    
    SELECT 
        'cold' as data_source,
        dag_id,
        execution_date,
        state,
        EXTRACT(EPOCH FROM (end_date - start_date)) as duration_seconds
    FROM s3.airflow_backup.dag_run
    WHERE year >= YEAR(current_date - interval '3' month)
)
SELECT 
    dag_id,
    COUNT(*) as total_runs,
    AVG(duration_seconds) as avg_duration,
    MIN(execution_date) as first_run,
    MAX(execution_date) as last_run
FROM combined_runs
GROUP BY dag_id
ORDER BY total_runs DESC;

-- ============================================
-- 3. 任务实例分析
-- ============================================

-- 最耗时的任务TOP 10
SELECT 
    dag_id,
    task_id,
    AVG(duration) as avg_duration,
    MAX(duration) as max_duration,
    COUNT(*) as run_count
FROM postgresql.public.task_instance
WHERE start_date >= current_date - interval '7' day
    AND state = 'success'
    AND duration IS NOT NULL
GROUP BY dag_id, task_id
HAVING COUNT(*) > 3
ORDER BY avg_duration DESC
LIMIT 10;

-- 任务失败分析
SELECT 
    dag_id,
    task_id,
    COUNT(*) as failure_count,
    COUNT(DISTINCT DATE(start_date)) as failure_days,
    MIN(start_date) as first_failure,
    MAX(start_date) as last_failure
FROM postgresql.public.task_instance
WHERE state = 'failed'
    AND start_date >= current_date - interval '30' day
GROUP BY dag_id, task_id
HAVING COUNT(*) > 2
ORDER BY failure_count DESC;

-- 任务重试分析
SELECT 
    dag_id,
    task_id,
    AVG(try_number) as avg_retries,
    MAX(try_number) as max_retries,
    COUNT(*) as total_runs,
    SUM(CASE WHEN try_number > 1 THEN 1 ELSE 0 END) as retry_runs
FROM postgresql.public.task_instance
WHERE start_date >= current_date - interval '30' day
GROUP BY dag_id, task_id
HAVING AVG(try_number) > 1.5
ORDER BY avg_retries DESC;

-- ============================================
-- 4. 系统性能分析
-- ============================================

-- 每小时任务执行量
SELECT 
    DATE_TRUNC('hour', start_date) as hour,
    COUNT(*) as task_count,
    COUNT(DISTINCT dag_id) as unique_dags,
    AVG(duration) as avg_duration
FROM postgresql.public.task_instance
WHERE start_date >= current_date - interval '7' day
    AND state IN ('success', 'failed')
GROUP BY DATE_TRUNC('hour', start_date)
ORDER BY hour;

-- 执行器使用情况
SELECT 
    hostname,
    COUNT(*) as task_count,
    COUNT(DISTINCT dag_id) as unique_dags,
    AVG(duration) as avg_duration,
    SUM(CASE WHEN state = 'success' THEN 1 ELSE 0 END) as success_count
FROM postgresql.public.task_instance
WHERE start_date >= current_date - interval '7' day
GROUP BY hostname
ORDER BY task_count DESC;

-- 队列使用分析
SELECT 
    COALESCE(queue, 'default') as queue_name,
    COUNT(*) as task_count,
    AVG(duration) as avg_duration,
    AVG(EXTRACT(EPOCH FROM (start_date - queued_dttm))) as avg_queue_time
FROM postgresql.public.task_instance
WHERE start_date >= current_date - interval '7' day
    AND queued_dttm IS NOT NULL
GROUP BY queue
ORDER BY task_count DESC;

-- ============================================
-- 5. 配置变更历史
-- ============================================

-- 查看最新的变量配置
SELECT 
    key,
    val,
    description,
    backup_date
FROM s3.airflow_backup.variable_latest
ORDER BY key;

-- 变量配置变更历史
WITH variable_changes AS (
    SELECT 
        key,
        val,
        backup_version,
        backup_date,
        LAG(val) OVER (PARTITION BY key ORDER BY backup_version) as prev_val
    FROM s3.airflow_backup.variable
    WHERE key = 'your_variable_key'  -- 替换为实际的变量名
)
SELECT 
    key,
    backup_date,
    prev_val as old_value,
    val as new_value,
    backup_version
FROM variable_changes
WHERE prev_val IS NOT NULL AND prev_val != val
ORDER BY backup_version DESC;

-- 连接配置统计
SELECT 
    conn_type,
    COUNT(*) as connection_count,
    COUNT(DISTINCT host) as unique_hosts
FROM s3.airflow_backup.connection
WHERE version = (SELECT MAX(version) FROM s3.airflow_backup.connection)
GROUP BY conn_type
ORDER BY connection_count DESC;

-- ============================================
-- 6. 数据质量检查
-- ============================================

-- 检查数据完整性：对比热数据和冷数据的重叠
WITH overlap_check AS (
    SELECT 
        dag_id,
        run_id,
        execution_date
    FROM postgresql.public.dag_run
    WHERE execution_date < current_date - interval '30' day
    
    INTERSECT
    
    SELECT 
        dag_id,
        run_id,
        execution_date
    FROM s3.airflow_backup.dag_run
)
SELECT COUNT(*) as overlapping_records FROM overlap_check;

-- 检查分区数据分布
SELECT 
    year,
    month,
    COUNT(*) as record_count,
    COUNT(DISTINCT dag_id) as unique_dags,
    MIN(execution_date) as min_date,
    MAX(execution_date) as max_date
FROM s3.airflow_backup.dag_run
GROUP BY year, month
ORDER BY year DESC, month DESC;

-- 检查数据新鲜度
SELECT 
    'postgresql' as source,
    MAX(execution_date) as latest_date,
    COUNT(*) as total_records
FROM postgresql.public.dag_run

UNION ALL

SELECT 
    's3' as source,
    MAX(execution_date) as latest_date,
    COUNT(*) as total_records
FROM s3.airflow_backup.dag_run;

-- ============================================
-- 7. 业务分析示例
-- ============================================

-- DAG依赖分析（基于XCom数据）
SELECT 
    x1.dag_id as producer_dag,
    x2.dag_id as consumer_dag,
    x1.key as xcom_key,
    COUNT(*) as usage_count
FROM s3.airflow_backup.xcom x1
JOIN s3.airflow_backup.xcom x2 
    ON x1.key = x2.key 
    AND x1.dag_id != x2.dag_id
    AND x1.timestamp < x2.timestamp
WHERE x1.year >= YEAR(current_date - interval '3' month)
    AND x2.year >= YEAR(current_date - interval '3' month)
GROUP BY x1.dag_id, x2.dag_id, x1.key
HAVING COUNT(*) > 5
ORDER BY usage_count DESC;

-- 月度运行成本分析（基于运行时长）
SELECT 
    year,
    month,
    dag_id,
    COUNT(*) as runs,
    SUM(EXTRACT(EPOCH FROM (end_date - start_date))) / 3600 as total_hours,
    AVG(EXTRACT(EPOCH FROM (end_date - start_date))) / 60 as avg_minutes
FROM s3.airflow_backup.dag_run
WHERE state = 'success'
    AND end_date IS NOT NULL
    AND start_date IS NOT NULL
GROUP BY year, month, dag_id
ORDER BY year DESC, month DESC, total_hours DESC;

-- 错误模式分析
SELECT 
    dag_id,
    task_id,
    DATE_TRUNC('day', start_date) as failure_date,
    COUNT(*) as failure_count,
    STRING_AGG(DISTINCT hostname, ', ') as failed_hosts
FROM postgresql.public.task_instance
WHERE state = 'failed'
    AND start_date >= current_date - interval '30' day
GROUP BY dag_id, task_id, DATE_TRUNC('day', start_date)
HAVING COUNT(*) > 1
ORDER BY failure_date DESC, failure_count DESC;
