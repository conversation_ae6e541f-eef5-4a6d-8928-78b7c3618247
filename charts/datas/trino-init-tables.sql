-- Trino初始化脚本 - 创建S3外部表用于查询Airflow备份数据
-- 使用方法: 连接到Trino后执行此脚本

-- 创建schema用于存放备份数据表
CREATE SCHEMA IF NOT EXISTS s3.airflow_backup;

-- 1. 创建dag_run表（按年月分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.dag_run (
    dag_id VARCHAR,
    execution_date TIMESTAMP,
    state VARCHAR,
    run_id VARCHAR,
    external_trigger BOOLEAN,
    conf VARCHAR,
    end_date TIMESTAMP,
    start_date TIMESTAMP,
    run_type VARCHAR,
    last_scheduling_decision TIMESTAMP,
    dag_hash VARCHAR,
    creating_job_id BIGINT,
    queued_at TIMESTAMP,
    data_interval_start TIMESTAMP,
    data_interval_end TIMESTAMP,
    updated_at TIMESTAMP
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/dag_run/',
    format = 'PARQUET',
    partitioned_by = ARRAY['year', 'month']
);

-- 2. 创建job表（按年月分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.job (
    id BIGINT,
    dag_id VARCHAR,
    state VARCHAR,
    job_type VARCHAR,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    latest_heartbeat TIMESTAMP,
    executor_class VARCHAR,
    hostname VARCHAR,
    unixname VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/job/',
    format = 'PARQUET',
    partitioned_by = ARRAY['year', 'month']
);

-- 3. 创建log表（按年月分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.log (
    id BIGINT,
    dttm TIMESTAMP,
    dag_id VARCHAR,
    task_id VARCHAR,
    event VARCHAR,
    execution_date TIMESTAMP,
    owner VARCHAR,
    extra VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/log/',
    format = 'PARQUET',
    partitioned_by = ARRAY['year', 'month']
);

-- 4. 创建task_instance表（按年月分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.task_instance (
    task_id VARCHAR,
    dag_id VARCHAR,
    run_id VARCHAR,
    map_index INTEGER,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    duration DOUBLE,
    state VARCHAR,
    try_number INTEGER,
    max_tries INTEGER,
    hostname VARCHAR,
    unixname VARCHAR,
    job_id BIGINT,
    pool VARCHAR,
    pool_slots INTEGER,
    queue VARCHAR,
    priority_weight INTEGER,
    operator VARCHAR,
    queued_dttm TIMESTAMP,
    queued_by_job_id BIGINT,
    pid INTEGER,
    executor_config VARCHAR,
    updated_at TIMESTAMP,
    rendered_fields VARCHAR,
    next_method VARCHAR,
    next_kwargs VARCHAR,
    run_as_user VARCHAR,
    external_executor_id VARCHAR,
    trigger_id BIGINT,
    trigger_timeout TIMESTAMP,
    note VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/task_instance/',
    format = 'PARQUET',
    partitioned_by = ARRAY['year', 'month']
);

-- 5. 创建xcom表（按年月分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.xcom (
    dag_run_id BIGINT,
    task_id VARCHAR,
    map_index INTEGER,
    key VARCHAR,
    value VARCHAR,
    timestamp TIMESTAMP,
    dag_id VARCHAR,
    run_id VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/xcom/',
    format = 'PARQUET',
    partitioned_by = ARRAY['year', 'month']
);

-- 6. 创建variable表（按版本分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.variable (
    key VARCHAR,
    val VARCHAR,
    description VARCHAR,
    is_encrypted BOOLEAN,
    backup_version VARCHAR,
    backup_date VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/variable/',
    format = 'PARQUET',
    partitioned_by = ARRAY['version']
);

-- 7. 创建connection表（按版本分区）
CREATE TABLE IF NOT EXISTS s3.airflow_backup.connection (
    conn_id VARCHAR,
    conn_type VARCHAR,
    description VARCHAR,
    host VARCHAR,
    schema VARCHAR,
    login VARCHAR,
    password VARCHAR,
    port INTEGER,
    extra VARCHAR,
    is_encrypted BOOLEAN,
    is_extra_encrypted BOOLEAN,
    backup_version VARCHAR,
    backup_date VARCHAR
)
WITH (
    external_location = 's3://your-backup-bucket/airflow-data-backup/connection/',
    format = 'PARQUET',
    partitioned_by = ARRAY['version']
);

-- 创建视图用于联合查询热数据和冷数据
-- 1. DAG运行统计视图（热数据 + 冷数据）
CREATE OR REPLACE VIEW s3.airflow_backup.dag_run_combined AS
SELECT 
    'hot' as data_source,
    dag_id,
    execution_date,
    state,
    run_id,
    start_date,
    end_date,
    run_type
FROM postgresql.public.dag_run
WHERE execution_date >= current_date - interval '30' day

UNION ALL

SELECT 
    'cold' as data_source,
    dag_id,
    execution_date,
    state,
    run_id,
    start_date,
    end_date,
    run_type
FROM s3.airflow_backup.dag_run
WHERE year >= YEAR(current_date - interval '1' year);

-- 2. 任务实例统计视图
CREATE OR REPLACE VIEW s3.airflow_backup.task_instance_combined AS
SELECT 
    'hot' as data_source,
    task_id,
    dag_id,
    run_id,
    start_date,
    end_date,
    duration,
    state,
    try_number,
    hostname
FROM postgresql.public.task_instance
WHERE start_date >= current_date - interval '30' day

UNION ALL

SELECT 
    'cold' as data_source,
    task_id,
    dag_id,
    run_id,
    start_date,
    end_date,
    duration,
    state,
    try_number,
    hostname
FROM s3.airflow_backup.task_instance
WHERE year >= YEAR(current_date - interval '1' year);

-- 3. 最新变量配置视图
CREATE OR REPLACE VIEW s3.airflow_backup.variable_latest AS
SELECT 
    key,
    val,
    description,
    is_encrypted,
    backup_version,
    backup_date
FROM s3.airflow_backup.variable
WHERE version = (
    SELECT MAX(version) 
    FROM s3.airflow_backup.variable
);

-- 示例查询
-- 查看表结构
-- DESCRIBE s3.airflow_backup.dag_run;

-- 查询最近3个月的DAG运行统计
-- SELECT 
--     dag_id,
--     state,
--     COUNT(*) as run_count,
--     AVG(EXTRACT(EPOCH FROM (end_date - start_date))) as avg_duration_seconds
-- FROM s3.airflow_backup.dag_run_combined
-- WHERE execution_date >= current_date - interval '3' month
-- GROUP BY dag_id, state
-- ORDER BY run_count DESC;

-- 查询任务失败统计
-- SELECT 
--     dag_id,
--     task_id,
--     COUNT(*) as failure_count
-- FROM s3.airflow_backup.task_instance_combined
-- WHERE state = 'failed' 
--     AND start_date >= current_date - interval '3' month
-- GROUP BY dag_id, task_id
-- ORDER BY failure_count DESC
-- LIMIT 10;
