apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-pv-node2
  labels:
    node-type: database
    storage-type: local-ssd
spec:
  capacity:
    storage: 1Gi
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: /mnt/granafa
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - ***********  # 替换为实际节点名
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: kube-system
spec:
  storageClassName: local-storage
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  volumeMode: Filesystem # 挂载对象存储的格式，本示例填写 Filesystem，表示文件系统挂载。
  volumeName: local-pv-node2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: grafana
  name: grafana
  namespace: kube-system
spec:
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      securityContext:
        fsGroup: 472
        supplementalGroups:
          - 0
      containers:
        - name: grafana
          image: registry.eqfleetcmder.com/eq/grafana:closedloop
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 1
          resources:
            requests:
              cpu: 250m
              memory: 750Mi
            limits:
              cpu: 500m
              memory: 2Gi
          volumeMounts:
            - mountPath: /var/lib/grafana
              name: grafana-strorage
      volumes:
        - name: grafana-strorage
          persistentVolumeClaim:
            claimName: grafana-pvc
      imagePullSecrets:
        - name: docker-registry-secret
      tolerations:
        - key: node
          operator: Equal
          value: gpu
          effect: NoSchedule

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: kube-system
spec:
  ports:
    - port: 3000
      targetPort: 3000
      nodePort: 31116
      protocol: TCP
  selector:
    app: grafana
  sessionAffinity: None
  type: NodePort
