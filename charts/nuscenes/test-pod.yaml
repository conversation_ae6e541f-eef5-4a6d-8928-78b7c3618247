apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  namespace: argo
  labels:
    app: test
spec:
  containers:
  - name: test-pod
    image: registry.eqfleetcmder.com/eq/closedloop/rosbag-sensor-analyzer:v1.0.1
    imagePullPolicy: Always
    command: ["sh", "-c", "sleep inf"]
    volumeMounts:
    - name: airflow-common-path
      mountPath: /data
    resources:
      requests:
        cpu: "10m"
        memory: "100Mi"
  restartPolicy: Always
  imagePullSecrets:
  - name: docker-registry-secret
  volumes:
  - name: airflow-common-path
    persistentVolumeClaim:
      claimName: closedloop-pvc-rw
