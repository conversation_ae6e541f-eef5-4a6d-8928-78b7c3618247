apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-pv-node1
  labels:
    node-type: database
    storage-type: local-ssd
spec:
  capacity:
    storage: 50Gi
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: /mnt/pg-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - ***********  # 替换为实际节点名

---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: node1-pvc  # PVC 名称。
  namespace: airflow
spec:
  storageClassName: local-storage
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi  # 声明的存储使用量。
  volumeMode: Filesystem # 挂载对象存储的格式，本示例填写 Filesystem，表示文件系统挂载。
  volumeName: local-pv-node1  # 绑定到该 PVC 的 PV 名称。

---

apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql
  namespace: airflow
spec:
  serviceName: "postgresql"
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
    spec:
      securityContext:
        runAsUser: 999  # 设置运行用户 ID
        fsGroup: 999    # 设置文件系统组 ID
      tolerations:
      - key: node
        operator: Equal
        value: gpu
        effect: NoSchedule
      nodeSelector:
        node-type: database
      imagePullSecrets:
      - name: docker-registry-secret
      containers:
        - name: postgresql
          image: registry.eqfleetcmder.com/eq/postgresql:13
          env:
            - name: POSTGRES_USER
              value: "airflow"
            - name: POSTGRES_PASSWORD
              value: "airflow_123456"
            - name: POSTGRES_DB
              value: "airflow"
          args: ["postgres", "-c", "max_connections=1000", "-c", "shared_buffers=4000MB"]
          # args: ["/bin/bash", "-c", "sleep inf"]

          ports:
            - containerPort: 5432
              name: postgresql
          volumeMounts:
            - name: postgresql-persistent-storage
              mountPath: /var/lib/postgresql/data
            - name: pvc-tos
              mountPath: /mnt/tos
          resources:
            requests:
              memory: "16Gi"
              cpu: "1"
            limits:
              memory: "32Gi"
              cpu: "8"
      volumes:
        - name: postgresql-persistent-storage
          persistentVolumeClaim:
            claimName: node1-pvc
        - name: pvc-tos
          persistentVolumeClaim:
            claimName: airflow-pvc

---

apiVersion: v1
kind: Service
metadata:
  name: postgresql
  namespace: airflow
spec:
  type: NodePort
  ports:
    - port: 5432
      targetPort: 5432
      nodePort: 31112
      protocol: TCP
  selector:
    app: postgresql
