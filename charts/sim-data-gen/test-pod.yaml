apiVersion: v1
kind: Pod
metadata:
  name: test-pod-1
  namespace: simulation
  labels:
    app: test
spec:
  containers:
  - name: test-pod
    image: registry.eqfleetcmder.com/eq/airflow:busybox
    imagePullPolicy: Always
    command: ["sh", "-c", "sleep inf"]
    volumeMounts:
    - name: airflow-common-path
      mountPath: /data
    resources:
      requests:
        cpu: "10"
        memory: "100Gi"
      limits:
        cpu: "50"
        memory: "500Gi"
  restartPolicy: Always
  nodeSelector:
    node-type: database
  imagePullSecrets:
  - name: docker-registry-secret
  volumes:
  - name: airflow-common-path
    persistentVolumeClaim:
      claimName: data-gen-pvc
